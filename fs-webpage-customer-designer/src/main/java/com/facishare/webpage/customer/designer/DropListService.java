package com.facishare.webpage.customer.designer;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.core.language.ComponentLanguageService;
import com.facishare.webpage.customer.core.model.ComponentDto;
import com.facishare.webpage.customer.core.model.Widget;
import com.facishare.webpage.customer.designer.model.DesignerAuthInfo;
import com.facishare.webpage.customer.designer.model.DropListType;
import com.google.common.base.Strings;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Yu
 */
public abstract class DropListService<A, R> implements BaseDropList<A, R> {

    @Resource
    private ComponentLanguageService componentLanguageService;

    /**
     * 获取设计器的类型
     *
     * @return
     */
    public abstract DropListType getDropListType();

    /**
     * 获取设计器数据
     *
     * @param arg
     * @return
     */
    protected abstract R doGetDropList(A arg, DesignerAuthInfo designerAuthInfo);

    @Override
    public R getDropList(A arg, DesignerAuthInfo designerAuthInfo) {
        try {
            before(arg);
            R result = doGetDropList(arg, designerAuthInfo);
            result = after(arg, result);
            return result;
        } finally {
            finallyDo();
        }
    }

    /**
     * 前置处理
     *
     * @param arg
     */
    protected void before(A arg) {
        if (Objects.isNull(arg)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }

    /**
     * 后置处理
     *
     * @param arg
     * @param result
     * @return
     */
    protected R after(A arg, R result) {
        return result;
    }

    /**
     * 执行结束处理
     */
    protected void finallyDo() {

    }

    protected void setComponentDtoLanguage(int tenantId, List<ComponentDto> componentDtos, Locale locale) {
        if (CollectionUtils.isEmpty(componentDtos)) {
            return;
        }
        Map<String, String> languageMap = componentLanguageService.queryComponentLanguage(tenantId, componentDtos, locale);
        componentDtos.stream().forEach(componentDto -> {
            if (componentDto.getTitleI18nKey() != null) {
                String title = languageMap.get(componentDto.getTitleI18nKey());
                if (!Strings.isNullOrEmpty(title)) {
                    componentDto.setTitle(title);
                }
            }
            if (componentDto.getMenus() != null) {
                componentDto.getMenus().stream().forEach(menu -> {
                    if (menu.getNameI18nKey() != null) {
                        String name = languageMap.get(menu.getNameI18nKey());
                        if (!Strings.isNullOrEmpty(name)) {
                            menu.setName(name);
                        }
                    }
                });
            }
            Widget widget = componentDto.getWidget();
            if (widget != null && widget.getNameI18nKey() != null) {
                String name = languageMap.get(widget.getNameI18nKey());
                if (!Strings.isNullOrEmpty(name)) {
                    widget.setName(name);
                }
            }
        });
    }

    protected abstract String getBizId(A arg);
}
