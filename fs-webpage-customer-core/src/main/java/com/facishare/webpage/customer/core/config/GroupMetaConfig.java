package com.facishare.webpage.customer.core.config;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.core.model.GroupMetaData;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeListener;
import com.github.autoconf.api.IConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2021/1/8
 */
public class GroupMetaConfig {

    private static Logger logger = LoggerFactory.getLogger(GroupMetaConfig.class);

    private Map<String, List<GroupMetaData>> groupMetaMap = Maps.newHashMap();

    public void init() {
        IChangeListener listener = config -> loadConfig();
        ConfigFactory.getConfig("fs-webpage-customer-group", listener, true);
    }

    private void loadConfig() {

        IConfig config = ConfigFactory.getConfig("fs-webpage-customer-group");
        List<GroupMetaData> groupMetaDataList = JSONObject.parseArray(config.getString(), GroupMetaData.class);
        Map<String, List<GroupMetaData>> groupMetaDataMap = Maps.newHashMap();
        groupMetaDataList.stream().forEach(x -> {
            if (StringUtils.isEmpty(x.getAppId())){
                x.setAppId("CRM");
            }
            List<GroupMetaData> metaDataList = groupMetaDataMap.getOrDefault(x.getAppId(), Lists.newArrayList());
            metaDataList.add(x);
            groupMetaDataMap.put(x.getAppId(), metaDataList);
        });
        groupMetaMap = groupMetaDataMap;
        logger.info("init GroupMetaConfig config groupMetaMap:{};", groupMetaMap);
    }

    public List<GroupMetaData> getGroupMetaDataListByAppId(String appId) {
        List<GroupMetaData> groupMetaDataList = groupMetaMap.getOrDefault(appId, Lists.newArrayList());
        return groupMetaDataList.stream().map(x -> GroupMetaData.builder().
                groupName(x.getGroupName()).
                groupApiName(x.getGroupApiName()).
                afterAppId(x.getAfterAppId()).
                afterType(x.getAfterType()).
                afterApiName(x.getAfterApiName()).
                menuAppId(x.getMenuAppId()).
                menus(x.getMenus()).build()).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static void main(String[] args) {
        System.setProperty("process.profile", "fstest");
        GroupMetaConfig groupMetaConfig = new GroupMetaConfig();
        groupMetaConfig.init();
        List<GroupMetaData> groupMetaDataList = groupMetaConfig.getGroupMetaDataListByAppId("CRM");
        System.out.println(groupMetaDataList);
    }

}
