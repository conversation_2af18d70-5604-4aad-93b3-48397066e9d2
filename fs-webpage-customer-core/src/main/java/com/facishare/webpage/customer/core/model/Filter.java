package com.facishare.webpage.customer.core.model;

import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.webpage.customer.core.util.CollectionUtils;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class Filter extends DocumentBasedBean {

    private Filter(Map filter) {
        super(filter);
    }

    public static Filter of(Map filter) {
        return new Filter(filter);
    }

    public static Filter of(IFilter filter) {
        Filter result = new Filter(Maps.newHashMap());
        result.fromJsonString(filter.toJsonString());
        return result;
    }

    public static <T extends Map> List<Filter> ofList(List<T> filters) {
        if (CollectionUtils.empty(filters)) {
            return Collections.emptyList();
        }
        return filters.stream().map(Filter::new).collect(Collectors.toList());
    }

    public Integer getValueType() {
        Object valueType = get("value_type");
        if (valueType instanceof Number){
            return ((Number) valueType).intValue();
        }
        return null;
    }

    public Comparator getOperator() {
        return Comparator.valueOf(get("operator", String.class));
    }

    public List<String> getFieldValues() {
        return get("field_values", List.class);
    }

    public String getFieldName() {
        return get("field_name", String.class);
    }


    public void setFiledName(String filedName) {
        set("field_name", filedName);
    }
}
