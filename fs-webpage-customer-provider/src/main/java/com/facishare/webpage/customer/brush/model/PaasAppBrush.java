package com.facishare.webpage.customer.brush.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface PaasAppBrush {

    @Data
    class Arg implements Serializable {
        private List<Integer> tenantIds;
    }

    @Data
    class Result implements Serializable {
        private List<ResultData> resultDataList;
    }

    @Data
    class ResultData implements Serializable {
        private Integer tenantId;
        private Integer count;
    }

}
