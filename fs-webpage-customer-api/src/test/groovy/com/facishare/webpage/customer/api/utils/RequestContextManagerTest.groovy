package com.facishare.webpage.customer.api.utils

import com.facishare.webpage.customer.api.model.User
import spock.lang.Specification

import java.util.concurrent.CountDownLatch
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicReference

class RequestContextManagerTest extends Specification {

    def setup() {
        // 每个测试前清除上下文
        RequestContextManager.removeContext()
    }

    def cleanup() {
        // 每个测试后清除上下文
        RequestContextManager.removeContext()
    }

    def "initContextForIsFromManage应正确设置isFromManage标志"() {
        given: "传入isFromManage参数"
        boolean isFromManage = true

        when: "调用initContextForIsFromManage方法"
        RequestContextManager.initContextForIsFromManage(isFromManage)
        def context = RequestContextManager.getContext()

        then: "验证isFromManage标志已正确设置"
        context != null
        context.isFromManage() == isFromManage
    }

    def "initContext应正确设置isFromManage和isFromRest标志"() {
        given: "传入isFromManage和isFromRest参数"
        boolean isFromManage = true
        boolean isFromRest = false

        when: "调用initContext方法"
        RequestContextManager.initContext(isFromManage, isFromRest)
        def context = RequestContextManager.getContext()

        then: "验证标志已正确设置"
        context != null
        context.isFromManage() == isFromManage
        context.isFromRest() == isFromRest
    }

    def "initContextWithUser应正确设置用户信息"() {
        given: "创建一个User对象"
        User user = User.builder()
                .tenantId(10001)
                .userId(20001)
                .build()

        when: "调用initContextWithUser方法"
        RequestContextManager.initContextWithUser(user)
        def context = RequestContextManager.getContext()

        then: "验证用户信息已正确设置"
        context != null
        context.getUser() == user
        context.getUser().getTenantId() == 10001
        context.getUser().getUserId() == 20001
    }

    def "initContext无参方法应创建默认的RequestContext"() {
        when: "调用无参数的initContext方法"
        RequestContextManager.initContext()
        def context = RequestContextManager.getContext()

        then: "验证已创建默认的RequestContext"
        context != null
        !context.isFromManage()
        !context.isFromRest()
        !context.isIncomplete()
        context.getUser() != null
    }

    def "initContextForIsFromRest应正确设置isFromRest标志"() {
        given: "传入isFromRest参数"
        boolean isFromRest = true

        when: "调用initContextForIsFromRest方法"
        RequestContextManager.initContextForIsFromRest(isFromRest)
        def context = RequestContextManager.getContext()

        then: "验证isFromRest标志已正确设置"
        context != null
        context.isFromRest() == isFromRest
    }

    def "initContextForIsIncomplete应正确设置isIncomplete标志"() {
        given: "传入isIncomplete参数"
        boolean isIncomplete = true

        when: "调用initContextForIsIncomplete方法"
        RequestContextManager.initContextForIsIncomplete(isIncomplete)
        def context = RequestContextManager.getContext()

        then: "验证isIncomplete标志已正确设置"
        context != null
        context.isIncomplete() == isIncomplete
    }

    def "isFromManager应正确返回上下文中的isFromManage标志"() {
        given: "设置带有不同isFromManage值的上下文"
        RequestContextManager.initContextForIsFromManage(isFromManage)

        expect: "isFromManager方法返回正确的值"
        RequestContextManager.isFromManager() == expected

        where: "不同的测试数据"
        isFromManage | expected
        true         | true
        false        | false
    }

    def "isFromRest应正确返回上下文中的isFromRest标志"() {
        given: "设置带有不同isFromRest值的上下文"
        RequestContextManager.initContextForIsFromRest(isFromRest)

        expect: "isFromRest方法返回正确的值"
        RequestContextManager.isFromRest() == expected

        where: "不同的测试数据"
        isFromRest   | expected
        true         | true
        false        | false
    }

    def "isIncomplete应正确返回上下文中的isIncomplete标志"() {
        given: "设置带有不同isIncomplete值的上下文"
        RequestContextManager.initContextForIsIncomplete(isIncomplete)

        expect: "isIncomplete方法返回正确的值"
        RequestContextManager.isIncomplete() == expected

        where: "不同的测试数据"
        isIncomplete | expected
        true         | true
        false        | false
    }

    def "getUser应返回上下文中的用户信息或默认用户"() {
        when: "上下文为空时调用getUser"
        RequestContextManager.removeContext()
        def defaultUser = RequestContextManager.getUser()

        then: "返回默认的User对象"
        defaultUser != null
        defaultUser instanceof User

        when: "设置上下文中的用户信息后调用getUser"
        User customUser = User.builder()
                .tenantId(10002)
                .userId(20002)
                .build()
        RequestContextManager.initContextWithUser(customUser)
        def retrievedUser = RequestContextManager.getUser()

        then: "返回设置的用户信息"
        retrievedUser == customUser
        retrievedUser.getTenantId() == 10002
        retrievedUser.getUserId() == 20002
    }

    def "setContext应正确设置RequestContext"() {
        given: "创建一个RequestContext对象"
        def context = RequestContext.builder()
                .isFromManage(true)
                .isFromRest(true)
                .isIncomplete(true)
                .user(User.builder().tenantId(10003).userId(20003).build())
                .build()

        when: "调用setContext方法"
        RequestContextManager.setContext(context)
        def retrievedContext = RequestContextManager.getContext()

        then: "验证上下文已正确设置"
        retrievedContext == context
        retrievedContext.isFromManage()
        retrievedContext.isFromRest()
        retrievedContext.isIncomplete()
        retrievedContext.getUser().getTenantId() == 10003
        retrievedContext.getUser().getUserId() == 20003
    }

    def "setContext传入null时不应改变当前上下文"() {
        given: "先设置一个有效的上下文"
        def context = RequestContext.builder()
                .isFromManage(true)
                .build()
        RequestContextManager.setContext(context)

        when: "调用setContext方法并传入null"
        RequestContextManager.setContext(null)
        def retrievedContext = RequestContextManager.getContext()

        then: "上下文保持不变"
        retrievedContext == context
    }

    def "removeContext应正确清除ThreadLocal中的上下文"() {
        given: "先设置一个上下文"
        RequestContextManager.initContext()
        def contextBeforeRemove = RequestContextManager.getContext()

        when: "调用removeContext方法"
        RequestContextManager.removeContext()
        def contextAfterRemove = RequestContextManager.getContext()

        then: "上下文被成功清除"
        contextBeforeRemove != null
        contextAfterRemove == null
    }
    
    // 多线程环境下的测试
    def "多线程环境下ThreadLocal应当有效隔离各线程的上下文"() {
        given: "准备多线程测试环境"
        int threadCount = 5
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount)
        CountDownLatch startLatch = new CountDownLatch(1)
        CountDownLatch finishLatch = new CountDownLatch(threadCount)
        Map<Integer, AtomicReference<User>> threadUsers = [:]
        Map<Integer, AtomicReference<Boolean>> threadResults = [:]
        
        when: "多个线程同时设置和获取各自的上下文"
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i
            threadUsers[threadId] = new AtomicReference<>()
            threadResults[threadId] = new AtomicReference<>(false)
            
            executorService.submit({
                try {
                    // 创建特定于线程的用户
                    User threadUser = User.builder()
                            .tenantId(10000 + threadId)
                            .userId(20000 + threadId)
                            .build()
                            
                    // 等待所有线程准备就绪
                    startLatch.await()
                    
                    // 设置该线程的上下文
                    RequestContextManager.initContextWithUser(threadUser)
                    RequestContextManager.initContextForIsFromManage(threadId % 2 == 0)
                    
                    // 模拟业务处理延迟，增加线程交叉可能性
                    Thread.sleep(10)
                    
                    // 获取上下文并验证是否仍然是该线程设置的值
                    User retrievedUser = RequestContextManager.getUser()
                    boolean isCorrectUser = retrievedUser.getTenantId() == threadUser.getTenantId() && 
                                           retrievedUser.getUserId() == threadUser.getUserId()
                    boolean isCorrectFlag = RequestContextManager.isFromManager() == (threadId % 2 == 0)
                    
                    // 保存结果供验证
                    threadUsers[threadId].set(retrievedUser)
                    threadResults[threadId].set(isCorrectUser && isCorrectFlag)
                } finally {
                    // 清理线程上下文并通知测试线程完成
                    RequestContextManager.removeContext()
                    finishLatch.countDown()
                }
            })
        }
        
        // 启动所有线程
        startLatch.countDown()
        
        // 等待所有线程完成
        finishLatch.await(1, TimeUnit.SECONDS)
        executorService.shutdown()
        
        then: "每个线程应当获取到其自身设置的上下文"
        threadResults.every { threadId, result -> result.get() }
        
        and: "线程之间的上下文不应混淆"
        threadUsers.size() == threadCount
        for (int i = 0; i < threadCount; i++) {
            threadUsers[i].get().getTenantId() == 10000 + i
            threadUsers[i].get().getUserId() == 20000 + i
        }
    }
    
    // 方法调用组合测试
    def "多个初始化方法按不同顺序连续调用时应当以最后调用的为准"() {
        when: "先设置isFromManage，再设置isFromRest"
        RequestContextManager.initContextForIsFromManage(true)
        RequestContextManager.initContextForIsFromRest(true)
        def context1 = RequestContextManager.getContext()
        
        then: "两个标志都应被设置"
        context1.isFromManage()
        context1.isFromRest()
        
        when: "再次设置isFromManage为false"
        RequestContextManager.initContextForIsFromManage(false)
        def context2 = RequestContextManager.getContext()
        
        then: "isFromManage应被更新为false，而isFromRest保持不变"
        !context2.isFromManage()
        context2.isFromRest()
        
        when: "使用initContext方法同时设置两个标志"
        RequestContextManager.initContext(true, false)
        def context3 = RequestContextManager.getContext()
        
        then: "两个标志都应被更新为新值"
        context3.isFromManage()
        !context3.isFromRest()
    }
    
    def "多次调用同一个初始化方法应当以最后一次调用的值为准"() {
        when: "连续多次调用initContextForIsFromManage方法"
        RequestContextManager.initContextForIsFromManage(true)
        RequestContextManager.initContextForIsFromManage(false)
        RequestContextManager.initContextForIsFromManage(true)
        
        then: "isFromManage应该是最后一次调用的值"
        RequestContextManager.isFromManager()
    }
    
    // null值处理测试
    def "initContextWithUser传入null用户时应创建默认User对象"() {
        when: "调用initContextWithUser并传入null"
        RequestContextManager.initContextWithUser(null)
        def context = RequestContextManager.getContext()
        
        then: "应创建默认的User对象"
        context != null
        context.getUser() != null
        context.getUser() instanceof User
        
        // 默认User应该是空属性
        context.getUser().getTenantId() == 0
        context.getUser().getUserId() == 0
    }
    
    def "上下文为null时调用状态方法应返回默认值false"() {
        when: "确保上下文为null"
        RequestContextManager.removeContext()
        
        then: "状态方法应返回默认值false"
        !RequestContextManager.isFromManager()
        !RequestContextManager.isFromRest()
        !RequestContextManager.isIncomplete()
    }
    
    // 特殊用户处理
    def "使用系统用户ID创建的User应被正确处理"() {
        given: "创建具有系统用户ID的User"
        User systemUser = User.builder()
                .userId(Integer.parseInt(User.SYSTEM_USER_ID))
                .build()
                
        when: "设置系统用户并获取"
        RequestContextManager.initContextWithUser(systemUser)
        def retrievedUser = RequestContextManager.getUser()
        
        then: "应正确设置和获取系统用户"
        retrievedUser != null
        retrievedUser.getUserId() == Integer.parseInt(User.SYSTEM_USER_ID)
    }
    
    def "用户只设置部分属性时应正确处理"() {
        given: "创建只设置部分属性的用户"
        User partialUser = new User()
        partialUser.setTenantId(10005)
        // 不设置userId
        
        when: "设置并获取部分属性用户"
        RequestContextManager.initContextWithUser(partialUser)
        def retrievedUser = RequestContextManager.getUser()
        
        then: "应正确设置和获取部分属性"
        retrievedUser != null
        retrievedUser.getTenantId() == 10005
        retrievedUser.getUserId() == 0  // 未设置的数值型属性默认为0
    }
    
    // ThreadLocal资源管理测试
    def "重复清理上下文应正常工作且不抛出异常"() {
        given: "设置一个上下文"
        RequestContextManager.initContext()
        
        when: "连续多次调用removeContext"
        RequestContextManager.removeContext()
        RequestContextManager.removeContext()
        RequestContextManager.removeContext()
        
        then: "不应抛出异常，且上下文应保持为null"
        RequestContextManager.getContext() == null
    }
    
    def "上下文初始化、获取、清理的完整生命周期测试"() {
        when: "初始化上下文"
        RequestContextManager.initContext(true, true)
        RequestContextManager.initContextWithUser(User.builder().tenantId(10006).userId(20006).build())
        RequestContextManager.initContextForIsIncomplete(true)
        
        then: "所有属性应被正确设置"
        def context = RequestContextManager.getContext()
        context != null
        context.isFromManage()
        context.isFromRest()
        context.isIncomplete()
        context.getUser().getTenantId() == 10006
        context.getUser().getUserId() == 20006
        
        when: "清理上下文"
        RequestContextManager.removeContext()
        
        then: "上下文应为null"
        RequestContextManager.getContext() == null
        
        when: "再次初始化"
        RequestContextManager.initContext()
        
        then: "应创建新的默认上下文"
        def newContext = RequestContextManager.getContext()
        newContext != null
        !newContext.isFromManage()
        !newContext.isFromRest()
        !newContext.isIncomplete()
    }

    // 内存泄漏风险测试 - 线程池中线程重用导致的风险
    def "线程池中重用线程时应正确隔离每次任务的上下文"() {
        given: "创建一个固定大小为1的线程池，保证线程会被重用"
        ExecutorService singleThreadExecutor = Executors.newFixedThreadPool(1)
        CountDownLatch task1Latch = new CountDownLatch(1)
        CountDownLatch task2Latch = new CountDownLatch(1)
        AtomicReference<User> task1User = new AtomicReference<>()
        AtomicReference<User> task2User = new AtomicReference<>()
        AtomicReference<Boolean> task2Result = new AtomicReference<>(false)
        
        when: "第一个任务设置上下文但不清理"
        singleThreadExecutor.submit({
            try {
                // 第一个任务设置上下文
                User user1 = User.builder()
                        .tenantId(10010)
                        .userId(20010)
                        .build()
                RequestContextManager.initContextWithUser(user1)
                task1User.set(RequestContextManager.getUser())
                
                // 通知第一个任务已完成，但不清理上下文
                task1Latch.countDown()
            } catch (Exception e) {
                e.printStackTrace()
            }
        })
        
        // 等待第一个任务完成
        task1Latch.await(500, TimeUnit.MILLISECONDS)
        
        and: "第二个任务检查并设置自己的上下文"
        singleThreadExecutor.submit({
            try {
                // 检查是否存在上一个任务的上下文残留
                User previousUser = RequestContextManager.getUser()
                
                // 设置新的上下文
                User user2 = User.builder()
                        .tenantId(10020)
                        .userId(20020)
                        .build()
                RequestContextManager.initContextWithUser(user2)
                task2User.set(RequestContextManager.getUser())
                
                // 记录是否存在前一个任务的上下文残留
                task2Result.set(previousUser == null || 
                               (previousUser.getTenantId() != 10010 && 
                                previousUser.getUserId() != 20010))
                
                // 这次正确清理上下文
                RequestContextManager.removeContext()
                task2Latch.countDown()
            } catch (Exception e) {
                e.printStackTrace()
            }
        })
        
        // 等待第二个任务完成
        task2Latch.await(500, TimeUnit.MILLISECONDS)
        singleThreadExecutor.shutdown()
        
        then: "两个任务应该使用各自独立的上下文"
        task1User.get().getTenantId() == 10010
        task1User.get().getUserId() == 20010
        task2User.get().getTenantId() == 10020
        task2User.get().getUserId() == 20020
        
        and: "第二个任务不应该看到第一个任务的上下文残留(由于ThreadLocal的特性，这个测试可能不总是成功，取决于JVM的实现)"
        task2Result.get()
    }
    
    // 深层对象修改风险测试
    def "获取User对象后外部修改不应影响上下文状态"() {
        given: "初始化上下文并获取User对象"
        User originalUser = User.builder()
                .tenantId(10030)
                .userId(20030)
                .build()
        RequestContextManager.initContextWithUser(originalUser)
        User retrievedUser = RequestContextManager.getUser()
        
        when: "修改获取到的User对象"
        retrievedUser.setTenantId(10031)
        retrievedUser.setUserId(20031)
        
        and: "再次从上下文获取User对象"
        User afterModifyUser = RequestContextManager.getUser()
        
        then: "上下文中的User对象应该也被修改(因为Java对象引用特性)"
        afterModifyUser.getTenantId() == 10031
        afterModifyUser.getUserId() == 20031
        
        and: "原始对象也应被修改(这表明存在深层对象修改风险)"
        originalUser.getTenantId() == 10031
        originalUser.getUserId() == 20031
    }
    
    def "应该克隆User对象避免外部修改风险"() {
        given: "创建一个改进的包装用户类模拟测试"


        and: "使用包装类创建安全用户"
        User originalUser = User.builder()
                .tenantId(10040)
                .userId(20040)
                .build()
        SafeUserWrapper wrapper = new SafeUserWrapper(originalUser)
        
        when: "获取并修改用户对象"
        User retrieved = wrapper.getUser()
        retrieved.setTenantId(10041)
        retrieved.setUserId(20041)
        
        and: "再次获取用户对象"
        User afterModify = wrapper.getUser()
        
        then: "内部用户对象应保持不变"
        afterModify.getTenantId() == 10040
        afterModify.getUserId() == 20040
    }
    
    // 异常导致的上下文泄露测试
    def "异常发生时应保证上下文被清理"() {
        given: "模拟一个业务方法，在try-finally中使用上下文"
        def businessMethod = { boolean throwException ->
            try {
                RequestContextManager.initContext(true, true)
                
                if (throwException) {
                    throw new RuntimeException("测试异常")
                }
                return true
            } finally {
                RequestContextManager.removeContext()
            }
        }
        
        when: "正常执行业务方法"
        businessMethod(false)
        def contextAfterNormal = RequestContextManager.getContext()
        
        then: "上下文应被清理"
        contextAfterNormal == null
        
        when: "异常执行业务方法"
        boolean exceptionCaught = false
        try {
            businessMethod(true)
        } catch (RuntimeException e) {
            exceptionCaught = true
        }
        def contextAfterException = RequestContextManager.getContext()
        
        then: "异常应被捕获且上下文应被清理"
        exceptionCaught
        contextAfterException == null
    }
    
    def "不当的异常处理可能导致上下文泄露"() {
        given: "模拟一个有缺陷的业务方法，在try中使用上下文但没在finally中清理"
        def badBusinessMethod = { boolean throwException ->
            try {
                RequestContextManager.initContext(true, true)
                
                if (throwException) {
                    throw new RuntimeException("测试异常")
                }
                
                // 只在正常路径清理上下文，这是有缺陷的
                RequestContextManager.removeContext()
                return true
            } catch (Exception e) {
                // 捕获异常但不清理上下文，这会导致泄露
                return false
            }
        }
        
        when: "正常执行有缺陷的业务方法"
        badBusinessMethod(false)
        def contextAfterNormal = RequestContextManager.getContext()
        
        then: "上下文应被清理"
        contextAfterNormal == null
        
        when: "异常执行有缺陷的业务方法"
        badBusinessMethod(true)
        def contextAfterException = RequestContextManager.getContext()
        
        then: "上下文未被清理，导致泄露"
        contextAfterException != null
        contextAfterException.isFromManage()
        contextAfterException.isFromRest()
        
        cleanup: "手动清理泄露的上下文"
        RequestContextManager.removeContext()
    }
    
    // 初始化时序问题测试
    def "依赖上下文的组件在上下文初始化前使用应有合理行为"() {
        given: "模拟一个依赖上下文的组件"

        def component = new ContextDependentComponent()
        
        when: "在上下文初始化前使用组件"
        boolean isFromManage = component.isUserFromManagement()
        User user = component.getCurrentUser()
        
        then: "应返回合理的默认值而不是抛出异常"
        !isFromManage  // 默认为false
        user != null   // 默认返回空User而不是null
        user.getTenantId() == 0
        user.getUserId() == 0
        
        when: "初始化上下文后再使用"
        RequestContextManager.initContextForIsFromManage(true)
        RequestContextManager.initContextWithUser(User.builder().tenantId(10050).userId(20050).build())
        isFromManage = component.isUserFromManagement()
        user = component.getCurrentUser()
        
        then: "应返回正确的值"
        isFromManage
        user.getTenantId() == 10050
        user.getUserId() == 20050
    }
    
    // 父子线程上下文传递问题测试
    def "父线程的上下文不会自动传递给子线程"() {
        given: "在父线程中设置上下文"
        RequestContextManager.initContextWithUser(User.builder().tenantId(10060).userId(20060).build())
        RequestContextManager.initContextForIsFromManage(true)
        
        AtomicReference<User> childThreadUser = new AtomicReference<>()
        AtomicReference<Boolean> childThreadIsFromManage = new AtomicReference<>()
        CountDownLatch childThreadLatch = new CountDownLatch(1)
        
        when: "创建子线程并检查上下文"
        Thread childThread = new Thread({
            try {
                // 检查子线程是否继承了父线程的上下文
                childThreadUser.set(RequestContextManager.getUser())
                childThreadIsFromManage.set(RequestContextManager.isFromManager())
            } finally {
                childThreadLatch.countDown()
            }
        })
        childThread.start()
        childThreadLatch.await(500, TimeUnit.MILLISECONDS)
        
        then: "子线程应该没有继承父线程的上下文"
        User parentUser = RequestContextManager.getUser()
        parentUser.getTenantId() == 10060
        parentUser.getUserId() == 20060
        RequestContextManager.isFromManager()
        
        and: "子线程的上下文为null或默认值"
        User childUser = childThreadUser.get()
        if (childUser) {
            // 如果不为null，应为默认User
            childUser.getTenantId() == 0
            childUser.getUserId() == 0
        }
        !childThreadIsFromManage.get()
    }
    
    // 显式传递上下文给子线程的模拟测试
    def "应当能显式传递上下文给子线程"() {
        given: "在父线程中设置上下文"
        User parentUser = User.builder().tenantId(10070).userId(20070).build()
        RequestContextManager.initContextWithUser(parentUser)
        RequestContextManager.initContextForIsFromManage(true)
        
        // 保存父线程上下文
        RequestContext parentContext = RequestContextManager.getContext()
        
        AtomicReference<User> childThreadUser = new AtomicReference<>()
        AtomicReference<Boolean> childThreadIsFromManage = new AtomicReference<>()
        CountDownLatch childThreadLatch = new CountDownLatch(1)
        
        when: "创建子线程并显式传递上下文"
        Thread childThread = new Thread({
            try {
                // 显式设置从父线程传递的上下文
                RequestContextManager.setContext(parentContext)
                
                // 检查子线程中的上下文
                childThreadUser.set(RequestContextManager.getUser())
                childThreadIsFromManage.set(RequestContextManager.isFromManager())
            } finally {
                RequestContextManager.removeContext()
                childThreadLatch.countDown()
            }
        })
        childThread.start()
        childThreadLatch.await(500, TimeUnit.MILLISECONDS)
        
        then: "子线程应该成功接收父线程的上下文"
        childThreadUser.get().getTenantId() == 10070
        childThreadUser.get().getUserId() == 20070
        childThreadIsFromManage.get()
    }

    class SafeUserWrapper {
        private User user

        SafeUserWrapper(User user) {
            // 深度复制用户对象
            this.user = User.builder()
                    .tenantId(user.getTenantId())
                    .userId(user.getUserId())
                    .build()
        }

        User getUser() {
            // 返回用户对象的副本
            return User.builder()
                    .tenantId(user.getTenantId())
                    .userId(user.getUserId())
                    .build()
        }
    }

    class ContextDependentComponent {
        boolean isUserFromManagement() {
            return RequestContextManager.isFromManager()
        }

        User getCurrentUser() {
            return RequestContextManager.getUser()
        }
    }
}