package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.cms.*;
import com.facishare.webpage.customer.controller.model.result.cms.ConvertResourceResult;
import com.facishare.webpage.customer.controller.model.result.cms.OperationResult;
import com.facishare.webpage.customer.controller.model.result.cms.ResourceListResult;
import com.facishare.webpage.customer.controller.model.result.cms.ValidateFileStatusResult;

/**
 * CMS资源管理接口
 */
public interface CmsResourceAction {

    /**
     * 根据工作区apiname查询文件列表
     */
    ResourceListResult getListByWorkSpace(UserInfo userInfo, ClientInfo clientInfo, GetResourceListArg arg);

    /**
     * 批量保存文件信息
     */
    OperationResult batchSaveFileInfo(UserInfo userInfo, ClientInfo clientInfo, SaveFileInfoArg args);

    /**
     * 批量删除
     */
    OperationResult batchDelete(UserInfo userInfo, ClientInfo clientInfo, DeleteResourceArg args);

    /**
     * 移动文件
     */
    OperationResult move(UserInfo userInfo, ClientInfo clientInfo, MoveResourceArg args);

    /**
     * 编辑文件或文件夹
     */
    OperationResult update(UserInfo userInfo, ClientInfo clientInfo, UpdateResourceArg arg);

    /**
     * 启用
     */
    OperationResult enable(UserInfo userInfo, ClientInfo clientInfo, EnableResourceArg args);

    /**
     * 禁用
     */
    OperationResult disable(UserInfo userInfo, ClientInfo clientInfo, DisableResourceArg args);

    ConvertResourceResult convertResourceByApiName(UserInfo userInfo,
                                                   ClientInfo clientInfo,
                                                   OuterUserInfo outerUserInfo,
                                                   ConvertResourceArg arg);

    ValidateFileStatusResult validateFileStatus(UserInfo userInfo,
                                                ClientInfo clientInfo,
                                                OuterUserInfo outerUserInfo,
                                                ValidateFileStatusArg arg);
}
