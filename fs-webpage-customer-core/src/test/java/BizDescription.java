import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class BizDescription implements Serializable {
    /**
     * 唯一id
     */
    private String id;

    /**
     * 业务Id
     */
    private String bizId;

    /**
     * 业务类型(暂时只有 应用 )
     * @see
     */
    private String category;

    /**
     * 名字
     */
    private String name;

    /**
     * 应用图标
     */
    private Icon icon;

    /**
     * 点击事件
     */
    private Action action;

    /**
     * 作为菜单的数据
     */
    private MainTab mainTab;

    /**
     * 计算飘数来源类型
     */
    private int countSourceType;

    /**
     * 计算飘数来源数据
     */
    private JSONObject countSourceData;

    @NoArgsConstructor
    @Data
    public static class Icon implements Serializable {
        private String path;
    }

    @NoArgsConstructor
    @Data
    public static class Action implements Serializable {
        private String url;
        private String ios;
        private String android;
    }

    @NoArgsConstructor
    @Data
    public static class MainTab implements Serializable {
        /**
         * 能否作为菜单
         */
        private boolean isMainTabCompatible;

        /**
         * 选为菜单后是否需要隐藏应用列表里的此应用
         */
        private boolean hideInAppsCenter;

        /**
         * 菜单上的action
         */
        private Page page;

        /**
         * 菜单选中和未选中的图标
         */
        private IconX icon;

        /**
         * 670前的图标
         */
        private IconX iconBefore670;

        @NoArgsConstructor
        @Data
        public static class Page implements Serializable {
            private String url;
            private String ios;
            private String android;
        }

        @NoArgsConstructor
        @Data
        public static class IconX implements Serializable {
            private String normal;
            private String highlight;
        }
    }

}