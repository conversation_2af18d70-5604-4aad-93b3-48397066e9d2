package com.facishare.webpage.customer.aop;

import com.facishare.webpage.customer.api.exception.WebPageException;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/9
 */
@Aspect
public class WebPageServiceAspect {

    private static Logger logger = LoggerFactory.getLogger(WebPageServiceAspect.class);

    @Before(value = "execution(* com.facishare.webpage.customer.facade..*.*(..))")
    public void beforeCall(JoinPoint joinPoint) {
        logger.info("before WebPageService method:{} Args:{}", joinPoint.getSignature().toShortString(), joinPoint.getArgs());
    }

    @AfterReturning(value = "execution(* com.facishare.webpage.customer.facade..*.*(..))", returning = "ret")
    public void afterReturn(JoinPoint joinPoint, Object ret) {
        logger.info("afterReturn, WebPageService:{} arg:{} Return:{}",
                joinPoint.getSignature().toShortString(), joinPoint.getArgs(), ret);
    }

    @AfterThrowing(value = "execution(* com.facishare.webpage.customer.facade..*.*(..))", throwing = "e")
    public void afterThrowing(JoinPoint joinPoint, Throwable e) throws Throwable {
        if (e instanceof WebPageException) {
            logger.warn("afterThrowing, WebPageService:{} ", joinPoint.getSignature().toShortString(), e);
        } else {
            logger.error("afterThrowing, WebPageService:{} ", joinPoint.getSignature().toShortString(), e);
            throw e;
        }
    }

}
