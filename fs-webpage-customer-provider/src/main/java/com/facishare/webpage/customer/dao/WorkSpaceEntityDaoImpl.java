package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.api.constant.CmsStatus;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.dao.entity.WorkSpaceEntity;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 工作区实体DAO实现类
 */
@Setter
public class WorkSpaceEntityDaoImpl implements WorkSpaceEntityDao {

    @Resource
    private Datastore datastore;

    @PostConstruct
    public void init() {
        datastore.ensureIndexes(WorkSpaceEntity.class, true);
    }

    @Override
    public List<WorkSpaceEntity> findByCondition(WorkSpaceEntity condition, int offset, int limit) {
        Query<WorkSpaceEntity> query = datastore.createQuery(WorkSpaceEntity.class);
        query.field("tenantId").equal(condition.getTenantId());
        if (StringUtils.isNotBlank(condition.getName())) {
            query.field("name").containsIgnoreCase(condition.getName());
        }
        if (Objects.nonNull(condition.getStatus())) {
            query.field("status").equal(condition.getStatus());
        } else {
            query.field("status").notEqual(CmsStatus.DELETE);
        }
        if (CollectionUtils.isNotEmpty(condition.getChannelList())) {
            query.field("channelList").in(condition.getChannelList());
        }
        query.order("-createTime");
        query.offset(offset);
        query.limit(limit);
        return query.asList();
    }

    @Override
    public long getCountByCondition(WorkSpaceEntity condition) {
        Query<WorkSpaceEntity> query = datastore.createQuery(WorkSpaceEntity.class);
        query.field("tenantId").equal(condition.getTenantId());
        if (StringUtils.isNotBlank(condition.getName())) {
            query.field("name").containsIgnoreCase(condition.getName());
        }
        if (Objects.nonNull(condition.getStatus())) {
            query.field("status").equal(condition.getStatus());
        } else {
            query.field("status").notEqual(CmsStatus.DELETE);
        }
        if (CollectionUtils.isNotEmpty(condition.getChannelList())) {
            query.field("channelList").in(condition.getChannelList());
        }

        return query.countAll();
    }

    @Override
    public WorkSpaceEntity findByApiName(Integer tenantId, String apiName) {
        Query<WorkSpaceEntity> query = datastore.createQuery(WorkSpaceEntity.class);
        query.field("status").notEqual(CmsStatus.DELETE);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").equal(apiName);
        return query.get();
    }

    @Override
    public List<WorkSpaceEntity> findWorkSpaceList(Integer tenantId, Set<String> apiNameList) {
        Query<WorkSpaceEntity> query = datastore.createQuery(WorkSpaceEntity.class);
        query.field("tenantId").equal(tenantId);

        if (CollectionUtils.isNotEmpty(apiNameList)) {
            query.field("apiName").in(apiNameList);
        }
        query.field("status").notEqual(CmsStatus.DELETE);

        query.order("-createTime");
        return query.asList();
    }

    @Override
    public List<WorkSpaceEntity> findWorkSpaceIncludesDeleted(Integer tenantId, Set<String> apiNameList) {
        Query<WorkSpaceEntity> query = datastore.createQuery(WorkSpaceEntity.class);
        query.field("tenantId").equal(tenantId);

        if (CollectionUtils.isNotEmpty(apiNameList)) {
            query.field("apiName").in(apiNameList);
        }

        query.order("-createTime");
        return query.asList();
    }
    @Override
    public WorkSpaceEntity save(User user, WorkSpaceEntity entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setId(ObjectId.get().toHexString());
        }

        if (Objects.isNull(entity.getCreateTime())) {
            entity.setCreateTime(System.currentTimeMillis());
            entity.setCreatorId(String.valueOf(user.getUserId()));
        }

        entity.setTenantId(user.getTenantId());
        entity.setUpdateTime(System.currentTimeMillis());
        entity.setUpdaterId(String.valueOf(user.getUserId()));

        if (Objects.isNull(entity.getStatus())) {
            entity.setStatus(CmsStatus.ENABLE);
        }

        datastore.save(entity);
        return entity;
    }

    @Override
    public void save(WorkSpaceEntity entity) {
        // 简单保存，不做额外处理
        datastore.save(entity);
    }

    @Override
    public WorkSpaceEntity update(User user, WorkSpaceEntity entity) {
        if (StringUtils.isBlank(entity.getApiName())) {
            return null;
        }

        Query<WorkSpaceEntity> query = datastore.createQuery(WorkSpaceEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        query.field("apiName").equal(entity.getApiName());

        UpdateOperations<WorkSpaceEntity> ops = datastore.createUpdateOperations(WorkSpaceEntity.class);

        if (StringUtils.isNotBlank(entity.getName())) {
            ops.set("name", entity.getName());
        }

        if (StringUtils.isNotBlank(entity.getDescription())) {
            ops.set("description", entity.getDescription());
        }

        if (entity.getChannelList() != null) {
            ops.set("channelList", entity.getChannelList());
        }

        ops.set("updateTime", System.currentTimeMillis());
        ops.set("updaterId", user.getUserId());

        if (Objects.nonNull(entity.getStatus())) {
            ops.set("status", entity.getStatus());
        }

        return datastore.findAndModify(query, ops);
    }

    @Override
    public void update(WorkSpaceEntity entity) {
        Query<WorkSpaceEntity> query = datastore.createQuery(WorkSpaceEntity.class);
        query.field("tenantId").equal(entity.getTenantId());
        query.field("apiName").equal(entity.getApiName());

        UpdateOperations<WorkSpaceEntity> ops = datastore.createUpdateOperations(WorkSpaceEntity.class);
        ops.set("status", entity.getStatus());
        ops.set("updateTime", System.currentTimeMillis());
        ops.set("updaterId", entity.getUpdaterId());
        ops.set("name", entity.getName());
        ops.set("description", entity.getDescription());
        ops.set("channelList", entity.getChannelList());
        datastore.findAndModify(query, ops);
    }

    @Override
    public void updateStatusByApiName(User user, String apiName, Integer status) {
        Query<WorkSpaceEntity> query = datastore.createQuery(WorkSpaceEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        query.field("apiName").equal(apiName);

        UpdateOperations<WorkSpaceEntity> ops = datastore.createUpdateOperations(WorkSpaceEntity.class);
        ops.set("status", status);
        ops.set("updateTime", System.currentTimeMillis());
        ops.set("updaterId", user.getUserId());

        datastore.update(query, ops);
    }

    @Override
    public void delete(User user, String apiName) {
        // 标记删除工作区
        Query<WorkSpaceEntity> query = datastore.createQuery(WorkSpaceEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        query.field("apiName").equal(apiName);

        UpdateOperations<WorkSpaceEntity> ops = datastore.createUpdateOperations(WorkSpaceEntity.class);
        ops.set("status", CmsStatus.DELETE);
        ops.set("updateTime", System.currentTimeMillis());
        ops.set("updaterId", user.getUserId());

        datastore.update(query, ops);
    }

    @Override
    public List<WorkSpaceEntity> findByApiNames(Integer tenantId, List<String> apiNames) {
        if (CollectionUtils.isEmpty(apiNames)) {
            return Collections.emptyList();
        }

        Query<WorkSpaceEntity> query = datastore.createQuery(WorkSpaceEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").in(apiNames);

        return query.asList();
    }
}
