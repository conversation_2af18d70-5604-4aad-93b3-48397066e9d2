package com.facishare.webpage.customer.core.config;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.core.model.MenuCollection;
import com.github.autoconf.ConfigFactory;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON> on 19/12/11.
 */
public class MenusCollectionConfig {


    private Map<String,MenuCollection> menuCollections;

    public void init() {
        ConfigFactory.getInstance().getConfig("fs-webpage-customer-menus-collection", iConfig -> {
            List<MenuCollection> collections = JSONObject.parseArray(iConfig.getString(), MenuCollection.class);
            menuCollections = collections.stream().collect(Collectors.toMap(MenuCollection::getId, Function.identity(),(key1,key2)->key2));
        });
    }

    public MenuCollection getMenuCollection(String collectionId) {
        return menuCollections.get(collectionId);
    }

}
