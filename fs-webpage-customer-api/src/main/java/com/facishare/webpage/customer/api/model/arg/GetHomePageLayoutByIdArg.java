package com.facishare.webpage.customer.api.model.arg;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Locale;

/**
 * Created by zhangyu on 2019/9/9
 */
@Data
public class GetHomePageLayoutByIdArg implements Serializable {

    @JSONField(name = "M1")
    @SerializedName("LayoutID")
    private String layoutId;
    @JSONField(name = "M2")
    @SerializedName("appType")
    private int appType;
    @JSONField(name = "M3")
    @SerializedName("layoutApiName")
    private String layoutApiName;

    private Integer enterpriseId;
    private String enterpriseAccount;
    private Integer employeeId;
    private ClientTypeEnum type;
    private String version;
    private String deviceId;
    private Locale locale;
    private String osVersion;
    private boolean translateFlag = true;
    private boolean manage = false;

    public void valid() {
        if (StringUtils.isEmpty(layoutId) && StringUtils.isEmpty(layoutApiName)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
