package com.facishare.webpage.customer.processor.siteDiffProcessor.result;

// 内部类定义
public class DiffResults {

    private EntityDiffResult themeStyleDiff;
    private EntityDiffResult themeLayoutDiff;
    private EntityDiffResult menuDiff;
    private EntityDiffResult pageDiff;
    private EntityDiffResult fileDiff;
    private EntityDiffResult i18nDiff;

    public DiffResults(EntityDiffResult themeStyleDiff, EntityDiffResult themeLayoutDiff,
                       EntityDiffResult menuDiff, EntityDiffResult pageDiff,
                       EntityDiffResult fileDiff, EntityDiffResult i18nDiff) {
        this.themeStyleDiff = themeStyleDiff;
        this.themeLayoutDiff = themeLayoutDiff;
        this.menuDiff = menuDiff;
        this.pageDiff = pageDiff;
        this.fileDiff = fileDiff;
        this.i18nDiff = i18nDiff;
    }

    public EntityDiffResult getThemeStyleDiff() {
        return themeStyleDiff;
    }

    public EntityDiffResult getThemeLayoutDiff() {
        return themeLayoutDiff;
    }

    public EntityDiffResult getMenuDiff() {
        return menuDiff;
    }

    public EntityDiffResult getPageDiff() {
        return pageDiff;
    }

    public EntityDiffResult getFileDiff() {
        return fileDiff;
    }

    public EntityDiffResult getI18nDiff() {
        return i18nDiff;
    }

}


