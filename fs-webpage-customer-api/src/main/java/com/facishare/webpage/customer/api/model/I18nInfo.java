package com.facishare.webpage.customer.api.model;

import com.facishare.webpage.customer.api.constant.TranslateI18nUtils;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.fxiaoke.i18n.SupportLanguage;
import com.fxiaoke.i18n.client.api.Localization;
import com.fxiaoke.i18n.util.LangIndex;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class I18nInfo {
    private String apiName;
    private String defaultValue;
    private String value;
    private String preKey;
    private String customKey;
    private Map<String, String> languageInfo;

    private static final String CUSTOM_BUSINESS_KEY = "webpage_customer_";

    public I18nInfo buildResult() {
        I18nInfo i18nInfo = I18nInfo.builder()
                .apiName(getApiName())
                .value(getValue())
                .build();
        if (RequestContextManager.isFromManager()) {
            i18nInfo.setLanguageInfo(getLanguageInfo());
            i18nInfo.setCustomKey(getCustomKey());
            i18nInfo.setDefaultValue(getDefaultValue());
            i18nInfo.setPreKey(getPreKey());
        }
        return i18nInfo;
    }

    public void setLanguageInfo(Map<String, String> languageInfo) {
        if (Objects.isNull(languageInfo) || languageInfo.isEmpty()) {
            return;
        }
        languageInfo.entrySet().removeIf(x -> StringUtils.isEmpty(x.getValue()) || StringUtils.isEmpty(x.getKey()));
        this.languageInfo = languageInfo;
    }

    public void setLanguageInfo(Localization languageInfo) {
        if (Objects.isNull(languageInfo)) {
            return;
        }
        Map<Byte, String> data = languageInfo.getData();
        if (Objects.isNull(data) || data.isEmpty()) {
            return;
        }
        Map<String, String> localization = Maps.newHashMap();
        data.forEach((k, v) -> {
            if (StringUtils.isEmpty(v)) {
                return;
            }
            SupportLanguage supportLanguage = LangIndex.getInstance().get(k);
            if (Objects.isNull(supportLanguage)) {
                return;
            }
            String replace = supportLanguage.getBeanDisplay().replace("_", "-");
            localization.put(replace, v);

        });
        this.languageInfo = localization;
    }


    public static I18nInfo from(Map<String, Object> map) {
        if (Objects.isNull(map)) {
            return I18nInfo.builder().build();
        }
        I18nInfo i18nInfo = new I18nInfo();
        if (StringUtils.isNotEmpty((CharSequence) map.get("apiName"))) {
            i18nInfo.setApiName(String.valueOf(map.get("apiName")));
        }
        if (Objects.nonNull(map.get("languageInfo"))) {
            Map<String, String> languageInfoParam = (Map<String, String>) map.get("languageInfo");
            i18nInfo.setLanguageInfo(languageInfoParam);
        }
        if (Objects.nonNull(map.get("value"))) {
            i18nInfo.setValue(String.valueOf(map.get("value")));
        }
        if (Objects.nonNull(map.get("preKey"))) {
            i18nInfo.setPreKey(String.valueOf(map.get("preKey")));
        }
        if (Objects.nonNull(map.get("customKey"))) {
            i18nInfo.setCustomKey(String.valueOf(map.get("customKey")));
        }
        if (Objects.nonNull(map.get("defaultValue"))) {
            i18nInfo.setDefaultValue(String.valueOf(map.get("defaultValue")));
        }

        return i18nInfo;
    }

    public I18nInfo buildByTransValue(Integer tenantId, Map<String, Localization> languageMap, String languageTag) {
        Localization localization = languageMap.get(getCustomKey());
        if (RequestContextManager.isFromManager() || RequestContextManager.isFromRest()) {
            if (Objects.nonNull(localization)) {
                setLanguageInfo(localization);
                String transValue = localization.get(languageTag, null);
                if (StringUtils.isBlank(transValue)) {
                    transValue = TranslateI18nUtils.getI18nValue(tenantId, getPreKey(), languageTag, null);
                }
                setValue(transValue);
            }
        } else {
            String transValue = "";
            if (Objects.nonNull(localization) && StringUtils.isNotEmpty(localization.get(languageTag, null))) {
                transValue = localization.get(languageTag, getDefaultValue());
            } else {
                transValue = TranslateI18nUtils.getI18nValue(tenantId, getPreKey(), languageTag, getDefaultValue());
            }
            setValue(transValue);
            setLanguageInfo(Maps.newHashMap());
        }
        return buildResult();
    }

    public Localization toLocalization(String tenantId) {
        if (Objects.isNull(getLanguageInfo()) || getLanguageInfo().isEmpty()) {
            return null;
        }
        Localization localization = new Localization();
        getLanguageInfo().forEach(localization::set);
        localization.setKey(getCustomKey());
        localization.setTenantId(Long.parseLong(tenantId));
        localization.setTags(Lists.newArrayList("server", "web"));
        return localization;
    }


}
