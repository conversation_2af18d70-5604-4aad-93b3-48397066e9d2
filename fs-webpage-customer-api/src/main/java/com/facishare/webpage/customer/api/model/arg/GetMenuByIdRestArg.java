package com.facishare.webpage.customer.api.model.arg;

import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Locale;

/**
 * Created by zhangyu on 2020/11/26
 */
@Data
public class GetMenuByIdRestArg implements Serializable {
    private String menuId;
    private Integer enterpriseId;
    private String enterpriseAccount;
    private Integer employeeId;
    private ClientTypeEnum type;
    private String version;
    private String deviceId;
    private Locale locale;
    private String osVersion;
    private boolean previewNewCrmFlag = false;
    public void valid() {
        if (StringUtils.isEmpty(menuId)){
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
