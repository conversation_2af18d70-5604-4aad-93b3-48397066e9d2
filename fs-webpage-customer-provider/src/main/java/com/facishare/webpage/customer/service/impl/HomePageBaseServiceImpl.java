package com.facishare.webpage.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.qixin.common.monitor.GlobalStopWatch;
import com.facishare.qixin.common.monitor.SlowLog;
import com.facishare.qixin.objgroup.common.service.PaasOrgGroupService;
import com.facishare.qixin.sysdb.filter.Filter;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.*;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.*;
import com.facishare.webpage.customer.api.model.core.SimpObjectDescription;
import com.facishare.webpage.customer.api.model.core.customelayout.CustomerLayout;
import com.facishare.webpage.customer.api.utils.WebPageUtils;
import com.facishare.webpage.customer.common.CheckService;
import com.facishare.webpage.customer.common.EmployeeConfigCommonService;
import com.facishare.webpage.customer.common.LanguageService;
import com.facishare.webpage.customer.common.OrganizationCommonService;
import com.facishare.webpage.customer.component.CovertCustomerManage;
import com.facishare.webpage.customer.config.ComponentConfig;
import com.facishare.webpage.customer.config.DefaultTenantConfig;
import com.facishare.webpage.customer.constant.*;
import com.facishare.webpage.customer.core.component.ComponentCovertService;
import com.facishare.webpage.customer.core.config.ComponentNameConfig;
import com.facishare.webpage.customer.core.config.PageTemplateConfig;
import com.facishare.webpage.customer.core.config.WidgetsConfig;
import com.facishare.webpage.customer.core.model.PageTemplateConfigVO;
import com.facishare.webpage.customer.core.model.Widget;
import com.facishare.webpage.customer.core.service.I18nService;
import com.facishare.webpage.customer.core.service.UIPaasLicenseService;
import com.facishare.webpage.customer.core.util.BIUrlUtil;
import com.facishare.webpage.customer.core.util.ComponentExt;
import com.facishare.webpage.customer.core.util.ScopesUtil;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.facishare.webpage.customer.dao.EmployeeConfigDao;
import com.facishare.webpage.customer.dao.HomePageLayoutDao;
import com.facishare.webpage.customer.dao.TenantConfigDao;
import com.facishare.webpage.customer.dao.entity.EmployeeConfigEntity;
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity;
import com.facishare.webpage.customer.dao.entity.TenantConfigEntity;
import com.facishare.webpage.customer.filter.FilterLayoutComponentService;
import com.facishare.webpage.customer.model.PageData;
import com.facishare.webpage.customer.model.UserHomePageLayoutAO;
import com.facishare.webpage.customer.model.component.CustomerLayoutHelper;
import com.facishare.webpage.customer.remote.ObjectService;
import com.facishare.webpage.customer.remote.TempFileToFormalFile;
import com.facishare.webpage.customer.service.HomePageBaseService;
import com.facishare.webpage.customer.service.HomePageCommonService;
import com.facishare.webpage.customer.service.RemoteService;
import com.facishare.webpage.customer.service.UserHomePageBaseService;
import com.facishare.webpage.customer.system.PageSystemService;
import com.facishare.webpage.customer.util.*;
import com.facishare.webpage.customer.util.component.FilterCardCovert;
import com.facishare.webpage.customer.util.component.NewDataCovertOldData;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mongodb.morphia.query.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.webpage.customer.util.PageTemplateUtils.isBICard;
import static com.facishare.webpage.customer.util.PageTemplateUtils.isScene;

/**
 * Created by zhangyu on 2019/9/12
 */
public class HomePageBaseServiceImpl implements HomePageBaseService {

    private static final Logger logger = LoggerFactory.getLogger(HomePageBaseServiceImpl.class);

    @Autowired
    private HomePageLayoutDao homePageLayoutDao;
    @Autowired
    private UserHomePageBaseService userHomePageBaseService;
    @Autowired
    private HomePageCommonService homePageCommonService;
    @Autowired
    private ObjectService objectService;
    @Resource
    private PageSystemService pageSystemService;
    @Resource
    private EmployeeConfigDao employeeConfigDao;
    @Resource
    private RemoteService remoteService;
    @Resource
    private ComponentNameConfig componentNameConfig;
    @Autowired
    private BIUrlUtil biUrlUtil;
    @Autowired
    private PaasOrgGroupService paasOrgGroupService;
    @Resource
    private ComponentCovertService componentCovertService;
    @Resource
    private CovertCustomerManage covertCustomerManage;
    @Resource
    private ComponentConfig componentConfig;
    @Resource
    private LanguageService languageService;
    @Resource
    private DealRelationService dealRelationService;
    @Resource
    private EmployeeConfigCommonService employeeConfigCommonService;
    @Resource
    private PageTemplateConfig pageTemplateConfig;
    @Resource
    private TenantConfigDao tenantConfigDao;
    @Resource
    private FilterLayoutComponentService filterLayoutComponentService;
    @Resource
    private WidgetsConfig widgetsConfig;
    @Resource
    private OrganizationCommonService organizationCommonService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private TempFileToFormalFile tempFileToFormalFile;
    @Resource
    private I18nService i18nService;
    @Resource
    private UIPaasLicenseService uiPaasLicenseService;
    @Autowired
    private CheckService checkService;

    public void setHomePageLayoutDao(HomePageLayoutDao homePageLayoutDao) {
        this.homePageLayoutDao = homePageLayoutDao;
    }

    @Override
    public HomePageLayoutTO getHomePageLayoutByLayoutId(int tenantId, Integer employeeId, OuterUserInfo outerUserInfo, String layoutId, String apiName, int appType, Locale locale, SlowLog stopWatch, boolean manager) {
        HomePageLayoutEntity homePageLayoutEntity;
        if (BizType.CRM.getType() == appType) {
            homePageLayoutEntity = getCrmHomePageLayoutEntity(layoutId);
        } else if (BizType.CUSTOMER.getType() == appType) {
            homePageLayoutEntity = homePageLayoutDao.getHomePageByTenantIdAndApiName(tenantId, apiName, null);
        } else if (BizType.PRE_CUSTOMER.getType() == appType) {
            List<HomePageLayoutEntity> homePageLayoutEntityList = pageSystemService.queryPreCustomPageList(tenantId, appType, apiName);
            if (CollectionUtils.isNotEmpty(homePageLayoutEntityList)) {
                if (homePageLayoutEntityList.size() > 1) {
                    logger.warn("there is more HomePageLayoutEntity for tenantId:{}, appType:{}, apiName:{}", tenantId, appType, apiName);
                }
                homePageLayoutEntity = homePageLayoutEntityList.get(0);
            } else {
                homePageLayoutEntity = null;
                logger.warn("there is no HomePageLayoutEntity for tenantId:{}, appType:{}, apiName:{}", tenantId, appType, apiName);
            }
        } else {
            homePageLayoutEntity = pageSystemService.getPageEntityByLayoutId(tenantId, layoutId);
        }
        stopWatch.lap("getHomePageEntity");
        HomePageLayoutTO homePageLayoutTO = HomePageLayoutUtil.buildHomePageLayoutItem(homePageLayoutEntity);
        stopWatch.lap("buildHomePageLayoutItem");
        // 对不支持的对象的场景进行过滤以及对widget支持多语的下发
        updateLayoutComponents(tenantId, employeeId, outerUserInfo, homePageLayoutTO, locale, stopWatch, manager);
        stopWatch.lap("updateLayoutComponents");
        logger.debug("getHomePageLayoutByLayoutId homePageLayoutTO {}", homePageLayoutTO);
        return homePageLayoutTO;
    }

    @Override
    public HomePageLayoutEntity getCrmHomePageLayoutEntity(String layoutId) {
        HomePageLayoutEntity homePageLayoutEntity = homePageLayoutDao.getHomePageLayoutById(layoutId, null);
        if (homePageLayoutEntity == null) {
            return null;
        }
        if (SourceType.SYSTEM.equals(homePageLayoutEntity.getSourceType()) && !homePageLayoutEntity.isChange()) {
            HomePageLayoutEntity homePageLayoutById = homePageLayoutDao.getHomePageLayoutById(homePageLayoutEntity.getSourceId(), null);
            {
                homePageLayoutEntity.setHomePageCardEntityList(homePageLayoutById.getHomePageCardEntityList());
                homePageLayoutEntity.setName(homePageLayoutById.getName());
                homePageLayoutEntity.setPriorityLevel(homePageLayoutById.getPriorityLevel());
                homePageLayoutEntity.setPageLayoutType(homePageLayoutById.getPageLayoutType());
                homePageLayoutEntity.setCustomerLayout(homePageLayoutById.getCustomerLayout());
                homePageLayoutEntity.setDescription(homePageLayoutById.getDescription());
            }
        }
        return homePageLayoutEntity;
    }

    @Override
    public List<HomePageLayoutEntity> getOldCrmHomePageLayoutEntityList(String tenantId, List<Integer> layoutType, List<Integer> status,
                                                                        Locale locale, boolean isTrans) {
        List<HomePageLayoutEntity> homePageLayoutEntityList = homePageLayoutDao.getHomePageLayoutListBytenantId(Integer.parseInt(tenantId),
                new Filter<Query<HomePageLayoutEntity>>() {
                    @Override
                    public void addFilter(Query<HomePageLayoutEntity> query) {
                        query.filter("appId", Constant.APP_CRM);
                        if (CollectionUtils.isNotEmpty(layoutType)) {
                            query.field("layoutType").in(layoutType);
                        }
                        if (CollectionUtils.isNotEmpty(status)) {
                            query.field("status").in(status);
                        }
                        // priorityLevel==0系 统预置, priorityLevel>0 自定义, 没有就算脏数据
                        query.field("priorityLevel").greaterThanOrEq(0);
                        Filter.super.addFilter(query);
                    }
                });
        if (CollectionUtils.isNotEmpty(homePageLayoutEntityList)) {
            // 补充source数据
            homePageLayoutEntityList.forEach(homePageLayoutEntity -> {
                if (SourceType.SYSTEM.equals(homePageLayoutEntity.getSourceType()) && !homePageLayoutEntity.isChange()) {
                    HomePageLayoutEntity sourceEntity = homePageLayoutDao.getHomePageLayoutById(homePageLayoutEntity.getSourceId(), null);
                    homePageLayoutEntity.setHomePageCardEntityList(sourceEntity.getHomePageCardEntityList());
                    homePageLayoutEntity.setName(sourceEntity.getName());
                    homePageLayoutEntity.setPriorityLevel(sourceEntity.getPriorityLevel());
                    homePageLayoutEntity.setPageLayoutType(sourceEntity.getPageLayoutType());
                    homePageLayoutEntity.setCustomerLayout(sourceEntity.getCustomerLayout());
                    homePageLayoutEntity.setDescription(sourceEntity.getDescription());
                }
            });
        }
        if (isTrans) {
            locale = Objects.nonNull(locale) ? locale : Locale.CHINA;
            setOldCrmHomePageLayoutNames(Integer.parseInt(tenantId), homePageLayoutEntityList, locale);
        }
        return homePageLayoutEntityList;
    }

    /**
     * 处理CRM首页布局的多语言
     *
     * @param tenantId                 租户ID
     * @param homePageLayoutEntityList 首页布局实体列表
     * @param locale                   语言环境
     */
    private void setOldCrmHomePageLayoutNames(int tenantId, List<HomePageLayoutEntity> homePageLayoutEntityList, Locale locale) {
        if (CollectionUtils.isEmpty(homePageLayoutEntityList)) {
            return;
        }

        // 构建翻译参数
        List<I18nTrans.TransArg> i18nTranArgList = Lists.newArrayList();
        homePageLayoutEntityList.forEach(entity -> {
            List<String> preKeys = Lists.newArrayList();

            // 添加原有的key
            preKeys.add(TranslateI18nUtils.getCrmFrontPageKey(entity.getLayoutId()));

            // 如果有sourceId，添加source的key
            if (StringUtils.isNotEmpty(entity.getSourceId())) {
                preKeys.add(WebPageConstants.CRM_HOMEPAGE_PREFIX + TranslateI18nUtils.delAllEi(entity.getSourceId()));
            }

            // 添加老的预置key
            preKeys.add(WebPageConstants.CRM_HOMEPAGE_PREFIX + entity.getName());

            // 构建名称的翻译参数
            i18nTranArgList.add(I18nTrans.TransArg.builder()
                    .name(entity.getName())
                    .customKey(TranslateI18nUtils.getWebPageNameKey(entity.getApiName()))
                    .preKeyList(preKeys)
                    .build());
        });

        // 获取翻译结果
        Map<String, String> transValueMap = i18nService.getTransValueIncludePreKey(
                tenantId,
                i18nTranArgList,
                locale.toLanguageTag());

        // 更新实体的名称和描述
        homePageLayoutEntityList.forEach(entity -> {
            // 更新名称
            String nameKey = TranslateI18nUtils.getWebPageNameKey(entity.getApiName());
            String translatedName = transValueMap.getOrDefault(nameKey, entity.getName());
            entity.setName(translatedName);
        });
    }

    @Override
    public HomePageLayoutTO getHomePageLayoutByApiName(UserInfo userInfo, String layoutId, String apiName, int appType, ClientInfo clientInfo, boolean translateFlag) {
        int tenantId = userInfo.getEnterpriseId();
        SlowLog stopWatch = GlobalStopWatch.create("getHomePageLayoutByApiName", 100L);
        HomePageLayoutTO homePageLayout = getHomePageLayoutByLayoutId(tenantId, userInfo.getEmployeeId(), null, layoutId, apiName, appType, clientInfo.getLocale(), stopWatch, true);

        if (homePageLayout == null) {
            return null;
        }

        translateFlag = translateFlag && uiPaasLicenseService.existMultiLanguageModule(tenantId);
        setHomePageLayoutName(tenantId, Lists.newArrayList(homePageLayout), clientInfo.getLocale());    // 设置首页名称和描述多语
        if (translateFlag) {    // 组件与顶导航翻译
            doTranslate(tenantId, homePageLayout, clientInfo.getLocale(), true);
        }
        if (homePageLayout.getDataVersion() == DataVersion.dataVersion_100) {
            // 构建首页卡片的url
            buildHomePageLayoutsUrl(userInfo, null, homePageLayout, clientInfo.getLocale());
            CustomerLayout customerLayout = buildCustomerLayout(homePageLayout.getAppId(), userInfo, null, homePageLayout.getHomePageLayouts(), layoutId, true, clientInfo.getLocale());
            homePageLayout.setCustomerLayout(JSONObject.parseObject(JSONObject.toJSONString(customerLayout)));
            List<JSONObject> customerLayoutList = new ArrayList<>();
            homePageLayout.getCustomerLayoutList().forEach(item -> {
                CustomerLayout customerLayout1 = buildCustomerLayout(homePageLayout.getAppId(), userInfo, null, homePageLayout.getHomePageLayouts(), layoutId, true, clientInfo.getLocale());
                customerLayoutList.add(JSONObject.parseObject(JSONObject.toJSONString(customerLayout1)));
            });
            homePageLayout.setCustomerLayoutList(customerLayoutList);
        } else if (homePageLayout.getDataVersion() == DataVersion.dataVersion_200) {
            JSONObject pageLayoutCustomerLayout = homePageLayout.getCustomerLayout();
            JSONObject customerLayout = covertCustomerManage.convertTenantLayout(userInfo, layoutId, homePageLayout.getAppId(), clientInfo.getLocale(), pageLayoutCustomerLayout, homePageLayout.getHomePageLayouts());
            homePageLayout.setCustomerLayout(customerLayout);
            List<JSONObject> customerLayoutList = new ArrayList<>();
            homePageLayout.getCustomerLayoutList().forEach(item -> {
                JSONObject customerLayout1 = covertCustomerManage.convertTenantLayout(userInfo, layoutId, homePageLayout.getAppId(), clientInfo.getLocale(), item, homePageLayout.getHomePageLayouts());
                customerLayoutList.add(customerLayout1);
            });
            homePageLayout.setCustomerLayoutList(customerLayoutList);
            if (WebPageConstants.APP_CRM.equals(homePageLayout.getAppId())) {
                Map<String, Integer> widgetTypeMap = componentConfig.getWidgetTypeMap();
                List<String> crmFilterComponents = componentConfig.getCrmFilterComponentsByApp();
                List<HomePageLayoutCard> homePageLayoutCardList = NewDataCovertOldData.covertNewData(pageLayoutCustomerLayout, widgetTypeMap, crmFilterComponents);
                homePageLayout.setHomePageLayouts(homePageLayoutCardList);
                buildHomePageLayoutsUrl(userInfo, null, homePageLayout, clientInfo.getLocale());
            }
        }
        if (homePageLayout.getFromOldCrmHomePage()) {
            homePageLayout.setShowPageMultiFlag(false);
        }

        stopWatch.stop("getHomePageLayoutByApiName");
        return homePageLayout;
    }

    @Override
    public HomePageLayoutTO getUserHomePageLayoutByApiName(UserInfo userInfo, OuterUserInfo outerUserInfo, String layoutId, String apiName, int appType, Locale locale) {
        int tenantId = userInfo.getEnterpriseId();
        SlowLog stopWatch = GlobalStopWatch.create("getUserHomePageLayoutByApiName", 100L);
        HomePageLayoutTO homePageLayout = getHomePageLayoutByLayoutId(tenantId, userInfo.getEmployeeId(), outerUserInfo, layoutId, apiName, appType, locale, stopWatch, false);

        if (homePageLayout == null) {
            return null;
        }
        // crm迁移后 老得crm首页  会当作自定义页面来查询，传的appType是4，导致查不到数据，所以在这里做下转换
        if (SourceType.SYSTEM.equals(homePageLayout.getSourceType()) && 4 == homePageLayout.getAppType()) {
            appType = 1;
            layoutId = homePageLayout.getLayoutId();
            homePageLayout = getHomePageLayoutByLayoutId(tenantId, userInfo.getEmployeeId(), outerUserInfo, layoutId, apiName, appType, locale, stopWatch, false);
        }
        appType = homePageLayout.getAppType();

        setHomePageLayoutName(tenantId, Lists.newArrayList(homePageLayout), locale);

        doTranslate(tenantId, homePageLayout, locale, false);
        if (homePageLayout.getDataVersion() == DataVersion.dataVersion_100) {
            // 构建首页卡片的url
            buildHomePageLayoutsUrl(userInfo, outerUserInfo, homePageLayout, locale);
            CustomerLayout customerLayout = buildCustomerLayout(homePageLayout.getAppId(), userInfo, outerUserInfo, homePageLayout.getHomePageLayouts(), layoutId, false, locale);
            homePageLayout.setCustomerLayout(JSONObject.parseObject(JSONObject.toJSONString(customerLayout)));

            List<JSONObject> customerLayoutList = new ArrayList<>();
            for (JSONObject item : homePageLayout.getCustomerLayoutList()) {
                CustomerLayout customerLayout1 = buildCustomerLayout(homePageLayout.getAppId(), userInfo, outerUserInfo, homePageLayout.getHomePageLayouts(), layoutId, false, locale);
                customerLayoutList.add(JSONObject.parseObject(JSONObject.toJSONString(customerLayout1)));
            }
            homePageLayout.setCustomerLayoutList(customerLayoutList);
        } else if (homePageLayout.getDataVersion() == DataVersion.dataVersion_200) {
            if (BizType.CUSTOMER.getType() == appType) {
                layoutId = apiName;
            }

            if (WebPageConstants.APP_CRM.equals(homePageLayout.getAppId())) {
                Map<String, Integer> widgetTypeMap = componentConfig.getWidgetTypeMap();
                List<String> crmFilterComponents = componentConfig.getCrmFilterComponentsByApp();
                List<HomePageLayoutCard> homePageLayoutCardList = NewDataCovertOldData.covertNewData(homePageLayout.getCustomerLayout(), widgetTypeMap, crmFilterComponents);
                homePageLayout.setHomePageLayouts(homePageLayoutCardList);
                buildHomePageLayoutsUrl(userInfo, null, homePageLayout, locale);
            }
            // 这里转换个人级筛选器
            JSONObject customerLayout = covertCustomerManage.convertUserLayout(userInfo, outerUserInfo, layoutId, appType, homePageLayout.getAppId(), homePageLayout.getCustomerLayout(), homePageLayout.getHomePageLayouts(), locale);
            homePageLayout.setCustomerLayout(customerLayout);

            List<JSONObject> customerLayoutList = new ArrayList<>();
            for (JSONObject item : homePageLayout.getCustomerLayoutList()) {
                JSONObject customerLayout1 = covertCustomerManage.convertUserLayout(userInfo, outerUserInfo, layoutId, appType, homePageLayout.getAppId(), item, homePageLayout.getHomePageLayouts(), locale);
                // 顶导航数据针对适用范围做权限过滤
                JSONArray scopeListArray = customerLayout1.getJSONArray(CustomerLayoutField.scopeList);
                if (CustomerLayoutField.LABEL_INDEX_STRING.equals(customerLayout1.getString(CustomerLayoutField.LABEL_INDEX))) {
                    // 默认页签无需过滤
                    customerLayoutList.add(customerLayout1);
                    continue;
                }
                if (customerLayout1.getBooleanValue(CustomerLayoutField.LABEL_IS_OPEN_RANGE)) {
                    if (CollectionUtils.isNotEmpty(scopeListArray)) {
                        // 设置适用范围，则针对适用范围做权限过滤
                        List<Scope> userScopeList = organizationCommonService.queryScopeList(tenantId, userInfo.getEmployeeId(), null, null, homePageLayout.getAppId());
                        List<String> userScopesToString = ScopesUtil.buildScopesToString(userScopeList);

                        List<Scope> layoutScopeList = new ArrayList<>();
                        scopeListArray.forEach(x -> {
                            JSONObject scopJsonObject = JSONObject.parseObject(JSONObject.toJSONString(x));
                            Scope scope = new Scope();
                            scope.setDataType(scopJsonObject.getInteger(CustomerLayoutField.scopeDataType));
                            scope.setDataId(scopJsonObject.getString(CustomerLayoutField.scopeDataID).replace(".0", ""));
                            layoutScopeList.add(scope);
                        });
                        List<String> layoutScopesToString = ScopesUtil.buildScopesToString(layoutScopeList);

                        if (CollectionUtils.containsAny(layoutScopesToString, userScopesToString)) {
                            // 该顶导航页签的适用范围包含当前用户的适用范围，则下发该标签页
                            customerLayoutList.add(customerLayout1);
                        }
                    }
                } else {
                    customerLayoutList.add(customerLayout1);
                }
            }
            homePageLayout.setCustomerLayoutList(customerLayoutList);
        }
        stopWatch.stop("getUserHomePageLayoutByApiName");
        return homePageLayout;
    }

    private void buildHomePageLayoutsUrl(UserInfo userInfo, OuterUserInfo outerUserInfo, HomePageLayoutTO homePageLayout, Locale locale) {

        if (CollectionUtils.isEmpty(homePageLayout.getHomePageLayouts())) {
            return;
        }

        homePageLayout.getHomePageLayouts().stream().forEach(homePageLayoutCard -> {
            String url = biUrlUtil.buildUrl(homePageLayoutCard.getCardId(), homePageLayoutCard.getType());
            homePageLayoutCard.setUrl(url);
            if (CardType.isFilter(homePageLayoutCard.getType())) {
                List<HomePageLayoutFilter> homePageLayoutFilters = buildHomePageFilter(userInfo, outerUserInfo, homePageLayout.getAppId(), homePageLayoutCard.getHomePageLayoutFilters(), locale);
                homePageLayoutCard.setHomePageLayoutFilters(homePageLayoutFilters);
            }
        });
    }

    @Override
    public HomePageLayoutTO insertHomePageLayout(String appId, int appType, int tenantId, int employeeId, String apiName, Integer applyType, HomePageLayoutTO homePageLayoutTO) {
        HomePageLayoutEntity homePageLayoutEntity;
        if (BizType.CUSTOMER.getType() == appType) {
            HomePageLayoutEntity homePageByTenantIdAndApiName = homePageLayoutDao.getHomePageByTenantIdAndApiName(tenantId, apiName, null);
            if (homePageByTenantIdAndApiName != null) {
                throw new WebPageException(InterErrorCode.DUPLICATE_HOMEPAGE);
            }
            if (applyType == null) {
                applyType = 0;
            }
            HomePageLayoutEntity entity = HomePageLayoutUtil.buildHomePageLayoutEntity(appType, appId, employeeId, applyType, homePageLayoutTO);
            homePageLayoutEntity = homePageLayoutDao.findAndModifyHomePage(tenantId, apiName, entity);
        } else {
            homePageLayoutTO.setLayoutId(TempleIdUtil.buildId(tenantId));
            homePageLayoutEntity = homePageLayoutDao.insertHomePageLayout(appId, appType, tenantId, employeeId, homePageLayoutTO, "", true);
        }
        if (homePageLayoutEntity == null) {
            return null;
        }
        if (DataVersion.dataVersion_200 == homePageLayoutTO.getDataVersion()) {
            // 做引用关系的处理
            List<String> cusCompApiNames = ComponentUtil.getCusCompApiNames(homePageLayoutEntity.getCustomerLayout());
            dealRelationService.updateCusCompRelation(tenantId, appId, homePageLayoutEntity.getName(), homePageLayoutEntity.getApiName(), cusCompApiNames);
        }
        HomePageLayoutTO buildHomePageLayoutTO = HomePageLayoutUtil.buildHomePageLayoutItem(homePageLayoutEntity);
        logger.debug("insertHomePageLayout homePageLayoutTO {}", buildHomePageLayoutTO);
        return buildHomePageLayoutTO;
    }

    @Override
    public HomePageLayoutTO updateHomePageLayout(int appType, String appId, int tenantId, int employeeId, String apiName, Integer applyType, HomePageLayoutTO homePageLayoutTO) {
        HomePageLayoutEntity homePageLayoutEntity;
        if (BizType.CRM.getType() == appType) {
            homePageLayoutEntity = homePageLayoutDao.updateHomePageLayout(employeeId, homePageLayoutTO);
        } else if (BizType.CUSTOMER.getType() == appType || BizType.PRE_CUSTOMER.getType() == appType) {
            if (applyType == null) {
                applyType = 0;
            }
            HomePageLayoutEntity entity = HomePageLayoutUtil.buildHomePageLayoutEntity(appType, appId, employeeId, applyType, homePageLayoutTO);
            homePageLayoutEntity = homePageLayoutDao.findAndModifyHomePage(tenantId, apiName, entity);
        } else {
            HomePageLayoutEntity entity = HomePageLayoutUtil.buildHomePageLayoutEntity(appType, appId, employeeId, null, homePageLayoutTO);
            entity.setApiName(TempleIdUtil.removeTenantIdById(tenantId, appType, entity.getLayoutId()));
            homePageLayoutEntity = pageSystemService.createOrUpdatePage(tenantId, entity.getLayoutId(), entity);
        }

        if (DataVersion.dataVersion_200 == homePageLayoutTO.getDataVersion()) {
            // 做引用关系的处理
            List<String> cusCompApiNames = ComponentUtil.getCusCompApiNames(homePageLayoutEntity.getCustomerLayout());
            dealRelationService.updateCusCompRelation(tenantId, appId, homePageLayoutEntity.getName(), homePageLayoutEntity.getApiName(), cusCompApiNames);
        }
        HomePageLayoutTO buildHomePageLayoutTO = HomePageLayoutUtil.buildHomePageLayoutItem(homePageLayoutEntity);
        logger.debug("updateHomePageLayout homePageLayoutTO {}", buildHomePageLayoutTO);
        return buildHomePageLayoutTO;
    }

    @Override
    public boolean makeHomePageFormal(String layoutId, int employeeId, String templeId, int appType) {
        HomePageLayoutEntity homePageLayoutEntity = homePageLayoutDao.makeHomePageFormal(layoutId, employeeId, templeId, appType);
        logger.debug("makeHomePageFormal homePageLayoutEntity {}", homePageLayoutEntity);
        if (homePageLayoutEntity != null) {
            return true;
        }
        return false;
    }

    @Override
    public List<HomePageLayoutTO> getEmployeeHomePageLayoutList(int tenantId, int employeeId, int appType, String appId, Locale locale, Boolean enablePersonPageConfig) {
        List<HomePageLayoutEntity> homePageLayoutEntityList = Lists.newArrayList();
        SlowLog stopWatch = GlobalStopWatch.create("getEmployeeHomePageLayoutList", 100L);
        if (BizType.CRM.getType() == appType) {
            List<String> scopeList = homePageCommonService.getScopeList(tenantId, employeeId, appId);
            stopWatch.lap("over getScopeList");
            List<HomePageLayoutEntity> employeeHomePageLayoutList = homePageLayoutDao.getEmployeeHomePageLayoutList(tenantId, appId, scopeList, enablePersonPageConfig);
            stopWatch.lap("over dao getEmployeeHomePageLayoutList");
            for (HomePageLayoutEntity homePageLayoutEntity : employeeHomePageLayoutList) {
                if (SourceType.SYSTEM.equals(homePageLayoutEntity.getSourceType()) && !homePageLayoutEntity.isChange() && homePageLayoutEntity.getLayoutType() == LayoutType.SYSTEM) {
                    HomePageLayoutEntity homePageLayoutById = homePageLayoutDao.getHomePageLayoutById(homePageLayoutEntity.getSourceId(), null);
                    if (homePageLayoutById == null) {
                        break;
                    } else {
                        homePageLayoutEntity.setHomePageCardEntityList(homePageLayoutById.getHomePageCardEntityList());
                        homePageLayoutEntity.setName(homePageLayoutById.getName());
                        homePageLayoutEntity.setDescription(homePageLayoutById.getDescription());
                    }
                }
                homePageLayoutEntityList.add(homePageLayoutEntity);
            }
            stopWatch.lap("over getAllEmployeeHomePageLayout");
        } else {
            homePageLayoutEntityList = pageSystemService.getUserPageList(tenantId, employeeId, appType, appId);
            homePageLayoutEntityList = homePageLayoutEntityList.stream().filter(homePageLayoutEntity -> appId.equals(homePageLayoutEntity.getAppId())).filter(x -> x.getStatus() == Status.ENABLE).collect(Collectors.toList());
        }
        // 对获取到的首页按时间做排序
        List<HomePageLayoutEntity> entityList = PageTemplateUtil.sortHomePageWithCreateTime(homePageLayoutEntityList);
        stopWatch.lap("over sortHomePageWithCreateTime");
        List<HomePageLayoutTO> homePageLayoutList = buildHomePageLayoutEntityList(tenantId, entityList, locale);
        stopWatch.lap("over buildHomePageLayoutEntityList");
        List<HomePageLayoutTO> homePageLayoutTOS = setCurrentHomePage(tenantId, employeeId, appId, homePageLayoutList);
        stopWatch.lap("over setCurrentHomePage");
        stopWatch.stop("over getEmployeeHomePageLayoutList");
        return homePageLayoutTOS;
    }

    @Override
    public List<HomePageLayoutTO> getHomePageLayoutList(int tenantId, int appType, String appId, Integer applyType, Locale locale) {

        List<HomePageLayoutEntity> homePageLayoutEntityList;
        if (BizType.CRM.getType() == appType) {
            homePageLayoutEntityList = homePageLayoutDao.getHomePageLayoutList(tenantId, appId);
        } else if (BizType.CUSTOMER.getType() == appType) {
            if (applyType == null) {    // 默认情况就是企业内部的应用
                applyType = 0;
            }
            Integer finalApplyType = applyType;
            Filter<Query> filter = new Filter<Query>() {

                @Override
                public void addFilter(Query query) {
                    query.filter("appType", appType);
                    query.field("status").notEqual(Status.TEMPORARY);
                    query.filter("applyType", finalApplyType);
                }
            };
            homePageLayoutEntityList = homePageLayoutDao.getHomePageLayoutListByFilter(tenantId, filter);
        } else {
            homePageLayoutEntityList = pageSystemService.queryTenantPageList(tenantId, appType, appId);
        }
        // 对获取到的首页按时间做排序
        List<HomePageLayoutEntity> entityList = PageTemplateUtil.sortHomePageWithCreateTime(homePageLayoutEntityList);
        List<HomePageLayoutTO> homePageLayoutList = buildHomePageLayoutEntityList(tenantId, entityList, locale);
        return homePageLayoutList;
    }

    @Override
    public List<HomePageLayoutEntity> queryPreCustomPageListByConfig(int tenantId) {
        Set<String> transWorkBenchSupportPreCustomPageAppIds = DefaultTenantConfig.getTransWorkBenchSupportPreCustomPageAppIds();
        return pageSystemService.batchQueryPreCustomPageList(tenantId, transWorkBenchSupportPreCustomPageAppIds);
    }

    @Override
    public List<HomePageLayoutTO> queryPreCustomPageListByConfigConvertDTO(int tenantId, Locale locale) {
        List<HomePageLayoutEntity> homePageLayoutEntities = queryPreCustomPageListByConfig(tenantId);
        return buildHomePageLayoutEntityList(tenantId, homePageLayoutEntities, locale);
    }

    @Override
    public long getPageCount(String appId, int tenantId, int layoutType, int employeeId) {
        if (layoutType == LayoutType.ENTERPRISE) {
            employeeId = 0;
        }
        return homePageLayoutDao.getPageCount(appId, tenantId, layoutType, employeeId);
    }

    @Override
    public boolean deleteHomePageLayout(int tenantId, String layoutId, int employeeId) {
        HomePageLayoutEntity homePageLayoutEntity = homePageLayoutDao.updateHomePageStatus(layoutId, Status.TEMPORARY, employeeId);
        if (homePageLayoutEntity != null) {
            return true;
        }
        return false;
    }

    @Override
    public HomePageLayoutTO getHomePageLayoutByName(int tenantId, int employeeId, String appId, String name, int layoutType) {
        HomePageLayoutEntity homePageLayoutEntity = homePageLayoutDao.getHomePageLayoutByName(tenantId, employeeId, appId, name, layoutType);
        if (homePageLayoutEntity == null) {
            return null;
        }
        HomePageLayoutTO homePageLayoutTO = HomePageLayoutUtil.buildHomePageLayoutItem(homePageLayoutEntity);
        logger.debug("insertHomePageLayout homePageLayoutTO {}", homePageLayoutTO);
        return homePageLayoutTO;
    }

    @Override
    public boolean updateHomePageStatus(int tenantId, int appType, String layoutId, String apiName, int status, int employeeId) {
        HomePageLayoutEntity homePageLayoutEntity;
        if (BizType.BI.getType() == appType) {
            HomePageLayoutEntity pageLayoutEntity = pageSystemService.getPageEntityByLayoutId(tenantId, layoutId);
            pageLayoutEntity.setStatus(status);
            pageLayoutEntity.setUpdaterId(employeeId);
            pageLayoutEntity.setUpdateTime(new Date());
            homePageLayoutEntity = pageSystemService.createOrUpdatePage(tenantId, layoutId, pageLayoutEntity);
        } else if (BizType.CUSTOMER.getType() == appType) {
            homePageLayoutEntity = homePageLayoutDao.updateHomePageStatus(tenantId, apiName, status, employeeId);
        } else {
            homePageLayoutEntity = homePageLayoutDao.updateHomePageStatus(layoutId, status, employeeId);
        }

        if (Status.TEMPORARY == status && homePageLayoutEntity != null) {
            dealRelationService.delCusCompRelation(tenantId, homePageLayoutEntity.getAppId(), homePageLayoutEntity.getApiName());
        }

        if (homePageLayoutEntity != null) {
            return true;
        }
        return false;
    }

    // 前后台列表list的entity转化
    private List<HomePageLayoutTO> buildHomePageLayoutEntityList(int tenantId, List<HomePageLayoutEntity> homePageLayoutEntityList, Locale locale) {
        List<HomePageLayoutTO> homePageLayoutTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(homePageLayoutEntityList)) {
            return homePageLayoutTOList;
        }

        List<String> apiNames = homePageLayoutEntityList.stream().filter(x -> x.getAppType() == BizType.BI.getType()).map(x -> x.getAppId()).collect(Collectors.toList());
        List<Integer> creators = homePageLayoutEntityList.stream().map(homePageLayoutEntity -> homePageLayoutEntity.getCreatorId()).distinct().collect(Collectors.toList());
        Map<Integer, String> employeeName = remoteService.getEmployeeName(tenantId, creators);
        Map<String, String> displayName = objectService.getDisplayName(tenantId, apiNames, locale);

        homePageLayoutEntityList.stream().forEach(homePageLayoutEntity -> {
            if (SourceType.SYSTEM.equals(homePageLayoutEntity.getSourceType()) && !homePageLayoutEntity.isChange() && homePageLayoutEntity.getLayoutType() == LayoutType.SYSTEM) {
                HomePageLayoutEntity homePageLayoutById = homePageLayoutDao.getHomePageLayoutById(homePageLayoutEntity.getSourceId(), null);
                if (homePageLayoutById != null) {
                    homePageLayoutEntity.setName(homePageLayoutById.getName());
                    homePageLayoutEntity.setDescription(homePageLayoutById.getDescription());
                    homePageLayoutEntity.setHomePageCardEntityList(homePageLayoutById.getHomePageCardEntityList());
                    homePageLayoutEntity.setCustomerLayout(homePageLayoutById.getCustomerLayout());
                } else {
                    return;
                }
            }
            HomePageLayoutTO homePageLayoutTO = HomePageLayoutUtil.buildHomePageLayoutItem(homePageLayoutEntity);
            if (homePageLayoutEntity.getAppType() == BizType.BI.getType()) {
                homePageLayoutTO.setApiName(homePageLayoutEntity.getAppId());
                if (Objects.isNull(displayName.get(homePageLayoutEntity.getAppId()))) {
                    return;
                } else {
                    homePageLayoutTO.setDisplayName(displayName.get(homePageLayoutEntity.getAppId()));
                }
            }
            if (BizType.CUSTOMER.getType() == homePageLayoutEntity.getAppType()) {
                homePageLayoutTO.setCreator(employeeName.get(homePageLayoutEntity.getCreatorId()));
            }
            homePageLayoutTOList.add(homePageLayoutTO);
        });
        // 对首页相关数据支持多语
        setHomePageLayoutName(tenantId, homePageLayoutTOList, locale);
        return homePageLayoutTOList;
    }

    @Override
    public int getCreatorId(String layoutId, String apiName, int tenantId, int appType) {
        HomePageLayoutEntity homePageLayoutEntity;
        if (BizType.CUSTOMER.getType() == appType) {
            homePageLayoutEntity = homePageLayoutDao.getHomePageByTenantIdAndApiName(tenantId, apiName, null);
        } else {
            homePageLayoutEntity = homePageLayoutDao.getCreatorId(layoutId);
        }
        if (homePageLayoutEntity != null) {
            return homePageLayoutEntity.getCreatorId();
        }
        return 0;
    }

    @Override
    public List<HomePageLayoutEntity> copyHomePage(int fromTenantId, int toTenantId, int appType) {
        // 先删除toTenantId的首页数据
        homePageLayoutDao.deleteCustomerPage(toTenantId, appType);

        // 对租户级的首页进行复制
        Filter<Query> filter = new Filter<Query>() {

            @Override
            public void addFilter(Query query) {
                query.filter("appType", appType);
            }
        };
        List<HomePageLayoutEntity> homePageLayoutList = homePageLayoutDao.getHomePageLayoutListByFilter(fromTenantId, filter);
        homePageLayoutList = homePageLayoutList.stream().filter(homePageLayoutEntity -> LayoutType.PERSONAL != homePageLayoutEntity.getLayoutType()).collect(Collectors.toList());
        logger.info("copy homePageList:{}", homePageLayoutList);
        List<HomePageLayoutEntity> homePageLayoutEntityList = Lists.newArrayList();

        for (HomePageLayoutEntity homePageLayoutEntity : homePageLayoutList) {
            EmployeeConfigEntity employeeConfig = employeeConfigDao.getEmployeeConfig(homePageLayoutEntity.getLayoutId(), fromTenantId, 0, EmployeeConstant.UserDefindSelect);
            homePageLayoutEntity.setLayoutId(TempleIdUtil.generateId(toTenantId, homePageLayoutEntity.getLayoutId(), TempleIdUtil.SEPARATOR));
            HomePageLayoutEntity modifyHomePage = homePageLayoutDao.findAndModifyHomePage(toTenantId, homePageLayoutEntity);
            if (employeeConfig != null) {
                EmployeeConfig config = new EmployeeConfig();
                config.setKey(EmployeeConstant.UserDefindSelect);
                config.setValue(employeeConfig.getEValue());
                employeeConfigDao.upsertEmployeeConfig(toTenantId, 0, homePageLayoutEntity.getLayoutId(), config);
            }
            homePageLayoutEntityList.add(modifyHomePage);
        }

        logger.info("copy over homePageLayoutEntityList fromTenantId:{}, toTenantId:{}, appType:{}, size:{}",
                fromTenantId, toTenantId, appType, homePageLayoutEntityList.size());
        return homePageLayoutEntityList;
    }

    @Override
    public int destroyHomePage(int tenantId) {
        List<HomePageLayoutEntity> homePageLayoutList = homePageLayoutDao.getHomePageLayoutList(tenantId, WebPageConstants.APP_CRM);
        List<String> deleteHomePageList = Lists.newArrayList();
        List<HomePageLayoutEntity> recoverHomePageList = Lists.newArrayList();
        homePageLayoutList.stream().forEach(homePageLayoutEntity -> {
            if (SourceType.SYSTEM.equals(homePageLayoutEntity.getSourceType())) {
                recoverHomePageList.add(homePageLayoutEntity);
            } else {
                deleteHomePageList.add(homePageLayoutEntity.getLayoutId());
            }
        });
        int deleteHomePageCount = deleteHomePage(deleteHomePageList);
        logger.info("deleteHomePage count:{}", deleteHomePageCount);
        int recoverHomePageCount = recoverHomePage(recoverHomePageList);
        logger.info("recoverHomePage count:{}", recoverHomePageCount);
        return recoverHomePageCount;
    }

    private int deleteHomePage(List<String> layoutIds) {
        if (CollectionUtils.isEmpty(layoutIds)) {
            return 0;
        }
        layoutIds.stream().forEach(layoutId -> {
            HomePageLayoutEntity homePageLayoutEntity = homePageLayoutDao.updateHomePageStatus(layoutId, Status.TEMPORARY, 0);
            logger.info("deleteHomePage homePageLayoutEntity:{}", homePageLayoutEntity);
        });
        return layoutIds.size();
    }

    private int recoverHomePage(List<HomePageLayoutEntity> recoverHomePageList) {
        if (CollectionUtils.isEmpty(recoverHomePageList)) {
            return 0;
        }
        recoverHomePageList.stream().forEach(homePageLayoutEntity -> {
            homePageLayoutEntity.setHomePageCardEntityList(Lists.newArrayList());
            homePageLayoutEntity.setChange(false);
            logger.info("recoverHomePage homePageLayoutEntity:{}", homePageLayoutEntity);
        });
        return recoverHomePageList.size();
    }

    private List<HomePageLayoutTO> setCurrentHomePage(int tenantId, int employeeId, String appId, List<HomePageLayoutTO> homePageLayoutTOList) {
        if (CollectionUtils.isEmpty(homePageLayoutTOList)) {
            return Lists.newArrayList();
        }
        if (StringUtils.isEmpty(appId)) {
            appId = Constant.APP_CRM;
        }
        String layoutId = userHomePageBaseService.getCurrentHomePageLayoutId(tenantId, employeeId, appId, HomePageConstant.web);
        boolean currentHomePageLayout = false;
        for (HomePageLayoutTO homePageLayoutTO : homePageLayoutTOList) {
            if (homePageLayoutTO.getLayoutId().equals(layoutId)) {
                currentHomePageLayout = true;
                homePageLayoutTO.setCurrentLayout(currentHomePageLayout);
                break;
            }
        }
        if (!currentHomePageLayout) {
            if (CollectionUtils.isNotEmpty(homePageLayoutTOList)) {
                homePageLayoutTOList.get(0).setCurrentLayout(true);
                userHomePageBaseService.updateEmployeeCurrentHomePageLayout(homePageLayoutTOList.get(0).getLayoutId(), tenantId, employeeId, appId, HomePageConstant.web);
            }
        }

        return homePageLayoutTOList;
    }

    @Override
    public List<PageData> getPageData(DataSourceEnv env, int tenantId, String appId) {
        int applyType = 0;
        if (!env.isType()) {
            applyType = 1;
        }
        int finalApplyType = applyType;
        Filter<Query> filter = new Filter<Query>() {
            @Override
            public void addFilter(Query query) {
                query.filter("appType", BizType.CUSTOMER.getType());
                query.filter("applyType", finalApplyType);
                if (StringUtils.isNotBlank(appId)) {
                    query.filter("appId", appId);
                }
            }
        };
        List<HomePageLayoutEntity> homePageLayoutEntityList = homePageLayoutDao.getHomePageLayoutListByFilter(tenantId, filter);
        List<PageData> pageData = HomePageLayoutUtil.buildCusMenuItems(homePageLayoutEntityList);
        return pageData;
    }

    @Override
    public void deleteHomePageLayouts(int tenantId, int appType, String appId) {
        homePageLayoutDao.deleteHomePageLayouts(tenantId, appType, appId);
    }

    @Override
    public Boolean queryEnablePersonPageConfig(int tenantId) {
        TenantConfigEntity tenantValueByKey = tenantConfigDao.getTenantValueByKey(tenantId, TenantConfigKey.PERSON_PAGE_CONFIGURATION);
        if (tenantValueByKey == null || StringUtils.isEmpty(tenantValueByKey.getValue())) {
            return true;
        }
        return StringUtils.equals(tenantValueByKey.getValue(), Boolean.TRUE.toString());
    }

    @Override
    public void updatePersonPageConfig(int tenantId, boolean value) {
        tenantConfigDao.setTenantValue(tenantId, TenantConfigKey.PERSON_PAGE_CONFIGURATION, value ? Boolean.TRUE.toString() : Boolean.FALSE.toString());
    }

    @Override
    public List<HomePageLayoutTO> queryEnableHomePageLayouts(int tenantId, int appType, String appId, int applyType) {
        List<HomePageLayoutEntity> homePageLayoutEntities = homePageLayoutDao.getEnableHomePageLayoutList(tenantId, appType, appId, applyType);
        return homePageLayoutEntities.stream().map(HomePageLayoutUtil::buildHomePageLayoutItem).collect(Collectors.toList());
    }

    @Override
    public UserHomePageLayoutAO queryUserHomePageLayoutBySourceId(int enterpriseId, String layoutId) {
        HomePageLayoutEntity homePageLayoutEntity = homePageLayoutDao.getHomePageLayoutBySourceId(enterpriseId, layoutId, SourceType.EDIT_TENANT);
        return HomePageLayoutUtil.covert2UserHomePageLayoutAO(homePageLayoutEntity);
    }

    @Override
    public HomePageLayoutTO getUserHomePageLayoutBySourceId(UserInfo userInfo, OuterUserInfo outerUserInfo, String layoutId, Locale locale) {
        Integer enterpriseId = userInfo.getEnterpriseId();
        SlowLog stopWatch = GlobalStopWatch.create("getUserHomePageLayoutBySourceId", 100L);
        HomePageLayoutEntity homePageLayoutEntity = homePageLayoutDao.getHomePageLayoutBySourceId(enterpriseId, layoutId, SourceType.EDIT_TENANT);
        if (homePageLayoutEntity == null) {
            return null;
        }
        HomePageLayoutTO homePageLayout = HomePageLayoutUtil.buildHomePageLayoutItem(homePageLayoutEntity);
        // 对不支持的对象的场景进行过滤以及对widget支持多余的下发
        updateLayoutComponents(enterpriseId, userInfo.getEmployeeId(), outerUserInfo, homePageLayout, locale, stopWatch, false);
        JSONObject customerLayout = covertCustomerManage.convertUserLayout(userInfo, outerUserInfo, layoutId, homePageLayout.getAppType(), homePageLayout.getAppId(), homePageLayout.getCustomerLayout(), homePageLayout.getHomePageLayouts(), locale);
        homePageLayout.setCustomerLayout(customerLayout);
        return homePageLayout;
    }

    @Override
    public String saveUserHomePageLayout(UserInfo userInfo, UserHomePageLayoutAO userHomePageLayoutAO) {
        int enterpriseId = userInfo.getEnterpriseId();
        int employeeId = userInfo.getEmployeeId();
        tempFileToFormalFile(userInfo.getEnterpriseAccount(), employeeId, userHomePageLayoutAO);

        HomePageLayoutEntity entity = HomePageLayoutUtil.buildHomePageLayoutEntity(userHomePageLayoutAO.getAppType(), userHomePageLayoutAO.getAppId(), employeeId, null, HomePageLayoutUtil.covert2HomePageLayoutTO(enterpriseId, userHomePageLayoutAO));
        entity.setApiName(TempleIdUtil.removeTenantIdById(enterpriseId, userHomePageLayoutAO.getAppType(), entity.getLayoutId()));
        HomePageLayoutEntity homePageLayoutEntity = pageSystemService.createOrUpdatePage(enterpriseId, entity.getLayoutId(), entity);
        return homePageLayoutEntity.getLayoutId();
    }

    private void tempFileToFormalFile(String enterpriseAccount, int employeeId, UserHomePageLayoutAO userHomePageLayoutAO) {
        if (userHomePageLayoutAO == null) {
            return;
        }

        if (userHomePageLayoutAO.getDataVersion() == DataVersion.dataVersion_100) {
            return;
        }

        List<JSONObject> customerLayoutList = new ArrayList<>();
        List<JSONObject> newCustomerLayoutList = new ArrayList<>();
        customerLayoutList.add(userHomePageLayoutAO.getCustomerLayout());
        if (CollectionUtils.isNotEmpty(userHomePageLayoutAO.getCustomerLayoutList())) {
            customerLayoutList.addAll(userHomePageLayoutAO.getCustomerLayoutList());
        }
        for (int i = 0; i < customerLayoutList.size(); i++) {
            JSONObject customerLayout = customerLayoutList.get(i);

            JSONObject newCustomerLayout = JSONObject.parseObject(JSONObject.toJSONString(customerLayout));
            if (Objects.isNull(newCustomerLayout)) {
                continue;
            }
            CustomerLayoutHelper customerLayoutHelper = new CustomerLayoutHelper(newCustomerLayout);
            List<JSONObject> components = customerLayoutHelper.getComponents();

            List<JSONObject> newComponents = components.stream().map(x -> {
                if (x.getString(CustomerLayoutField.type).equals(ComponentConstant.NAVIGATE)) {
                    return navigateMenuIconToFormat(enterpriseAccount, employeeId, x);
                } else if (x.getString(CustomerLayoutField.type).equals(ComponentConstant.slideImage)) {
                    return slideImageToFormat(enterpriseAccount, employeeId, x);
                } else {
                    return x;
                }
            }).collect(Collectors.toList());
            customerLayoutHelper.setComponent(newComponents);
            if (i == 0) {
                userHomePageLayoutAO.setCustomerLayout(customerLayoutHelper.getCustomerLayout());
            } else {
                newCustomerLayoutList.add(customerLayoutHelper.getCustomerLayout());
            }
        }
        userHomePageLayoutAO.setCustomerLayoutList(newCustomerLayoutList);
    }

    private JSONObject slideImageToFormat(String enterpriseAccount, int employeeId, JSONObject slideImageJSONObject) {
        JSONArray imgsArray = slideImageJSONObject.getJSONArray(CustomerLayoutField.imgs);

        List<String> slideImageTempFilePath = imgsArray.stream().map(x -> JSONObject.parseObject(String.valueOf(x)).getString(CustomerLayoutField.img)).filter(x -> org.apache.commons.lang.StringUtils.isNotBlank(x)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(slideImageTempFilePath)) {
            throw new WebPageException(InterErrorCode.TN_IMG_NOT_FOUND);
        }

        Map<String, String> newSslideImageMap = tempFileToFormalFile.tempFileToFormalFile(enterpriseAccount, employeeId, slideImageTempFilePath, false);
        JSONArray newImgsArray = new JSONArray();
        imgsArray.forEach(x -> {
            JSONObject img = JSONObject.parseObject(String.valueOf(x));
            img.put(CustomerLayoutField.img, newSslideImageMap.get(img.getString(CustomerLayoutField.img)));
            newImgsArray.add(img);
        });
        slideImageJSONObject.put(CustomerLayoutField.imgs, newImgsArray);
        return slideImageJSONObject;
    }

    private JSONObject navigateMenuIconToFormat(String enterpriseAccount, int employeeId, JSONObject navigateJSONObject) {

        String menusString = navigateJSONObject.getString(CustomerLayoutField.menus);
        Boolean dataSourceEnv = navigateJSONObject.getBoolean(CustomerLayoutField.dataSourceEnv);
        if (dataSourceEnv == null) {
            dataSourceEnv = DataSourceEnv.CROSS.isType();
        }
        if (org.apache.commons.lang.StringUtils.isEmpty(menusString)) {
            return navigateJSONObject;
        }
        JSONArray menus = JSONObject.parseArray(menusString);
        if (CollectionUtils.isEmpty(menus)) {
            return navigateJSONObject;
        }
        List<String> newIconTempFilePath = getNewIconTempFilePath(menus);
        Map<String, String> newIconMap = tempFileToFormalFile.tempFileToFormalFile(enterpriseAccount, employeeId, newIconTempFilePath, dataSourceEnv);

        List<JSONObject> newMenuJSONObject = menus.stream().map(x -> {
            JSONObject menuJSONObject = JSONObject.parseObject(JSONObject.toJSONString(x));
            String newIcon = menuJSONObject.getString(CustomerLayoutField.newIcon);
            if (org.apache.commons.lang.StringUtils.isNotEmpty(newIcon) && newIcon.startsWith("TN_")) {
                menuJSONObject.put(CustomerLayoutField.newIcon, newIconMap.get(newIcon));
            }
            return menuJSONObject;
        }).collect(Collectors.toList());
        navigateJSONObject.put(CustomerLayoutField.menus, newMenuJSONObject);
        return navigateJSONObject;
    }

    private List<String> getNewIconTempFilePath(JSONArray menus) {

        if (CollectionUtils.isEmpty(menus)) {
            return Lists.newArrayList();
        }
        return menus.stream().
                map(jsonObject -> {
                    JSONObject menuJSONObject = JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                    String newIcon = menuJSONObject.getString(CustomerLayoutField.newIcon);
                    if (org.apache.commons.lang.StringUtils.isNotEmpty(newIcon) && newIcon.startsWith("TN_")) {
                        return newIcon;
                    }
                    return null;
                }).
                filter(Objects::nonNull).
                collect(Collectors.toList());
    }

    private CustomerLayout buildCustomerLayout(String appId, UserInfo userInfo, OuterUserInfo outerUserInfo, List<HomePageLayoutCard> homePageLayoutCardList, String layoutId, boolean manager, Locale locale) {

        List<String> cardIds = homePageLayoutCardList.stream().map(HomePageLayoutCard::getCardId).collect(Collectors.toList());
        List<Widget> widgets = widgetsConfig.getWidgetsByCardIds(cardIds);
        Map<String, String> widgetMap = widgets.stream().collect(Collectors.toMap(Widget::getCardId, Widget::getNameI18nKey, (x1, x2) -> x1));
        List<String> nameI18nKeys = CollectionUtils.isEmpty(widgets) ? Lists.newArrayList() : Lists.newArrayList(widgetMap.values());
        Map<String, String> languageMap = languageService.queryLanguageByNameI18nKeys(userInfo.getEnterpriseId(), nameI18nKeys, locale);
        CustomerLayout customerLayout = new CustomerLayout();

        CustomerLayout.Column columnLeft = new CustomerLayout.Column();
        List<String> columnLeftList = Lists.newArrayList();
        columnLeft.setWidth("66%");
        CustomerLayout.Column columnRight = new CustomerLayout.Column();
        List<String> columnRightList = Lists.newArrayList();
        columnRight.setWidth("34%");

        CustomerLayout.Column columnTop = new CustomerLayout.Column();
        List<String> columnTopList = Lists.newArrayList();
        columnTop.setWidth("100%");

        JSONObject components = new JSONObject();
        homePageLayoutCardList = PageTemplateUtil.sortLayoutCard(homePageLayoutCardList);

        for (HomePageLayoutCard homePageLayoutCard : homePageLayoutCardList) {
            if (StringUtils.isEmpty(homePageLayoutCard.getAppId())) {
                homePageLayoutCard.setAppId(appId);
            }
            String nameI18nKey = widgetMap.getOrDefault(homePageLayoutCard.getCardId(), "");
            String cardName = languageMap.getOrDefault(nameI18nKey, homePageLayoutCard.getTitle());
            homePageLayoutCard.setTitle(cardName);
            JSONObject component = componentCovertService.covertHomePageCardV2(appId, homePageLayoutCard);

            List<String> apiNames = Lists.newArrayList(component.keySet());
            String apiName = apiNames.get(0);
            if (homePageLayoutCard.getColumn() == 0) {
                columnLeftList.add(apiName);
            } else {
                columnRightList.add(apiName);
            }
            components.putAll(component);
        }
        List<com.facishare.webpage.customer.api.model.core.customelayout.Filter> filterList = homePageCommonService.convertFilter(userInfo, outerUserInfo, layoutId);
        JSONObject filterComponent = buildFilterComponent(filterList);
        components.putAll(filterComponent);
        columnTopList.add(ComponentConstant.FILTERS);

        CustomerLayout.LayoutData topLayoutData = new CustomerLayout.LayoutData();
        topLayoutData.setColumns(Lists.newArrayList(columnTop));
        List<List<String>> topComponents = new ArrayList<>();
        topComponents.add(columnTopList);
        topLayoutData.setComponents(topComponents);

        CustomerLayout.LayoutData layoutData = new CustomerLayout.LayoutData();
        layoutData.setColumns(Lists.newArrayList(columnLeft, columnRight));
        layoutData.setComponents(Lists.newArrayList(columnLeftList, columnRightList));

        customerLayout.setLayout(Lists.newArrayList(topLayoutData, layoutData));
        customerLayout.setComponents(components);
        if (manager) {
            PageTemplateConfigVO pageTemplateConfig = this.pageTemplateConfig.getPageTemplateConfig(appId);
            boolean cross = pageTemplateConfig != null && pageTemplateConfig.isCrossApp();
            customerLayout.setFilters(employeeConfigCommonService.buildManagerFilters(userInfo.getEnterpriseId(), userInfo.getEmployeeId(), layoutId, cross));
        } else {
            customerLayout.setFilters(filterList);
        }
        return customerLayout;
    }

    private List<HomePageLayoutFilter> buildHomePageFilter(UserInfo userInfo, OuterUserInfo outerUserInfo, String appId, List<HomePageLayoutFilter> homePageLayoutFilters, Locale locale) {
        if (CollectionUtils.isEmpty(homePageLayoutFilters)) {
            return Lists.newArrayList();
        }
        return homePageLayoutFilters.stream().map(homePageLayoutFilter -> {
            if (GeneralUtil.checkObj(homePageLayoutFilter.getFilterKey())) {
                String filterMainID = paasOrgGroupService.getTemplateIdByUpOrDown(userInfo, outerUserInfo, appId, homePageLayoutFilter.getFilterKey(), locale);
                homePageLayoutFilter.setFilterMainID(filterMainID);
            } else {
                homePageLayoutFilter.setFilterMainID(homePageLayoutFilter.getFilterKey());
            }

            return homePageLayoutFilter;
        }).collect(Collectors.toList());
    }

    private JSONObject buildFilterComponent(List<com.facishare.webpage.customer.api.model.core.customelayout.Filter> filters) {

        if (CollectionUtils.isEmpty(filters)) {
            return new JSONObject();
        }

        FilterCardCovert filterCardCovert = new FilterCardCovert();
        filterCardCovert.setComponentNameConfig(componentNameConfig);
        filterCardCovert.setFilters(filters);

        return filterCardCovert.buildComponent();
    }

    private void setHomePageLayoutName(int tenantId, List<HomePageLayoutTO> homePageLayoutTOList, Locale locale) {
        // 查出数据库的值, 组装预置key
        List<String> ids = homePageLayoutTOList.stream().map(HomePageLayoutTO::getLayoutId).collect(Collectors.toList());
        Map<String, HomePageLayoutEntity> entities = homePageLayoutDao.getBatchHomePageLayoutByIds(ids, null)
                .stream().collect(Collectors.toMap(HomePageLayoutEntity::getLayoutId, x -> x));
        List<I18nTrans.TransArg> i18nTranArgList = Lists.newArrayList();
        homePageLayoutTOList.forEach(homePageLayoutTO -> {
            List<String> preKeys = Lists.newArrayList(TranslateI18nUtils.getCrmFrontPageKey(homePageLayoutTO.getLayoutId())); // 之前的key
            if (Objects.nonNull(entities.getOrDefault(homePageLayoutTO.getLayoutId(), null))) {
                String sourceId = entities.get(homePageLayoutTO.getLayoutId()).getSourceId();
                if (StringUtils.isNotEmpty(sourceId)) {
                    preKeys.add(WebPageConstants.CRM_HOMEPAGE_PREFIX + TranslateI18nUtils.delAllEi(sourceId));
                }
            }
            preKeys.add(WebPageConstants.CRM_HOMEPAGE_PREFIX + homePageLayoutTO.getName());     // 老的预置key, 已弃用
            i18nTranArgList.add(I18nTrans.TransArg.builder()
                    .name(homePageLayoutTO.getName())
                    .customKey(TranslateI18nUtils.getWebPageNameKey(homePageLayoutTO.getLayoutApiName()))
                    .preKeyList(preKeys)
                    .build());
            i18nTranArgList.add(TranslateI18nUtils.buildWebPageDescriptionTranslateKey(homePageLayoutTO.getDescription(), homePageLayoutTO.getLayoutApiName()));
        });
        Map<String, String> transValueMap = i18nService.getTransValueIncludePreKey(tenantId, i18nTranArgList, locale.toLanguageTag());

        List<String> descriptions = homePageLayoutTOList.stream()
                .filter(homePageLayoutTO -> StringUtils.isNotEmpty(homePageLayoutTO.getDescription()))
                .map(homePageLayoutTO -> WebPageConstants.DESCRIBE_PREFIX + homePageLayoutTO.getDescription())
                .collect(Collectors.toList());
        Map<String, String> homePageLanguageMap = languageService.queryLanguageByNameI18nKeys(tenantId, descriptions, locale);

        homePageLayoutTOList.forEach(homePageLayoutTO -> {
            String nameKey = TranslateI18nUtils.getWebPageNameKey(homePageLayoutTO.getLayoutApiName());
            homePageLayoutTO.setName(transValueMap.getOrDefault(nameKey, homePageLayoutTO.getName()));

            String description = homePageLanguageMap.getOrDefault(WebPageConstants.DESCRIBE_PREFIX + homePageLayoutTO.getDescription(), homePageLayoutTO.getDescription());
            description = transValueMap.getOrDefault(TranslateI18nUtils.getWebPageDescriptionKey(homePageLayoutTO.getLayoutApiName()), description);
            homePageLayoutTO.setDescription(description);
        });
    }

    @Override
    public List<String> buildPreKeyList(JSONObject component, String tenantId, String layoutId, String source, String model) {
        ComponentExt componentExt = ComponentExt.of(component);
        String apiName = component.getString(CustomerLayoutField.apiName);
        String key = TranslateI18nUtils.getWebTemplateWidgetsNameKey(layoutId, apiName);
        List<String> preKeys = Lists.newArrayList(key);
        if (isScene(component)) {   // 场景组件特殊, 不取预置翻译
            return preKeys;
        } else if (isBICard(component)) {
            if (StringUtils.isNotBlank(model)) {
                preKeys.add(WebPageUtils.buildWidgetI18Key(source, model, layoutId, apiName));
            }
            preKeys.addAll(componentExt.getItemI18nKeys());
            // 部分BI组件特殊, 和普通组件一样在nameI18key上
            String nameI18Key = componentExt.getNameI18nKey();
            if (StringUtils.isNotBlank(nameI18Key)) {
                preKeys.add(nameI18Key);
            }
        } else {
            String preKey = componentExt.getNameI18nKey();
            if (StringUtils.isEmpty(preKey)) {
                // 查组件定义找预置key
                Widget widget = widgetsConfig.getWidgetByCardIdOrId(org.apache.commons.lang3.StringUtils.firstNonBlank
                                (component.getString(CustomerLayoutField.id), component.getString(CustomerLayoutField.type)),
                        component.getString(CustomerLayoutField.cardId));
                if (Objects.nonNull(widget)) {
                    preKey = widget.getNameI18nKey();
                    component.put(CustomerLayoutField.nameI18nKey, preKey);
                }
            }
            if (StringUtils.isNotEmpty(preKey)) {
                preKeys.add(preKey);
            }
        }
        return preKeys;
    }

    private void doTranslate(int tenantId, HomePageLayoutTO homePageLayoutTO, Locale locale, boolean onTimeLoadFlag) {
        String model = PageTemplateUtils.getTemplateModel(homePageLayoutTO);
        String source = TempleType.WEB;
        String ei = String.valueOf(tenantId);
        List<JSONObject> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(homePageLayoutTO.getCustomerLayoutList()) && homePageLayoutTO.getPageMultiType() == 1) {
            list = homePageLayoutTO.getCustomerLayoutList();
        } else {
            list.add(homePageLayoutTO.getCustomerLayout());
        }
        String layoutId = homePageLayoutTO.getLayoutId();
        List<I18nTrans.TransArg> i18nTranArgList = new ArrayList<>();
        List<I18nTrans.TransArg> tabTranArgList = new ArrayList<>();
        List<String> i18InfoKeys = new ArrayList<>();
        // 构建翻译keu查询值
        list.forEach(layout -> {
            if (null == layout) {
                return;
            }
            JSONArray components;
            try {
                components = getComponents(layout);
            } catch (Exception e) {
                logger.error("getComponents error! tenantId:{},layout:{}", tenantId, layout, e);
                return;
            }

            if (CollectionUtils.isNotEmpty(components)) {
                components.forEach(component -> {
                    JSONObject jsonObject = (JSONObject) component;
                    ComponentExt componentExt = ComponentExt.of(jsonObject);
                    String newHeader = StringUtils.firstNonBlank(componentExt.getNewHeader(),
                            componentExt.getTitleName(),
                            componentExt.getTitle(),
                            componentExt.getHeader());
                    String i18Key = TranslateI18nUtils.getWebTemplateWidgetsNameKey(homePageLayoutTO.getLayoutId(),
                            jsonObject.getString(CustomerLayoutField.apiName));
                    i18nTranArgList.add(I18nTrans.TransArg.builder()
                            .name(newHeader)
                            .customKey(TranslateI18nUtils.delEiInKey(ei, i18Key))
                            .preKeyList(buildPreKeyList(jsonObject, ei, layoutId, source, model))
                            .build());
                    List<I18nInfo> i18nInfoList = ComponentExt.of(jsonObject).getI18nInfoList(ei, homePageLayoutTO.getLayoutId(), ComponentExt.of(jsonObject).getApiName());
                    if (CollectionUtils.isNotEmpty(i18nInfoList)) {
                        i18InfoKeys.addAll(i18nInfoList.stream().map(I18nInfo::getCustomKey).collect(Collectors.toList()));
                    }

                    // 页签容器的标签
                    if (CustomerLayoutField.TAB.equals(jsonObject.getString(CustomerLayoutField.type))) {
                        JSONArray tabs = jsonObject.getJSONArray(CustomerLayoutField.tabCollection);
                        if (com.facishare.webpage.customer.core.util.CollectionUtils.notEmpty(tabs)) {
                            for (Object tabObject : tabs) {
                                JSONObject tab = JSONObject.parseObject(JSONObject.toJSONString(tabObject));
                                String tabName = tab.getString(CustomerLayoutField.header);
                                String tabsNameKey = ComponentExt.getTabTransKey(ei, homePageLayoutTO.getLayoutId(), componentExt.getApiName(), tab.getString(CustomerLayoutField.apiName));
                                String oldTabKey = TranslateI18nUtils.getWebTemplateWidgetsNameKey(homePageLayoutTO.getLayoutId(), tab.getString(CustomerLayoutField.apiName));
                                tabTranArgList.add(TranslateI18nUtils.convertToTransArg(tabsNameKey, Lists.newArrayList(oldTabKey), Lists.newArrayList(), tabName));
                            }
                        }
                    }
                });
            }
        });
        Map<String, String> transValueMap = Maps.newHashMap();
        if (WebPageGraySwitch.isGetTabsTransValueByOldKeyGrayEi(ei)) {
            transValueMap.putAll(i18nService.getTransValueIncludePreKeyV2(tenantId, tabTranArgList, locale.toLanguageTag()));
        } else {
            i18nTranArgList.addAll(tabTranArgList);
        }
        transValueMap.putAll(i18nService.getTransValueIncludePreKey(tenantId, i18nTranArgList, locale.toLanguageTag()));
        Map<String, Localization> i18nInfosLanguageMap = i18nService.getLocalizationByKey(tenantId, i18InfoKeys, true, onTimeLoadFlag);
        list.forEach(layout ->
        {
            if (null == layout) {
                return;
            }
            JSONArray components;
            try {
                components = getComponents(layout);
            } catch (Exception e) {
                logger.error("getComponents error! tenantId:{},layout:{}", tenantId, layout, e);
                return;
            }
            if (CollectionUtils.isNotEmpty(components)) {
                components.forEach(component -> {
                    JSONObject jsonObject = (JSONObject) component;
                    String i18Key = TranslateI18nUtils.getWebTemplateWidgetsNameKey(homePageLayoutTO.getLayoutId(), jsonObject.getString(CustomerLayoutField.apiName));
                    i18Key = TranslateI18nUtils.delEiInKey(ei, i18Key);
                    String widgetName = transValueMap.get(i18Key);
                    if (StringUtils.isNotBlank(widgetName)) {
                        jsonObject.put(CustomerLayoutField.newHeader, widgetName);
                        jsonObject.put(CustomerLayoutField.titleName, widgetName);
                        String type = jsonObject.getString(CustomerLayoutField.type);
                        if ("sceneCard".equals(type)) {
                            // 场景组件前端取的是title字段
                            jsonObject.put(CustomerLayoutField.title, widgetName);
                        }
                        if (!checkService.checkGoNewCRM(tenantId)) {
                            if ("grid".equals(type)) {
                                // 老版CRM首页 栅格组件右侧组件设置前端取的是header字段
                                jsonObject.put(CustomerLayoutField.header, widgetName);
                            }
                        }
                    }
                    List<I18nInfo> i18nInfoList = ComponentExt.of(jsonObject).getI18nInfoList(ei, homePageLayoutTO.getLayoutId(), ComponentExt.of(jsonObject).getApiName());
                    if (CollectionUtils.isNotEmpty(i18nInfoList)) {
                        List<I18nInfo> i18nInfos = Lists.newArrayList();
                        i18nInfoList.forEach(i18nInfo -> i18nInfos.add(i18nInfo.buildByTransValue(tenantId, i18nInfosLanguageMap, locale.toLanguageTag())));
                        ComponentExt.of(jsonObject).setI18nInfo(i18nInfos);
                    }
                    if (CustomerLayoutField.TAB.equals(jsonObject.getString(CustomerLayoutField.type))) {
                        ComponentExt componentExt = ComponentExt.of(jsonObject);
                        // 页签容器的标签
                        JSONArray tabs = jsonObject.getJSONArray(CustomerLayoutField.tabCollection);
                        JSONArray newTabs = new JSONArray();
                        if (com.facishare.webpage.customer.core.util.CollectionUtils.notEmpty(tabs)) {
                            for (Object tabObject : tabs) {
                                JSONObject tab = JSONObject.parseObject(JSONObject.toJSONString(tabObject));
                                String i18KeyTab = ComponentExt.getTabTransKey(ei, homePageLayoutTO.getLayoutId(), componentExt.getApiName(), tab.getString(CustomerLayoutField.apiName));
                                String tabName = transValueMap.get(i18KeyTab);
                                if (StringUtils.isNotBlank(tabName)) {
                                    tab.put(CustomerLayoutField.header, tabName);
                                }
                                newTabs.add(tab);
                            }
                        }
                        jsonObject.put(CustomerLayoutField.tabCollection, newTabs);
                    }
                });
            }
        });
        // web顶导航
        List<JSONObject> customerLayoutList = homePageLayoutTO.getCustomerLayoutList();
        customerLayoutList.removeAll(Collections.singleton(null));
        if (CollectionUtils.isNotEmpty(customerLayoutList)) {
            List<I18nTrans.TransArg> transArgList = new ArrayList<>();
            customerLayoutList.forEach(customerLayout -> {
                if (null == customerLayout) {
                    return;
                }
                if (customerLayout.containsKey("labelPageName")) {
                    String translateKey = TranslateI18nUtils.getLabelPageNameKey(homePageLayoutTO.getLayoutId(), customerLayout.getString("labelIndex"));
                    transArgList.add(TranslateI18nUtils.buildArgKeyWithoutEi(ei, translateKey, customerLayout.getString("labelPageName")));
                }
            });
            Map<String, String> transLateMap = i18nService.getTransValueIncludePreKey(tenantId, transArgList, locale.toLanguageTag());
            customerLayoutList.forEach(customerLayout -> {
                if (null == customerLayout) {
                    return;
                }
                if (customerLayout.containsKey("labelPageName")) {
                    String translateKey = TranslateI18nUtils.delEiInKey(String.valueOf(tenantId),
                            TranslateI18nUtils.getLabelPageNameKey(homePageLayoutTO.getLayoutId(), customerLayout.getString("labelIndex")));
                    if (StringUtils.isNotBlank(transLateMap.get(translateKey))) {
                        customerLayout.put("labelPageName", transLateMap.get(translateKey));
                    }
                }
            });
        }

        homePageLayoutTO.setCustomerLayout(JSONObject.parseObject(JSONObject.toJSONString(list.get(0))));
        homePageLayoutTO.setCustomerLayoutList(list);
    }

    private JSONArray getComponents(JSONObject layout) {
        JSONArray result = new JSONArray();
        Object components = layout.get(CustomerLayoutField.components);
        if (components instanceof JSONArray) {
            return (JSONArray) components;
        } else if (components instanceof Collection) {
            result.addAll((Collection) components);
        } else if (components instanceof JSONObject) {
            result.addAll(((JSONObject) components).values());
        }
        return result;
    }


    /**
     * 对组件进行过滤
     *
     * @param tenantId
     * @param employeeId
     * @param outerUserInfo
     * @param homePageLayoutTO
     * @param locale
     * @param stopWatch
     * @param manager
     */
    private void updateLayoutComponents(int tenantId, Integer employeeId, OuterUserInfo outerUserInfo, HomePageLayoutTO homePageLayoutTO, Locale locale, SlowLog stopWatch, boolean manager) {

        if (homePageLayoutTO == null) {
            return;
        }

        int dataVersion = homePageLayoutTO.getDataVersion();
        String layoutId = homePageLayoutTO.getLayoutId();
        if (dataVersion == DataVersion.dataVersion_100) {
            List<HomePageLayoutCard> homePageLayouts = homePageLayoutTO.getHomePageLayouts();
            // 更新widget
            List<HomePageLayoutCard> newHomePageLayouts = updateOldLayoutComponents(tenantId, homePageLayouts);
            stopWatch.lap("updateOldLayoutComponents");
            homePageLayoutTO.setHomePageLayouts(newHomePageLayouts);
        }
        else if (dataVersion == DataVersion.dataVersion_200) {
            JSONObject newCustomerLayout = updateNewLayoutComponents(tenantId, employeeId, outerUserInfo,
                    homePageLayoutTO.getAppId(), homePageLayoutTO.getCustomerLayout(),
                    locale, stopWatch, manager);
            homePageLayoutTO.setCustomerLayout(newCustomerLayout);
            homePageLayoutTO.getCustomerLayoutList().removeAll(Collections.singleton(null));
            if (CollectionUtils.isNotEmpty(homePageLayoutTO.getCustomerLayoutList())) {
                List<JSONObject> list = new ArrayList<>();
                homePageLayoutTO.getCustomerLayoutList().stream().filter(Objects::nonNull).forEach(x -> {
                    JSONObject newCustomerLayout1 = updateNewLayoutComponents(tenantId, employeeId, outerUserInfo,
                            homePageLayoutTO.getAppId(), x, locale, stopWatch, manager);
                    list.add(newCustomerLayout1);
                });
                homePageLayoutTO.setCustomerLayoutList(list);
            }
        }
    }

    private JSONObject updateNewLayoutComponents(int tenantId, Integer employeeId, OuterUserInfo outerUserInfo,
                                                 String appId, JSONObject customerLayout, Locale locale,
                                                 SlowLog stopWatch, boolean manager) {
        if (Objects.isNull(customerLayout)) {
            return null;
        }
        CustomerLayoutHelper customerLayoutHelper = new CustomerLayoutHelper(customerLayout);
        List<JSONObject> components = customerLayoutHelper.getComponents();
        if (CollectionUtils.isEmpty(components)) {
            return customerLayout;
        }

        List<String> nameI18nKeys = components.stream().map(x -> {
            String nameI18nKey = x.getString(CustomerLayoutField.nameI18nKey);
            // bi的组件在最外层统一赋值，无需在这儿处理，itemI18nKeys不为空代表是bi组件
            if (StringUtils.isEmpty(nameI18nKey) && CollectionUtils.isEmpty(x.getJSONArray(CustomerLayoutField.itemI18nKeys))) {
                Widget widget = widgetsConfig.getWidgetByCardIdOrId(org.apache.commons.lang3.StringUtils.firstNonBlank
                                (x.getString(CustomerLayoutField.id), x.getString(CustomerLayoutField.type)),
                        x.getString(CustomerLayoutField.cardId));
                if (widget != null) {
                    nameI18nKey = widget.getNameI18nKey();
                    x.put(CustomerLayoutField.nameI18nKey, nameI18nKey);
                }
            }
            return nameI18nKey;
        }).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        Map<String, String> titleMap = languageService.queryLanguageByNameI18nKeys(tenantId, nameI18nKeys, locale);

        components.forEach(component -> {
            ComponentExt componentExt = ComponentExt.of(component);
            String nameI18nKey = componentExt.getNameI18nKey();
            String value = StringUtils.firstNonBlank(
                    componentExt.getNewHeader(),
                    componentExt.getTitleName(),
                    componentExt.getTitle(),
                    componentExt.getHeader()
            );
            if (StringUtils.isBlank(nameI18nKey) || !titleMap.containsKey(nameI18nKey)) {
                return;
            }
            Localization localization = I18nClient.getInstance().get(nameI18nKey, tenantId);
            if (Objects.isNull(localization)) {
                return;
            }
            if (Objects.nonNull(localization.getData())) {
                if (!localization.getData().containsValue(value)) {    // 改过名, 需修正titleMap
                    titleMap.put(nameI18nKey, value);
                }
            }
        });

        stopWatch.lap("queryLanguageByNameI18nKeys");
        // 对components进行过滤
        List<JSONObject> newComponents = filterLayoutComponentService.filterLayoutComponents(tenantId, employeeId, outerUserInfo, appId, components, locale, manager);
        customerLayoutHelper.setComponent(newComponents, titleMap, "");
        stopWatch.lap("setComponent");

        List<String> apiNames = newComponents.stream().map(x -> x.getString(CustomerLayoutField.apiName)).collect(Collectors.toList());
        customerLayoutHelper.setLayoutComponent(apiNames);

        return customerLayoutHelper.getCustomerLayout();
    }


    private List<HomePageLayoutCard> updateOldLayoutComponents(int tenantId, List<HomePageLayoutCard> homePageLayouts) {

        if (CollectionUtils.isEmpty(homePageLayouts)) {
            return Lists.newArrayList();
        }
        // 获取场景相关的widget
        List<HomePageLayoutCard> filterCards = homePageLayouts.stream().filter(homePageLayoutCard -> homePageLayoutCard.getCardId().equals(CustomerLayoutField.oldFilterCardId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterCards)) {
            return homePageLayouts;
        }
        // 获取所有的对象apiName
        List<String> apiNames = getApiNames(tenantId, Locale.CHINA);
        // 对所有widget中包含场景的进行过滤
        List<HomePageLayoutCard> newHomePageLayouts = homePageLayouts.stream().map(homePageLayoutCard -> filterHomePageLayoutFilter(apiNames, homePageLayoutCard)).filter(homePageLayoutCard -> !CustomerLayoutField.oldFilterCardId.equals(homePageLayoutCard.getCardId()) || CollectionUtils.isNotEmpty(homePageLayoutCard.getHomePageLayoutFilters())).collect(Collectors.toList());
        return newHomePageLayouts;
    }

    private HomePageLayoutCard filterHomePageLayoutFilter(List<String> apiNames, HomePageLayoutCard homePageLayoutCard) {

        if (!homePageLayoutCard.getCardId().equals(CustomerLayoutField.oldFilterCardId)) {
            return homePageLayoutCard;
        }
        List<HomePageLayoutFilter> homePageLayoutFilterList = homePageLayoutCard.getHomePageLayoutFilters().stream().filter(homePageLayoutFilter -> apiNames.contains(homePageLayoutFilter.getObjectApiName())).collect(Collectors.toList());
        homePageLayoutCard.setHomePageLayoutFilters(homePageLayoutFilterList);
        return homePageLayoutCard;
    }


    private List<String> getApiNames(int tenantId, Locale locale) {
        List<SimpObjectDescription> allDescribe = objectService.getAllDescribe(tenantId, locale);
        if (CollectionUtils.isEmpty(allDescribe)) {
            return Lists.newArrayList();
        }
        return allDescribe.stream().map(SimpObjectDescription::getApiName).collect(Collectors.toList());
    }

}
