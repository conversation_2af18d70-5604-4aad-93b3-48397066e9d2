package com.facishare.webpage.customer.api.model.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Locale;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryCustomerPageWidgetsArg implements Serializable {

    private int enterpriseId;
    private String source;
    private Integer applyType;
    private Locale locale;
    private boolean includePreCustomPage;
}
