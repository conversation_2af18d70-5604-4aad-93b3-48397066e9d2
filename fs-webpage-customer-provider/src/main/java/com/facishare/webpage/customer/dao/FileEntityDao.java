package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.dao.entity.FileEntity;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件DAO接口
 */
public interface FileEntityDao {

    /**
     * 根据ApiName查询文件（包括禁用状态的）
     *
     * @param tenantId 租户ID
     * @param apiName  文件ApiName
     * @return 文件实体
     */
    FileEntity findByApiNameIncludeDisable(Integer tenantId, String apiName);

    /**
     * 根据ApiName查询文件
     *
     * @param tenantId 租户ID
     * @param apiName  文件ApiName
     * @return 文件实体
     */
    FileEntity findByApiName(Integer tenantId, String apiName);

    /**
     * 根据工作区查询文件列表
     *
     * @param tenantId         租户ID
     * @param workSpaceApiName 工作区ApiName
     * @return 文件列表
     */
    List<FileEntity> findByWorkSpace(Integer tenantId, String workSpaceApiName);

    Map<String, List<FileEntity>> batchFindByWorkSpace(Integer tenantId, Collection<String> workSpaceApiNameList);

    List<FileEntity> findByCondition(FileEntity condition, int offset, int limit);

    /**
     * 根据工作区和父文件夹查询文件列表
     *
     * @param tenantId         租户ID
     * @param workSpaceApiName 工作区ApiName
     * @param parentId         父文件夹ID
     * @return 文件列表
     */
    List<FileEntity> findByWorkSpace(Integer tenantId, String workSpaceApiName, String parentId);

    /**
     * 根据父文件夹查询文件列表
     *
     * @param tenantId      租户ID
     * @param parentApiName 父文件夹ApiName
     * @return 文件列表
     */
    List<FileEntity> findByParent(Integer tenantId, String parentApiName);

    /**
     * 根据ApiName列表批量查询文件
     *
     * @param tenantId 租户ID
     * @param apiNames 文件ApiName列表
     * @return 文件列表
     */
    List<FileEntity> findByApiNames(Integer tenantId, Collection<String> apiNames);

    List<FileEntity> findByApiNamesIncludesDelete(Integer tenantId, Collection<String> apiNames);


    /**
     * 保存文件
     *
     * @param user   用户信息
     * @param entity 文件实体
     * @return 保存后的文件实体
     */
    FileEntity save(User user, FileEntity entity);

    /**
     * 批量保存文件
     *
     * @param user     用户信息
     * @param entities 文件实体列表
     * @return 保存后的文件实体列表
     */
    List<FileEntity> batchSave(User user, List<FileEntity> entities);


    /**
     * 更新文件
     *
     * @param user   用户信息
     * @param entity 文件实体
     * @return 更新后的文件实体
     */
    FileEntity update(User user, FileEntity entity);


    /**
     * 批量更新文件状态
     *
     * @param user     用户信息
     * @param apiNames 文件ApiName列表
     * @param status   状态
     */
    void batchUpdateStatus(User user, Collection<String> apiNames, Integer status);

    /**
     * 移动文件
     *
     * @param user     用户信息
     * @param apiName  文件ApiName
     * @param parentId 父文件夹ID
     * @return 移动后的文件实体
     */
    FileEntity moveFile(User user, String apiName, String parentId);

    /**
     * 删除文件
     *
     * @param apiName 文件ApiName
     */
    void delete(User user, String apiName);

    void deleteByWorkSpace(User user, String workSpaceApiName);


    void batchDelete(User user, List<FileEntity> allFile);

    void batchUpdate(User user, List<FileEntity> updatedEntities);

    long findCountByCondition(FileEntity condition);

    List<FileEntity> batchFindFileByApiNames(int tenantId, List<String> apiNameList);
}
