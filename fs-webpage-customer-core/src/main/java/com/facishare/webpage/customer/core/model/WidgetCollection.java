package com.facishare.webpage.customer.core.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.webpage.customer.api.model.core.TenantPrivilege;
import lombok.Data;

import java.util.List;

/**
 * Created by she<PERSON> on 19/12/10.
 */
@Data
public class WidgetCollection {

    @JSONField(ordinal = 1)
    private String id;

    @JSONField(ordinal = 2)
    private String name;

    @JSONField(ordinal = 3)
    private String nameI18nKey;

    @JSONField(ordinal = 4)
    private TenantPrivilege tenantPrivilege;

    @JSONField(ordinal = 5)
    private List<String> widgets;

    @JSONField(ordinal = 6)
    private int widgetSourceType;///1:来源自定义组件
}
