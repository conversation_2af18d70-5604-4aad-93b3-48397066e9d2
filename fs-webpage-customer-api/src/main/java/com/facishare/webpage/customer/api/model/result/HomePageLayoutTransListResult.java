package com.facishare.webpage.customer.api.model.result;

import com.fxiaoke.i18n.client.api.Localization;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
public class HomePageLayoutTransListResult implements Serializable {
    @SerializedName("HomePageLayoutTransList")
    private List<HomePageLayoutTrans> homePageLayoutTransList;

    @Data
    public static class  HomePageLayoutTrans implements Serializable {
        @SerializedName("apiName")
        private String apiName;
        @SerializedName("layoutId")
        private String layoutId;
        @SerializedName("name")
        private String name;
        @SerializedName("description")
        private String description;
        @SerializedName("descriptionTranslateKey")
        private String descriptionTranslateKey;
        @SerializedName("transKey")
        @Deprecated
        private String transKey;
        @SerializedName("pageMultiType")
        private int pageMultiType;
        @SerializedName("lastModifiedTime")
        private long lastModifiedTime;
        @SerializedName("localization")
        private Localization localization;
        @SerializedName("defaultTranslateKey")
        private String defaultTranslateKey;
        @SerializedName("preKeys")
        private List<String> preKeys;
    }
}
