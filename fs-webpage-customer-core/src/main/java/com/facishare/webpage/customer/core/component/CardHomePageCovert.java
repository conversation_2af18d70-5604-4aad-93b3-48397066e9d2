package com.facishare.webpage.customer.core.component;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.HomePageLayoutCard;
import com.facishare.webpage.customer.api.model.HomePageLayoutTool;
import com.facishare.webpage.customer.core.config.ComponentNameConfig;
import com.facishare.webpage.customer.core.constant.Constant;
import com.facishare.webpage.customer.core.util.CovertUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * Created by zhangyu on 2020/6/24
 */
@Data
public class CardHomePageCovert extends ComponentCovert {

    private HomePageLayoutCard homePageLayoutCard;

    private ComponentNameConfig componentNameConfig;

    private String apiName;

    @Override
    public String getApiName() {
        return apiName;
    }

    @Override
    public String getType() {
        return componentNameConfig.getComponentName(homePageLayoutCard.getCardId());
    }

    @Override
    public int getLimit() {
        return 1;
    }

    @Override
    public JSONObject getProps() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("api_name", apiName);
        setCompFields(jsonObject);
        return jsonObject;
    }

    private void setCompFields(JSONObject ret) {
        ret.putAll(buildData(Constant.WEB, homePageLayoutCard));
    }

    public JSONObject buildData(String type, HomePageLayoutCard homePageLayoutCard) {
        JSONObject data = new JSONObject();
        {
            data.put("cardId", homePageLayoutCard.getCardId());
            data.put("appId", homePageLayoutCard.getAppId());
            data.put("title", homePageLayoutCard.getTitle());
            data.put("dataId", homePageLayoutCard.getCardId());
            data.put("type", buildType(type));
            data.put("propsType", buildType(type));
            data.put("url", homePageLayoutCard.getUrl());
            data.put("tools", buildTools(homePageLayoutCard.getHomePageLayoutTools()));
            data.put("filters", CovertUtil.buildFilters(homePageLayoutCard.getHomePageLayoutFilters()));
        }
        return data;
    }

    private String buildType(String type){
        if (Constant.APP.equals(type)){
            return getType();
        }else {
            return String.valueOf(homePageLayoutCard.getType());
        }
    }

    private static List<JSONObject> buildTools(List<HomePageLayoutTool> homePageLayoutTools) {
        if (CollectionUtils.isEmpty(homePageLayoutTools)) {
            return Lists.newArrayList();
        }
        List<JSONObject> tools = Lists.newArrayList();

        for (HomePageLayoutTool homePageLayoutTool : homePageLayoutTools) {
            JSONObject tool = new JSONObject();
            {
                tool.put("ToolID", homePageLayoutTool.getToolID());
                tool.put("ToolName", homePageLayoutTool.getToolName());
                tool.put("URL", homePageLayoutTool.getUrl());
                tool.put("ToolType", homePageLayoutTool.getToolType());
                tool.put("IsShow", homePageLayoutTool.isShow());
            }
            tools.add(tool);
        }
        return tools;
    }
}
