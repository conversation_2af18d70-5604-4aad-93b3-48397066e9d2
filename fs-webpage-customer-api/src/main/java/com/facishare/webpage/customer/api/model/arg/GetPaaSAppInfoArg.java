package com.facishare.webpage.customer.api.model.arg;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.google.common.base.Strings;
import lombok.Data;

import java.io.Serializable;
import java.util.Locale;

@Data
public class GetPaaSAppInfoArg implements Serializable {
    private int tenantId;
    private String appId;
    private Locale locale;

    public void valid() {
        if (tenantId < 0 || Strings.isNullOrEmpty(appId)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
