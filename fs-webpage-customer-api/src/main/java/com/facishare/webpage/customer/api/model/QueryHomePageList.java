package com.facishare.webpage.customer.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Locale;

public interface QueryHomePageList {


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        private int tenantId;
        private int appType;
        //appId可以为空，自定义页面没有appId
        private String appId;
        //代表是是自定义页面还是渠道自定义页面      * 0：企业内 1、互联
        private Integer applyType;
        private Locale locale;

    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {

        private List<HomePageLayoutTO> homePageLayoutList;
    }
}
