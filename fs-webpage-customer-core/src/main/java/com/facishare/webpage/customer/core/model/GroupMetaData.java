package com.facishare.webpage.customer.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by zhangyu on 2021/1/8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupMetaData {

    private String groupApiName;
    private String groupName;
    private String appId;
    private String afterApiName;
    private String afterType;
    private String afterAppId;
    private String menuAppId;
    private List<String> menus;

}
