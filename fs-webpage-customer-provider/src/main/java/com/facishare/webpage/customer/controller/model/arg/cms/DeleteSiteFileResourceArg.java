package com.facishare.webpage.customer.controller.model.arg.cms;

import lombok.Data;
import java.io.Serializable;

/**
 * 删除站点文件资源参数 Created by cursor.
 */
@Data
public class DeleteSiteFileResourceArg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 站点ApiName
     */
    private String siteApiName;

    /**
     * 站点中的相对路径，从站点定义的根目录开始
     */
    private String relativePath;
}
