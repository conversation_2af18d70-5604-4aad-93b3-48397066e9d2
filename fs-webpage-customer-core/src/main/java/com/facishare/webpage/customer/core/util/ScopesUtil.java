package com.facishare.webpage.customer.core.util;

import com.facishare.webpage.customer.api.constant.Constant;
import com.facishare.webpage.customer.api.constant.ScopeType;
import com.facishare.webpage.customer.api.model.Scope;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by <PERSON>hangyi on 2019/9/17.
 */
public class ScopesUtil {

    public static List<String> buildScopesToString(List<Scope> scopeList) {
        if (CollectionUtils.isEmpty(scopeList)) {
            return Lists.newArrayList();
        }
        List<String> scopes = Lists.newArrayList();
        scopeList.stream().forEach(scope -> {
            scopes.add(ScopeType.getScopeValue(scope.getDataType()) + Constant.SEPARATOR + scope.getDataId());
        });
        return scopes;
    }

    public static List<Scope> resoleStringScope(List<String> scopes) {
        if (CollectionUtils.isEmpty(scopes)) {
            return Lists.newArrayList();
        }
        List<Scope> scopeList = Lists.newArrayList();
        scopes.stream().forEach(x -> {
            String[] strings = x.split(Constant.SEPARATOR);
            if (strings.length > 2) {
                return;
            } else {
                Scope scope = new Scope();
                scope.setDataType(ScopeType.getScopeType(strings[0]));
                scope.setDataId(strings[1]);
                scopeList.add(scope);
            }
        });
        return scopeList;
    }

    public static List<Scope> buildScope(int dataType, List<String> dataIds) {
        if (CollectionUtils.isEmpty(dataIds)) {
            return Lists.newArrayList();
        }
        List<Scope> scopeList = Lists.newArrayList();
        for (String dataId : dataIds) {
            Scope scope = new Scope();
            scope.setDataType(dataType);
            scope.setDataId(dataId);
            scopeList.add(scope);
        }
        return scopeList;
    }

    public static String getLayoutScopeName(Map<Integer, Object> scopeNameMap, List<Scope> scopeList) {
        List<String> scopeNames = getScopeNames(scopeNameMap, scopeList);
        return StringUtils.join(scopeNames, ",");
    }

    public static List<String> getScopeNames(Map<Integer, Object> scopeNameMap, List<Scope> scopeList) {
        if (CollectionUtils.isEmpty(scopeList)) {
            return null;
        }

        Map<Integer, String> employeeMap = (Map<Integer, String>) scopeNameMap.getOrDefault(ScopeType.Employee.getType(), Maps.newHashMap());
        Map<Integer, String> departmentMap = (Map<Integer, String>) scopeNameMap.getOrDefault(ScopeType.Department.getType(), Maps.newHashMap());
        Map<String, String> groupMap = (Map<String, String>) scopeNameMap.getOrDefault(ScopeType.Group.getType(), Maps.newHashMap());
        Map<String, String> roleMap = (Map<String, String>) scopeNameMap.getOrDefault(ScopeType.Role.getType(), Maps.newHashMap());
        Map<String, String> outRoleMap = (Map<String, String>) scopeNameMap.getOrDefault(ScopeType.OutRole.getType(), Maps.newHashMap());
        Map<String, String> outUIdMap = (Map<String, String>) scopeNameMap.getOrDefault(ScopeType.OutUId.getType(), Maps.newHashMap());
        Map<String, String> outTenantMap = (Map<String, String>) scopeNameMap.getOrDefault(ScopeType.OutTenant.getType(), Maps.newHashMap());
        Map<String, String> tenantGroupMap = (Map<String, String>) scopeNameMap.getOrDefault(ScopeType.TenantGroup.getType(), Maps.newHashMap());


        List<String> scopeNames = Lists.newArrayList();

        scopeList.stream().forEach(scope -> {
            int dataType = scope.getDataType();
            String dataId = scope.getDataId();
            switch (dataType) {
                case 1:
                    if (StringUtils.isNotBlank(dataId) && !"null".equals(dataId)) {
                        scopeNames.add(employeeMap.get(Integer.parseInt(dataId)));
                    }
                    break;
                case 2:
                    if (StringUtils.isNotBlank(dataId) && !"null".equals(dataId)) {
                        scopeNames.add(departmentMap.get(Integer.parseInt(dataId)));
                    }
                    break;
                case 3:
                    scopeNames.add(groupMap.get(dataId));
                    break;
                case 4:
                    scopeNames.add(roleMap.get(dataId));
                    break;
                case 5:
                    scopeNames.add(outRoleMap.get(dataId));
                    break;
                case 6:
                    scopeNames.add(outUIdMap.get(dataId));
                    break;
                case 7:
                    scopeNames.add(outTenantMap.get(dataId));
                    break;
                case 8:
                    scopeNames.add(tenantGroupMap.get(dataId));
                    break;
                default:
                    break;
            }
        });
        return scopeNames.stream().

                filter(Objects::nonNull).

                collect(Collectors.toList());
    }

    public static String getEmployeeName(Map<Integer, Object> scopeNameMap, int employeeId) {
        Map<Integer, String> employeeMap = (Map<Integer, String>) scopeNameMap.getOrDefault(ScopeType.Employee.getType(), Maps.newHashMap());
        return employeeMap.get(employeeId);
    }

    public static List<String> getDataId(int dataType, List<Scope> scopeList) {

        if (CollectionUtils.isEmpty(scopeList)) {
            return Lists.newArrayList();
        }
        return scopeList.stream().filter(scope -> dataType == scope.getDataType()).map(Scope::getDataId).collect(Collectors.toList());
    }

}
