package com.facishare.webpage.customer.api.model.core;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by shecheng on 19/12/10.
 */
@Data
public class PersonPrivilege implements Serializable{

    @JSONField(ordinal = 1)
    private List<String> functionCode;

    @JSONField(ordinal = 2)
    private List<String> roleCodes;

    @JSONField(ordinal = 3)
    private String appId;
}
