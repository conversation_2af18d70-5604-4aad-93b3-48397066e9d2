package com.facishare.webpage.customer.controller.model.arg.portal;

import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.dao.entity.ReferenceEntity;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 文件信息DTO，用于站点页面中引用的文件资源
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileInfoDTO {

    /**
     * 文件apiName
     */
    private String apiName;

    /**
     * 引用者路径，site:${siteApiName}/page:${pageApiName}/component:${componentApiName}
     */
    private String sourceId;

    /**
     * 引用者类型，如"site/page/component"
     */
    private String sourceType;

    /**
     * 所属工作区apiName
     */
    private String workSpaceApiName;


    public ReferenceEntity toEntity(User user, String siteApiName) {
        ReferenceEntity entity = new ReferenceEntity();
        entity.setTargetType("file");
        entity.setTargetId(apiName);
        entity.setSourceType(sourceType);
        entity.setSourceId(sourceId);
        entity.setIndexName("siteApiName");
        entity.setIndexValue(siteApiName);
        entity.setTenantId(user.getTenantId());
        entity.setCreatorId(user.getUserId());
        entity.setCreateTime(System.currentTimeMillis());
        return entity;
    }
}
