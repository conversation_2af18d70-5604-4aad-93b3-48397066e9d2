package com.facishare.webpage.customer.api.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by zhouwr on 2024/12/20.
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class RestApiResult<T> {
    public static final int SUCCESS_CODE = 0;
    public static final String SUCCESS_MESSAGE = "OK";

    private int code;
    private String message;
    private T data;

    public boolean success() {
        return code == SUCCESS_CODE;
    }

    public static <T> RestApiResult<T> success(T data) {
        return (RestApiResult<T>) RestApiResult.builder()
                .code(SUCCESS_CODE)
                .message(SUCCESS_MESSAGE)
                .data(data)
                .build();
    }

    public static RestApiResult fail(int code, String message) {
        return RestApiResult.builder().code(code).message(message).build();
    }
}
