package com.facishare.webpage.customer.api.utils;

import com.facishare.webpage.customer.api.constant.Constant;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/11/30 4:04 下午
 */
public class WebPageUtils {

    public static boolean checkPaaSApp(String appId) {
        return StringUtils.isNotEmpty(appId) && appId.startsWith(Constant.PAAS_APPID_PRE);
    }
    public static String buildWidgetI18Key(String source, String model, String homePageId, String biCardApiName) {
        return String.join("_", source, model, homePageId, biCardApiName);
    }

    public static String buildAppStatusKey(String appId, int tenantId){
        return String.join("_", appId, Integer.toString(tenantId));
    }

    public static boolean isOutUser(int userId) {
        return userId > 100000000;
    }

}
