package com.facishare.webpage.customer.core.component;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.HomePageLayoutCard;
import com.facishare.webpage.customer.core.config.ComponentNameConfig;
import com.facishare.webpage.customer.core.constant.Constant;
import com.facishare.webpage.customer.core.util.CovertUtil;

import javax.annotation.Resource;

/**
 * Created by zhangyu on 2020/6/28
 */
public class ComponentCovertService {

    @Resource
    private ComponentNameConfig componentNameConfig;

    public JSONObject covertHomePageCard(HomePageLayoutCard homePageLayoutCard) {

        CardHomePageCovert cardHomePageCovert = new CardHomePageCovert();
        String apiName = CovertUtil.getApiName(homePageLayoutCard);
        cardHomePageCovert.setApiName(apiName);
        cardHomePageCovert.setComponentNameConfig(componentNameConfig);
        cardHomePageCovert.setHomePageLayoutCard(homePageLayoutCard);
        return cardHomePageCovert.buildComponent();

    }

    public JSONObject covertWidget(HomePageLayoutCard homePageLayoutCard) {
        CardHomePageCovert cardHomePageCovert = new CardHomePageCovert();
        cardHomePageCovert.setComponentNameConfig(componentNameConfig);
        cardHomePageCovert.setHomePageLayoutCard(homePageLayoutCard);
        String apiName = CovertUtil.getApiName(homePageLayoutCard);
        cardHomePageCovert.setApiName(apiName);
        JSONObject jsonObject = cardHomePageCovert.buildData(Constant.APP, homePageLayoutCard);
        jsonObject.put("api_name", apiName);
        jsonObject.put("limit", cardHomePageCovert.getLimit());
        jsonObject.put("type", cardHomePageCovert.getType());
        return jsonObject;
    }

    public JSONObject covertHomePageCardV2(String appId, HomePageLayoutCard homePageLayoutCard) {

        CardHomePageCovert cardHomePageCovert = new CardHomePageCovert();
        String apiName = CovertUtil.getApiName(homePageLayoutCard);
        if ("CRM".equals(appId) && !CovertUtil.PS_Filter.equals(homePageLayoutCard.getCardId())){
            apiName = homePageLayoutCard.getCardId();
        }
        cardHomePageCovert.setApiName(apiName);
        cardHomePageCovert.setComponentNameConfig(componentNameConfig);
        cardHomePageCovert.setHomePageLayoutCard(homePageLayoutCard);
        return cardHomePageCovert.buildComponent();

    }

}
