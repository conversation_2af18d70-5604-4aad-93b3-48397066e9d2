package com.facishare.webpage.customer.api.model.core.customelayout;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2020/4/1
 */
@Data
public class CustomerLayout implements Serializable {

    private List<LayoutData> layout;

    private JSONObject components;

    private List<Filter> filters;

    @Data
    public static class LayoutData implements Serializable{
        private List<Column> columns;

        private List<List<String>> components;

    }
    @Data
    public static class Column implements Serializable{

        private String width;

    }

}
