package com.facishare.webpage.customer.api.constant;

import lombok.Data;

public interface AppTypeConstant {
    enum AppType {
        platApp(1, "platApp", "平台应用"),//ignoreI18n
        linkApp(2, "linkApp", "互联应用"),//ignoreI18n
        paasApp(3, "paasApp", "paas应用");//ignoreI18n

        public int getType() {
            return type;
        }

        public String getName() {
            return name;
        }

        public String getDesc() {
            return desc;
        }

        private int type;
        private String name;
        private String desc;

        AppType(int type, String name, String desc) {
            this.type = type;
            this.name = name;
            this.desc = desc;
        }

        public static AppType getAppTypeByName(String name) {
            for (AppType appType : AppType.values()) {
                if (appType.getName().equals(name)) {
                    return appType;
                }
            }
            return null;
        }

    }
}
