package com.facishare.webpage.customer.controller.model.arg.cms;

import com.facishare.webpage.customer.api.constant.CmsStatus;
import lombok.Data;
import java.io.Serializable;

import java.io.Serializable;
import java.util.List;

/**
 * 创建工作区参数
 */
@Data
public class CreateWorkSpaceArg implements Serializable {

    /**
     * 工作区名称
     */
    private String name;

    /**
     * 工作区ApiName
     */
    private String apiName;

    /**
     * 工作区描述
     */
    private String description;

    /**
     * 状态：0-启用（默认），1-禁用
     */
    private Integer status = CmsStatus.ENABLE;

    /**
     * 关联的频道列表
     */
    private List<String> channelList;
}
