package com.facishare.webpage.customer.api.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/19 11:56 上午
 */
@Data
@Builder
public class QueryCustomerGroupsResult implements Serializable {

    private Map<String, String> dataMap;
    private List<Result> results;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String menuName;
        private String menuId;
        private List<menuGroup> menuGroups;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class menuGroup {
        private String name;
        private String api;
        private List<String> oldKeys;
        private String defaultTranslateKey;
        private List<String> preKeys;
    }

}
