package com.facishare.webpage.customer.core.config;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.core.model.Widget;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Deprecated
/*
    * 启用
    * 不单单这一个配置文件, 应该读取fs-webpage-customer-widget-all
    * 参见com.facishare.webpage.customer.core.config.WidgetsConfig
 */
public class AllWidgetsConfig {

    private List<Widget> widgets;

    private Map<String, Widget> widgetCollections;

    public void init() {
        ConfigFactory.getInstance().getConfig("fs-webpage-customer-widget", iConfig -> {
            widgets = JSONObject.parseArray(iConfig.getString(), Widget.class);
            widgetCollections = widgets.stream().collect(Collectors.toMap(widget -> widget.getId(), Function.identity(), (t1, t2) -> t2));
        });
    }

    public List<Widget> getWidgets() {
        return widgets;
    }

    public List<Widget> getWidgetsByIds(List<String> widgetsIds) {
        List<Widget> widgetList = widgetsIds.stream().
                map(id -> widgetCollections.get(id)).
                filter(widget -> !Objects.isNull(widget)).
                collect(Collectors.toList());
        return widgetList.stream().map(x -> {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                return objectMapper.readValue(objectMapper.writeValueAsString(x), Widget.class);
            } catch (Exception e) {
                log.error("clone error by widget:{}", x, e);
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public List<Widget> getWidgets(List<String> widgetIds) {
        return widgets;
    }
}
