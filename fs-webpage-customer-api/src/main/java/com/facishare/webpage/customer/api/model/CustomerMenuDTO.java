package com.facishare.webpage.customer.api.model;

import com.facishare.webpage.customer.api.model.core.Icon;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/13 2:45 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerMenuDTO implements Serializable {
    /**
     * 自定义菜单项的Id
     */
    private String menuApiName;
    /**
     * 应用id
     */
    private String appId;
    /**
     * 自定义菜单的菜单类型
     */
    private int menuType;
    /**
     * 对应的apiName，自定义页面即为layoutApiName
     */
    private String apiName;
    /**
     * 对应的apiName列表，自定义页面即为layoutApiName
     */
    private List<String> apiNameList;
    /**
     * 自定义菜单项名称
     */
    private String name;
    /**
     * 自定义菜单项适用端（废弃）
     * web or app
     */
    @Deprecated
    private String type;
    /**
     * 自定义菜单项适用端
     * web or app
     */
    private List<String> supportClients;
    /**
     * 菜单应用类型
     * 0：企业内
     * 1、互联
     */
    private int applyType;
    /**
     * 适用范围
     */
    private List<Scope> scopeList;
    /**
     * 对象的apiName
     */
    private String objectApiName;
    /**
     * 业务类型
     */
    private String objectRecordTypeApiName;
    /**
     * 自定义菜单项icon
     */
    private Icon icon;
    /**
     * 快消要的全量适用范围
     */
    private List<Scope> allScopeList;
}
