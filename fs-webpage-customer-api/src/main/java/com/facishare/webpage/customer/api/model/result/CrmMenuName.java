package com.facishare.webpage.customer.api.model.result;

import com.facishare.cep.plugin.enums.ClientTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2024/11/1
 * @Description :
 */
@Data
@Builder
public class CrmMenuName {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg{
        private Integer enterpriseId;
        private Locale locale;
        private String appId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result{
        List<Menu> menuList;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Menu{
        private String menuName;
        private String menuId;
        private String defaultTranslateKey;
        private List<String> preKeys;
    }
}
