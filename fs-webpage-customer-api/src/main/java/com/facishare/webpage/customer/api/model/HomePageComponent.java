package com.facishare.webpage.customer.api.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2019/9/6
 */
@Data
public class HomePageComponent implements Serializable {

    private String appId;   //应用Id

    @SerializedName("CardID")
    private String cardId;  //卡片Id
    @SerializedName("Title")
    private String title;   // 标题
    @SerializedName("ID")
    private String id;  //当前菜单对应的Id
    @SerializedName("ParentID")
    private String parentId;    //当前菜单的父Id
    @SerializedName("Type")
    private int type;   //卡片类型：日程、任务、图表、场景、工具等
    @SerializedName("URL")
    private String url; //url
    @SerializedName("ComponentType")
    private int componentType;  //菜单栏状态：1、一级菜单；2、二级菜单
}
