package com.facishare.webpage.customer.controller.model.arg.cms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 删除资源参数
 */
@Data
public class DeleteResourceArg implements Serializable {

    private static final long serialVersionUID = 1L;

    List<DeleteResource> resourceList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class DeleteResource {
        /**
         * 文件ApiName
         */
        private String apiName;

        private String nodeType;

        private String workSpaceApiName;
    }

}
