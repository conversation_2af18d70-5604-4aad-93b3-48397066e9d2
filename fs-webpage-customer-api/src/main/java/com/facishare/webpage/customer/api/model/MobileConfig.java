package com.facishare.webpage.customer.api.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/12/26.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MobileConfig implements Serializable {
    @JSONField(name = "M1")
    private String listAction;
    @JSONField(name = "M2")
    private String addAction;

}
