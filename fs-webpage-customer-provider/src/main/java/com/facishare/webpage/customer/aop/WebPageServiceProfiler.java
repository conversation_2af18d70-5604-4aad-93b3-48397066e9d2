package com.facishare.webpage.customer.aop;

import com.facishare.webpage.customer.api.exception.ParameterException;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.github.trace.aop.ServiceProfiler;

/**
 * <AUTHOR>
 * @date 2021/11/22 10:00 上午
 */
public class WebPageServiceProfiler extends ServiceProfiler {

    @Override
    protected boolean isFail(Throwable e) {
        if (e == null) {
            return false;
        }
        if (e instanceof ParameterException
                || e instanceof WebPageException) {
            return false;
        }
        return super.isFail(e);
    }
}
