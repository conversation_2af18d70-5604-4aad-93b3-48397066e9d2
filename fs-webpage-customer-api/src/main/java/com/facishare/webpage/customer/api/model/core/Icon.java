package com.facishare.webpage.customer.api.model.core;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON> on 19/12/10.
 */
@Data
public class Icon implements Serializable{

    @J<PERSON>NField(ordinal = 1)
    private String icon_1;

    @J<PERSON>NField(ordinal = 2)
    private String icon_2;


    @J<PERSON><PERSON>ield(ordinal = 3)
    private String normal;


    @JSONField(ordinal = 4)
    private String highlight;

    @JSONField(ordinal = 5)
    private String fxIcon;
}
