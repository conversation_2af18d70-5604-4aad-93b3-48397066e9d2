package com.facishare.webpage.customer.api.service;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.arg.FindSiteConfigArg;
import com.facishare.webpage.customer.api.model.result.FindSiteConfigRestResult;

import java.util.Map;

/**
 * Created by zhouwr on 2024/12/19.
 */
@RestResource(
        value = "WebPageResource",
        desc = "独立站点rest接口",//ignoreI18n
        contentType = "application/json"
)
public interface SiteRestService {
    @POST(value = "/webPage/siteRest/findSiteConfig", desc = "查询站点配置")
    FindSiteConfigRestResult findSiteConfig(@HeaderMap Map<String, String> headers, @Body FindSiteConfigArg arg);
}
