package com.facishare.webpage.customer.controller.model;

import com.facishare.webpage.customer.dao.entity.ReferenceEntity;
import com.facishare.webpage.customer.dao.entity.SiteEntity;
import com.facishare.webpage.customer.util.ReferenceTargetType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;

/**
 * 多语信息DTO
 * 表示站点组件的多语内容信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class I18nInfoDTO {

    /**
     * 多语key，例如："i18n.sdasdsa.cc"
     */
    private String key;

    /**
     * 维度类型："page" 或 "theme"
     */
    private String dimension;

    /**
     * 源ID信息，固定格式：
     * "site:${siteApiName}/page:${pageApiName}/component:${componentApiName}"
     */
    private String sourceId;

    /**
     * 源类型："site/page/component"
     */
    private String sourceType;

    /**
     * 父级API名称（页面API名称或主题API名称）
     */
    private String superApiName;

    /**
     * 多语信息映射
     * Key: 语言代码（例如："zh-CN", "en", "ja-JP"）
     * Value: 翻译文本
     */
    private Map<String, String> languageInfo;

    /**
     * 验证多语信息
     *
     * @throws IllegalArgumentException 如果验证失败
     */
    public void validate() {
        if (StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("I18n key cannot be empty");
        }

        if (StringUtils.isBlank(dimension)) {
            throw new IllegalArgumentException("Dimension cannot be empty");
        }

        if (StringUtils.isBlank(sourceId)) {
            throw new IllegalArgumentException("Source ID cannot be empty");
        }

        if (StringUtils.isBlank(sourceType)) {
            throw new IllegalArgumentException("Source type cannot be empty");
        }
        if (MapUtils.isEmpty(languageInfo)) {
            throw new IllegalArgumentException("Language info cannot be empty");
        }
    }


    /**
     * 从源ID中提取站点API名称
     *
     * @return 站点API名称，如果未找到返回null
     */
    public String extractSiteApiName() {
        if (StringUtils.isBlank(sourceId)) {
            return null;
        }

        try {
            String[] parts = sourceId.split("/");
            for (String part : parts) {
                if (part.startsWith("site:")) {
                    return part.substring(5); // 移除"site:"前缀
                }
            }
        } catch (Exception e) {
            // 记录错误但不抛出异常
            return null;
        }

        return null;
    }

    /**
     * 从源ID中提取页面API名称
     *
     * @return 页面API名称，如果未找到返回null
     */
    public String extractPageApiName() {
        if (StringUtils.isBlank(sourceId)) {
            return null;
        }

        try {
            String[] parts = sourceId.split("/");
            for (String part : parts) {
                if (part.startsWith("page:")) {
                    return part.substring(5); // 移除"page:"前缀
                }
            }
        } catch (Exception e) {
            // 记录错误但不抛出异常
            return null;
        }

        return null;
    }

    /**
     * 从源ID中提取组件API名称
     *
     * @return 组件API名称，如果未找到返回null
     */
    public String extractComponentApiName() {
        if (StringUtils.isBlank(sourceId)) {
            return null;
        }

        try {
            String[] parts = sourceId.split("/");
            for (String part : parts) {
                if (part.startsWith("component:")) {
                    return part.substring(10); // 移除"component:"前缀
                }
            }
        } catch (Exception e) {
            // 记录错误但不抛出异常
            return null;
        }

        return null;
    }

    /**
     * 检查语言信息是否包含指定语言
     *
     * @param lang 要检查的语言代码
     * @return 如果包含该语言返回true
     */
    public boolean hasLanguage(String lang) {
        return MapUtils.isNotEmpty(languageInfo) && languageInfo.containsKey(lang);
    }

    /**
     * 获取指定语言的翻译
     *
     * @param lang 语言代码
     * @return 翻译文本，如果未找到返回null
     */
    public String getTranslation(String lang) {
        if (MapUtils.isEmpty(languageInfo)) {
            return null;
        }
        return languageInfo.get(lang);
    }

    /**
     * 转换为ReferenceEntity
     *
     * @param siteEntity 站点实体
     * @return ReferenceEntity
     */
    public ReferenceEntity toEntity(SiteEntity siteEntity) {
        ReferenceEntity referenceEntity = new ReferenceEntity();

        referenceEntity.setTenantId(siteEntity.getTenantId());
        referenceEntity.setTargetType(ReferenceTargetType.I18N.getCode());
        referenceEntity.setTargetId(this.key);
        referenceEntity.setSourceType(this.sourceType);
        referenceEntity.setSourceId(this.sourceId);
        referenceEntity.setIndexName("siteApiName");
        referenceEntity.setIndexValue(siteEntity.getApiName());

        return referenceEntity;
    }
}
