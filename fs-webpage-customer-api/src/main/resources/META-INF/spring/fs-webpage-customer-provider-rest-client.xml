<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-2.5.xsd">

<!--
    <bean name="webpageRestHttpClient" id="webpageRestHttpClient" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"/>
-->

    <bean id="webpageApiHostProfile" class="com.facishare.dubbo.plugin.client.ServerHostProfile">
        <property name="configName" value="fs-webpage-customer-provider-rest-client"/>
    </bean>

    <bean id="homePageService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.facishare.webpage.customer.api.service.HomePageService"/>
        <property name="serverHostProfile" ref="webpageApiHostProfile"/>
    </bean>


    <bean id="tenantPageTempleService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.facishare.webpage.customer.api.service.TenantPageTempleService"/>
        <property name="serverHostProfile" ref="webpageApiHostProfile"/>
    </bean>



    <bean id="menusRegisterService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.facishare.webpage.customer.api.service.MenusRegisterService"/>
        <property name="serverHostProfile" ref="webpageApiHostProfile"/>
    </bean>


    <bean id="consoleHomePageService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.facishare.webpage.customer.api.console.ConsoleHomePageService"/>
        <property name="serverHostProfile" ref="webpageApiHostProfile"/>
    </bean>


    <bean id="menuService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.facishare.webpage.customer.api.console.MenuService"/>
        <property name="serverHostProfile" ref="webpageApiHostProfile"/>
    </bean>



    <bean id="pageTempleService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.facishare.webpage.customer.api.console.PageTempleService"/>
        <property name="serverHostProfile" ref="webpageApiHostProfile"/>
    </bean>



    <bean id="mainChannelService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.facishare.webpage.customer.api.service.MainChannelService"/>
        <property name="serverHostProfile" ref="webpageApiHostProfile"/>
    </bean>


</beans>
