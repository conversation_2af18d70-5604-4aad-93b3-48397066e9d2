package com.facishare.webpage.customer.api.model.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Locale;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryHomePageWidgetsArg implements Serializable {

    private int enterpriseId;
    private String source;
    private Locale locale;

}
