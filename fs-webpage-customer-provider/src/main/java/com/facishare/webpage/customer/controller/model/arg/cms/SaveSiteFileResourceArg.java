package com.facishare.webpage.customer.controller.model.arg.cms;

import lombok.Data;
import java.io.Serializable;

/**
 * 保存站点文件资源参数 Created by cursor.
 */
@Data
public class SaveSiteFileResourceArg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 站点ApiName
     */
    private String siteApiName;

    /**
     * 工作区ApiName
     */
    private String workSpaceApiName;

    /**
     * 文件ApiName
     */
    private String fileApiName;

    /**
     * 站点中的相对路径，从站点定义的根目录开始
     */
    private String relativePath;
}
