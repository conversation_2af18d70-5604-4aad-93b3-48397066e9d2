package com.facishare.webpage.customer.api.model;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON>hangyu on 2019/9/6
 */
@Data
public class HomePageLayoutTO implements Serializable {
    @JSONField(name = "M1")
    @SerializedName("LayoutID")
    private String layoutId;    //首页Id
    @JSONField(name = "M2")
    @SerializedName("Name")
    private String name;
    @JSONField(name = "M3")
    @SerializedName("Description")
    private String description;
    @JSONField(name = "M4")
    @SerializedName("Scope")
    private List<Scope> scopes; //人员范围
    @JSONField(name = "M5")
    @SerializedName("Status")
    private int status;
    @JSONField(name = "M6")
    @SerializedName("LayoutType")
    private int layoutType;
    @JSONField(name = "M7")
    @SerializedName("HomePageLayouts")
    private List<HomePageLayoutCard> homePageLayouts;   //首页卡片集合, 只有很早之前的老版预置CRM首页会存在这个字段
    @JSONField(name = "M8")
    @SerializedName("LayoutScopeName")
    private String layoutScopeName;
    @JSONField(name = "M9")
    @SerializedName("IsCurrentLayout")
    private boolean isCurrentLayout;
    @JSONField(name = "M10")
    @SerializedName("IsSystem")
    private boolean isSystem;
    @JSONField(name = "M11")
    @SerializedName("templeId")
    private String templeId;
    @JSONField(name = "M12")
    @SerializedName("apiName")
    private String apiName;
    @JSONField(name = "M13")
    @SerializedName("displayName")
    private String displayName; // 对象名称, 在BI自定义页面会使用
    @SerializedName("dataVersion")
    private int dataVersion = 100;
    @SerializedName("pageLayoutType")
    private int pageLayoutType;   //页面布局类型：1、单区域布局；2、上左右布局（左右1：2）；3、上左右布局（左右2：1）；4、左右布局（1：2）；4、左右布局（2：1）
    @SerializedName("layoutApiName")
    private String layoutApiName;
    @SerializedName("creator")
    private String creator;
    @SerializedName("createTime")
    private long createTime;
    @SerializedName("updateTime")
    private long updateTime;
    @SerializedName("objectApiName")
    private String objectApiName;

    @SerializedName("appId")
    private String appId;
    @SerializedName("appName")
    private String appName;

    @SerializedName("channelId")
    private String channelId;
    @SerializedName("channelName")
    private String channelName;

    @SerializedName("priorityLevel")
    private int priorityLevel;  //优先级
    @SerializedName("appType")
    private int appType;
    /**
     * 顶导航开启 1开启  0未开启
     */
    @SerializedName("pageMultiType")
    private int pageMultiType;
    /**
     * 默认标签页index
     */
    @SerializedName("defaultLabelIndex")
    private int defaultLabelIndex;


    @SerializedName("customerLayout")
    private JSONObject customerLayout;
    /**
     * 所有标签页数据信息，每一项为customerLayout"，此项考虑和customerLayout冗余，customerLayout作为往前兼容的冗余数据
     */
    @SerializedName("customerLayoutList")
    private List<JSONObject> customerLayoutList;
    /**
     *
     */
    @SerializedName("iconIndex")
    private Integer iconIndex;
    @SerializedName("fromOldCrmHomePage")
    private Boolean fromOldCrmHomePage = false;

    @SerializedName("showPageMultiFlag")
    private Boolean showPageMultiFlag = true;

    @SerializedName("sourceType")
    private String sourceType;
}
