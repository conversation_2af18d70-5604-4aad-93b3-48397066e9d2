package com.facishare.webpage.customer.api.service;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.CheckGoNewCrm;
import com.facishare.webpage.customer.api.model.GetHomePageByIdArg;
import com.facishare.webpage.customer.api.model.QueryHomePageList;
import com.facishare.webpage.customer.api.model.SaveTenantMainChannel;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;

/**
 * Created by zhangyu on 2020/11/26
 */
@RestResource(
        value = "WebPageResource",
        desc = "自定义页面",//ignoreI18n
        contentType = "application/json"
)
public interface WebPageService {

    /**
     * 获取首页筛选器
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/getHomePageFilter", desc = "获取首页筛选器")//ignoreI18n
    GetHomePageFiltersResult getHomePageFilters(@HeaderParam("x-fs-ei") String tenantId, @Body GetHomePageFiltersArg arg);

    /**
     * 获取厂商门户的相关应用的组件
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/queryVendorComponents", desc = "获取厂商门户的相关应用的组件")//ignoreI18n
    QueryVendorComponentsResult queryVendorComponents(@HeaderParam("x-fs-ei") String tenantId, @Body QueryVendorComponentsArg arg);

    /**
     * 获取移动端的筛选器
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/queryAppPageFilters", desc = "获取移动端的筛选器")//ignoreI18n
    QueryAppPageFiltersResult queryAppPageFilters(@HeaderParam("x-fs-ei") String tenantId, @Body QueryAppPageFiltersArg arg);

    /**
     * 获取移动端PaaS应用
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/queryPaasApp", desc = "获取移动端PaaS应用")//ignoreI18n
    QueryPaasAppResult queryPaasApp(@HeaderParam("x-fs-ei") String tenantId, @Body QueryPaasAppArg arg);

    /**
     * 插入自定义页面
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/insertCustomerPage", desc = "插入自定义页面")//ignoreI18n
    InsertCustomerPageResult insertCustomerPage(@HeaderParam("x-fs-ei") String tenantId, @Body InsertCustomerPageArg arg);

    /**
     * 插入主导航数据，支持update
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/saveTenantMainChannel", desc = "插入主导航数据")//ignoreI18n
    SaveTenantMainChannel.Result saveTenantMainChannel(@HeaderParam("x-fs-ei") String tenantId, @Body SaveTenantMainChannel.Arg arg);

    /**
     * 保存自定义菜单项，支持update
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/saveCustomerMenu", desc = "保存自定义菜单项")//ignoreI18n
    SaveCustomerMenuRestResult saveCustomerMenu(@HeaderParam("x-fs-ei") String tenantId, @Body SaveCustomerMenuRestArg arg);

    /**
     * 自定义分组(用作翻译工作台)
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/queryCustomerGroups", desc = "获取自定义分组")// // ignoreI18n
    QueryCustomerGroupsResult queryCustomerGroups(@HeaderParam("x-fs-ei") String tenantId, @Body QueryCustomerGroupsArg arg);

    @POST(value = "/webPage/queryOldCrmMenuGroups", desc = "查询老crm菜单的菜单分组, 用于翻译工作台")// // ignoreI18n
    QueryCustomerGroupsResult queryOldCrmMenuGroups(@HeaderParam("x-fs-ei") String tenantId, @Body QueryCustomerGroupsArg arg);

    /**
     * 获取自定义菜单项(用作翻译工作台)
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/queryCustomerMenus", desc = "获取自定义菜单项")// // ignoreI18n
    QueryCustomerMenusResult queryCustomerMenus(@HeaderParam("x-fs-ei") String tenantId, @Body QueryCustomerMenusArg arg);

    /**
     * 判断CRM入口是否走新版CRM
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/checkGoNewCrm", desc = "判断CRM入口是否走新版CRM")// // ignoreI18n
    CheckGoNewCrm.Result checkGoNewCrm(@HeaderParam("x-fs-ei") String tenantId, @Body CheckGoNewCrm.Arg arg);

    /**
     * 获取单个企业 首页 组件列表(用作翻译工作台)
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/queryHomePageWidgets", desc = "获取单个企业 首页 组件列表(用作翻译工作台)")// // ignoreI18n
    QueryHomePageWidgetsResult queryHomePageWidgets(@HeaderParam("x-fs-ei") String tenantId, @Body QueryHomePageWidgetsArg arg);


    @POST(value = "/webPage/queryHomePageTabs", desc = "获取单个企业 首页 页签信息(用于翻译工作台)")
    QueryHomePageWidgetsResult queryHomePageTabs(@HeaderParam("x-fs-ei") String tenantId, @Body QueryHomePageWidgetsArg arg);

    /**
     * 获取单个企业 自定义页面 组件列表(用作翻译工作台)
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/queryCustomerPageWidgets", desc = "获取单个企业 自定义页面 组件列表(用作翻译工作台)")// // ignoreI18n
    QueryCustomerPageWidgetsResult queryCustomerPageWidgets(@HeaderParam("x-fs-ei") String tenantId, @Body QueryCustomerPageWidgetsArg arg);

    /**
     * 查询有启用的移动端视图的 应用列表
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/getAppListByTypeAndHasEnableAppView", desc = "查询有启用的移动端视图的 应用列表")// // ignoreI18n
    GetAppListByTypeAndHasEnableAppViewResult getAppListByTypeAndHasEnableAppView(@HeaderParam("x-fs-ei") String tenantId, @Body GetAppListByTypeAndHasEnableAppViewArg arg);

    /**
     * 查询企业下的自定义页面页签容器标签, 用于翻译
     *
     * @param tenantId
     */
    @POST(value = "/webPage/queryPageLayoutTabs", desc = "查询企业下的自定义页面页签容器标签")// // ignoreI18n
    QueryPageLayoutTabs.Result queryPageLayoutTabs(@HeaderParam("x-fs-ei") String tenantId, @Body QueryPageLayoutTabs.Arg arg);


    @POST(value = "/webPage/queryHomePageLayoutList", desc = "查询企业下的自定义页面")
    QueryHomePageList.Result queryHomePageLayoutList(@HeaderParam("x-fs-ei") String tenantId, @Body QueryHomePageList.Arg arg);

    @POST(value = "/webPage/getHomePageById", desc = "获取首页")
    GetHomePageByIdResult getHomePageById(@HeaderParam("x-fs-ei") String tenantId, @Body GetHomePageByIdArg arg);

}
