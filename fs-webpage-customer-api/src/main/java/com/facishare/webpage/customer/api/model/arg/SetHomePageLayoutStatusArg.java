package com.facishare.webpage.customer.api.model.arg;

import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.Locale;

/**
 * Created by zhangyu on 2019/10/28
 */
@Data
public class SetHomePageLayoutStatusArg implements Serializable {
    @SerializedName("LayoutID")
    private String layoutId;
    @SerializedName("Status")
    private int status;
    @SerializedName("appType")
    private int appType;
    private String layoutApiName;

    private Integer enterpriseId;
    private String enterpriseAccount;
    private Integer employeeId;
    private ClientTypeEnum type;
    private String version;
    private String deviceId;
    private Locale locale;
    private String osVersion;

}
