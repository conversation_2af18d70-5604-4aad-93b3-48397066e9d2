package com.facishare.webpage.customer.api.model.arg;

import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Locale;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/11/24.
 */
@Data
public class MenuListRestArg {
    private Integer enterpriseId;
    private String enterpriseAccount;
    private Integer employeeId;
    private ClientTypeEnum type;
    private String version;
    private String deviceId;
    private Locale locale;
    private String appId;

    public void valid() {
        if (StringUtils.isEmpty(appId)){
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
