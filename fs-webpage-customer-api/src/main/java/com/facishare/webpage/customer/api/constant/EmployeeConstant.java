package com.facishare.webpage.customer.api.constant;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/10/29
 */
public class EmployeeConstant {

    public static final int HomePageDefault = 1;    //首页默认值

    public static final int UserDefindSelect = 2;   // 自定义模块是否需要全局筛选

    public static final int HomePageMenu = 3; //菜单展示状态
    public static final int APP_LAYOUT_LIST_ORDER = 4; //移动端用户态展示的顶导航页签列表顺序
    public static final int MENU_SHOW_MODE = 5; //菜单展示样式

    @ToString
    public static class HomePageDefaultValue implements Serializable {
        private List<HomePageDefaultItem> empsAndDeps;
        private boolean isAll = false;
        private String dateId;
        private String dateType;
        private List<Integer> dataTypes = Lists.newArrayList();
        private String startTime;
        private String endTime;

        public List<HomePageDefaultItem> getEmpsAndDeps() {
            return empsAndDeps;
        }

        public void setEmpsAndDeps(List<HomePageDefaultItem> empsAndDeps) {
            this.empsAndDeps = empsAndDeps;
        }

        public boolean getIsAll() {
            return isAll;
        }

        public void setIsAll(boolean all) {
            isAll = all;
        }

        public String getDateId() {
            return dateId;
        }

        public void setDateId(String dateId) {
            this.dateId = dateId;
        }

        public String getDateType() {
            return dateType;
        }

        public void setDateType(String dateType) {
            this.dateType = dateType;
        }

        public List<Integer> getDataTypes() {
            return dataTypes;
        }

        public void setDataTypes(List<Integer> dataTypes) {
            this.dataTypes = dataTypes;
        }

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        @Data
        public static class HomePageDefaultItem implements Serializable {
            private long id;
            private int type;
        }

    }
}
