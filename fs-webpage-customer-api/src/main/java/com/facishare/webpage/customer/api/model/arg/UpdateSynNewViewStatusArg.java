package com.facishare.webpage.customer.api.model.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateSynNewViewStatusArg implements Serializable {
    private String appId;
    private int status;
    private int tenantId;
    private int process;
}