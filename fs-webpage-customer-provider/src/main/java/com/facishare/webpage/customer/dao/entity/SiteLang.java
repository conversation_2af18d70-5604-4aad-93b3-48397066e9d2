package com.facishare.webpage.customer.dao.entity;

import com.facishare.webpage.customer.api.model.SiteLangDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.mongodb.morphia.annotations.Property;

/**
 * 站点语言配置实体
 * 用于MongoDB嵌入式文档
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SiteLang {
    
    /**
     * 语言代码，例如："zh_CN", "en", "ja_JP"
     */
    @Property("lang")
    private String lang;
    
    /**
     * 是否为站点的默认语言
     */
    @Property("defaultLang")
    private Boolean defaultLang;
    
    /**
     * 转换为DTO
     * 
     * @return SiteLangDTO
     */
    public SiteLangDTO toDTO() {
        return SiteLangDTO.builder()
                .lang(lang)
                .defaultLang(defaultLang)
                .build();
    }
    
    /**
     * 从DTO创建
     * 
     * @param dto 要转换的DTO
     * @return SiteLang实体
     */
    public static SiteLang fromDTO(SiteLangDTO dto) {
        if (dto == null) {
            return null;
        }
        
        return SiteLang.builder()
                .lang(dto.getLang())
                .defaultLang(dto.getDefaultLang())
                .build();
    }
    
    /**
     * 检查这是否是默认语言
     * 
     * @return 如果是默认语言返回true
     */
    public boolean isDefaultLanguage() {
        return Boolean.TRUE.equals(defaultLang);
    }
}
