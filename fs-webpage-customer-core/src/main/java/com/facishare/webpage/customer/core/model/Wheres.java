package com.facishare.webpage.customer.core.model;

import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.webpage.customer.core.util.CollectionUtils;
import lombok.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class Wheres extends DocumentBasedBean {

    private Wheres(Map wheres) {
        super(wheres);
    }

    public static Wheres of(Map wheres) {
        return new Wheres(wheres);
    }

    public static <T extends Map> List<Wheres> ofList(List<T> wheres) {
        if (CollectionUtils.empty(wheres)) {
            return Collections.emptyList();
        }
        return wheres.stream()
                .map(Wheres::new)
                .map(x -> x.copy())
                .collect(Collectors.toList());
    }

    public List<Filter> getFilters() {
        return Filter.ofList(get("filters", List.class));
    }

    public void filters(List<Filter> filterList) {
        List<Map> filters = filterList.stream().map(Filter::getContainerDocument).collect(Collectors.toList());
        set("filters", filters);
    }

    public String getConnector() {
        return get("connector", String.class);
    }


    public Wheres copy() {
        return new Wheres((Map) copy(map));
    }
}
