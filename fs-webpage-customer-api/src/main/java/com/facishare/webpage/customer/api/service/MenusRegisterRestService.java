package com.facishare.webpage.customer.api.service;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.arg.*;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/11/26
 */
@RestResource(
        value = "WebPageResource",
        desc = "自定义菜单相关rest接口",//ignoreI18n
        contentType = "application/json"
)
public interface MenusRegisterRestService {

    /**
     * 创建menus
     */
    @POST(value = "/webPage/menusRegisterRestService/createMenus", desc = "创建menus")//ignoreI18n
    CreateMenus.Result createMenus(@HeaderParam("x-fs-ei") String tenantId, @Body CreateMenus.Arg arg);

    /**
     * 删除menus
     */
    @POST(value = "/webPage/menusRegisterRestService/deleteMenus", desc = "删除menus")//ignoreI18n
    DeleteMenus.Result deleteMenus(@HeaderParam("x-fs-ei") String tenantId, @Body DeleteMenus.Arg arg);

    @POST(value = "/webPage/menusRegisterRestService/updateMenus", desc = "修改menus")//ignoreI18n
    UpdateMenus.Result updateMenus(@HeaderParam("x-fs-ei") String tenantId, @Body UpdateMenus.Arg arg);
    /**
     *根据collectionId查询
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/menusRegisterRestService/queryMenusByCollectionIds", desc = "根据collectionId查询菜单")//ignoreI18n
    QueryMenusByCollectionIds.Result queryMenusByCollectionIds(@HeaderParam("x-fs-ei") String tenantId, @Body QueryMenusByCollectionIds.Arg arg);
    /**
     * 获取首页筛选器
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/menusRegisterRestService/queryMenus", desc = "查询菜单")//ignoreI18n
    QueryMenus.Result queryMenus(@HeaderParam("x-fs-ei") String tenantId, @Body QueryMenus.Arg arg);

}
