package com.facishare.webpage.customer.dao;


import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.Key;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import com.alibaba.fastjson.JSONObject;
import com.facishare.qixin.sysdb.filter.Filter;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.constant.ClientType;
import com.facishare.webpage.customer.api.constant.Constant;
import com.facishare.webpage.customer.api.constant.SourceType;
import com.facishare.webpage.customer.api.constant.Status;
import com.facishare.webpage.customer.api.model.HomePageLayoutCard;
import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.facishare.webpage.customer.api.model.LayoutType;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.core.util.ScopesUtil;
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity;
import com.facishare.webpage.customer.util.TempleIdUtil;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.mongodb.DuplicateKeyException;
import com.mongodb.MongoCommandException;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by zhangyu on 2019/9/5
 */
@Slf4j
public class HomePageLayoutDaoImpl implements HomePageLayoutDao {

    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();

    private static final String EMPTY = "";

    @Resource
    private Datastore datastore;

    public void setDatastore(Datastore datastore) {
        this.datastore = datastore;
    }

    private Query<HomePageLayoutEntity> buildQuery(int tenantId) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        return query;
    }

    private Query<HomePageLayoutEntity> buildQuery(int tenantId, String clientType) {
        Query<HomePageLayoutEntity> query = buildQuery(tenantId);
        if(ClientType.mobile.same(clientType)) {
            query.field("clientType").equal(ClientType.mobile.getValue());
        } else{
            query.or(
                    query.criteria("clientType").equal(ClientType.web.getValue()),
                    query.criteria("clientType").doesNotExist()
            );
        }
        return query;
    }

    @Override
    public HomePageLayoutEntity getHomePageLayoutById(String layoutId, Filter filter) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("layoutId").equal(layoutId);
        if (filter != null) {
            filter.addFilter(query);
        }
        return query.get();
    }

    @Override
    public List<HomePageLayoutEntity> findBySiteApiNameIncludeDisable(int tenantId, String appId, String siteApiName) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("appId").equal(appId);
        query.field("siteApiName").equal(siteApiName);
        query.field("status").in(Lists.newArrayList(Status.ENABLE, Status.DISABLE));
        query.order("createTime");
        return query.asList();
    }

    @Override
    public List<HomePageLayoutEntity> findBySiteApiNameIncludeDisable(int tenantId, String appId, String siteApiName, String clientType) {
        Query<HomePageLayoutEntity> query = buildQuery(tenantId, clientType);
        query.field("appId").equal(appId);
        query.field("siteApiName").equal(siteApiName);
        query.field("status").in(Lists.newArrayList(Status.ENABLE, Status.DISABLE));
        query.order("createTime");
        return query.asList();
    }

    @Override
    public List<HomePageLayoutEntity> findBySiteApiName(int tenantId, String appId, String siteApiName, String clientType) {
        Query<HomePageLayoutEntity> query = buildQuery(tenantId, clientType);
        query.field("appId").equal(appId);
        query.field("siteApiName").equal(siteApiName);
        query.field("status").equal(Status.ENABLE);
        query.order("createTime");
        return query.asList();
    }

    @Override
    public HomePageLayoutEntity findBySiteApiNameAndPageApiName(int tenantId,
                                                                String appId,
                                                                String siteApiName,
                                                                String pageApiName) {
        Query<HomePageLayoutEntity> query = buildQuery(tenantId);
        query.field("appId").equal(appId);
        query.field("siteApiName").equal(siteApiName);
        query.field("apiName").equal(pageApiName);
        query.field("status").equal(Status.ENABLE);
        return query.get();
    }

    @Override
    public HomePageLayoutEntity findHomePageBySiteApiName(int tenantId, String appId, String siteApiName, String clientType) {
        Query<HomePageLayoutEntity> query = buildQuery(tenantId, clientType);
        query.field("appId").equal(appId);
        query.field("siteApiName").equal(siteApiName);
        query.field("isHomePage").equal(true);
        return query.get();
    }

    @Override
    public List<HomePageLayoutEntity> getBatchHomePageLayoutByIds(List<String> layoutIds, Filter filter) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("layoutId").in(layoutIds);
        if (filter != null) {
            filter.addFilter(query);
        }
        return query.asList();
    }

    @Override
    public HomePageLayoutEntity insertHomePageLayout(String appId, int appType, int tenantId, int employeeId, HomePageLayoutTO homePageLayoutTO, String sourceId, boolean change) {

        List<String> homePageCardsEntity = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(homePageLayoutTO.getHomePageLayouts())) {
            homePageCardsEntity = buildHomePageCard(homePageLayoutTO.getHomePageLayouts());
        }
        HomePageLayoutEntity homePageLayoutEntity = new HomePageLayoutEntity();
        {
            homePageLayoutEntity.setLayoutId(homePageLayoutTO.getLayoutId());
            homePageLayoutEntity.setAppId(appId);
            homePageLayoutEntity.setTenantId(tenantId);
            homePageLayoutEntity.setHomePageCardEntityList(homePageCardsEntity);
            homePageLayoutEntity.setName(homePageLayoutTO.getName());
            homePageLayoutEntity.setDescription(homePageLayoutTO.getDescription());
            if (CollectionUtils.isNotEmpty(homePageLayoutTO.getScopes())) {
                homePageLayoutEntity.setScopes(ScopesUtil.buildScopesToString(homePageLayoutTO.getScopes()));
            }
            homePageLayoutEntity.setLayoutType(homePageLayoutTO.getLayoutType());
            homePageLayoutEntity.setCreatorId(employeeId);
            homePageLayoutEntity.setCreateTime(new Date());
            homePageLayoutEntity.setUpdaterId(employeeId);
            homePageLayoutEntity.setUpdateTime(new Date());
            homePageLayoutEntity.setChange(change);
            homePageLayoutEntity.setIconIndex(homePageLayoutEntity.getIconIndex());

            if (homePageLayoutTO.isSystem()) {
                homePageLayoutEntity.setSourceType(SourceType.SYSTEM);
            } else {
                homePageLayoutEntity.setSourceType(SourceType.CUSTOMER);
            }

            if (homePageLayoutTO.isSystem() && homePageLayoutTO.getLayoutType() == LayoutType.SYSTEM) {
                homePageLayoutEntity.setSourceId(sourceId);
            }

            if (appId.equals(Constant.APP_CRM) || appType == BizType.PAAS.getType() || appType == BizType.PRE_CUSTOMER.getType()) {
                homePageLayoutEntity.setStatus(Status.ENABLE);
            } else {
                homePageLayoutEntity.setStatus(Status.TEMPORARY);
            }
            if (homePageLayoutTO.getCustomerLayout() != null) {
                homePageLayoutEntity.setCustomerLayout(JSONObject.toJSONString(homePageLayoutTO.getCustomerLayout()));
            }
            if (CollectionUtils.isNotEmpty(homePageLayoutTO.getCustomerLayoutList())) {
                homePageLayoutEntity.setCustomerLayoutList(homePageLayoutTO.getCustomerLayoutList().stream().map(item -> JSONObject.toJSONString(item)).collect(Collectors.toList()));
            }
            if (appType == BizType.CUSTOMER.getType()) {
                homePageLayoutEntity.setApiName(homePageLayoutTO.getApiName());
            } else if (appType == BizType.PRE_CUSTOMER.getType()) {
                homePageLayoutEntity.setApiName(StringUtils.isBlank(homePageLayoutTO.getApiName()) ?
                        homePageLayoutTO.getLayoutApiName() : homePageLayoutTO.getApiName());
            } else {
                homePageLayoutEntity.setApiName(TempleIdUtil.removeTenantIdById(tenantId, appType, homePageLayoutTO.getLayoutId()));
            }

            homePageLayoutEntity.setDataVersion(homePageLayoutTO.getDataVersion());
            homePageLayoutEntity.setPageLayoutType(homePageLayoutTO.getPageLayoutType());
            homePageLayoutEntity.setAppType(appType);
            homePageLayoutEntity.setPriorityLevel(homePageLayoutTO.getPriorityLevel());
            if (CollectionUtils.isNotEmpty(homePageLayoutTO.getCustomerLayoutList())) {
                homePageLayoutEntity.setCustomerLayoutList(homePageLayoutTO.getCustomerLayoutList().stream().map(item -> JSONObject.toJSONString(item)).collect(Collectors.toList()));
            }
            homePageLayoutEntity.setPageMultiType(homePageLayoutTO.getPageMultiType());
            homePageLayoutEntity.setDefaultLabelIndex(homePageLayoutTO.getDefaultLabelIndex());
        }
        Key<HomePageLayoutEntity> saveKey = datastore.save(homePageLayoutEntity);
        if (saveKey != null) {
            return homePageLayoutEntity;
        } else {
            return null;
        }
    }

    @Override
    public HomePageLayoutEntity findAndModifyHomePage(int tenantId, HomePageLayoutEntity homePageLayoutEntity) {
        HomePageLayoutEntity resultEntity = new HomePageLayoutEntity();
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("layoutId").equal(homePageLayoutEntity.getLayoutId());

        UpdateOperations<HomePageLayoutEntity> updateOperations = datastore.createUpdateOperations(HomePageLayoutEntity.class);
        updateOperations.setOnInsert("layoutId", homePageLayoutEntity.getLayoutId());
        updateOperations.setOnInsert("tenantId", tenantId);
        updateOperations.setOnInsert("apiName", homePageLayoutEntity.getApiName() == null ?
                TempleIdUtil.removeTenantIdById(tenantId, homePageLayoutEntity.getAppType(), homePageLayoutEntity.getLayoutId()) :
                homePageLayoutEntity.getApiName());
        updateOperations.setOnInsert("layoutType", homePageLayoutEntity.getLayoutType());
        updateOperations.setOnInsert("appId", homePageLayoutEntity.getAppId());
        updateOperations.setOnInsert("appType", homePageLayoutEntity.getAppType());
        if (homePageLayoutEntity.getApplyType() != null) {
            updateOperations.setOnInsert("applyType", homePageLayoutEntity.getApplyType());
        }
        updateOperations.setOnInsert("sourceType", homePageLayoutEntity.getSourceType() == null ? SourceType.CUSTOMER : homePageLayoutEntity.getSourceType());
        updateOperations.setOnInsert("sourceId", homePageLayoutEntity.getSourceId() == null ? "" : homePageLayoutEntity.getSourceId());
        updateOperations.setOnInsert("creatorId", homePageLayoutEntity.getCreatorId());
        updateOperations.setOnInsert("createTime", homePageLayoutEntity.getCreateTime());
        updateOperations.set("scopes", homePageLayoutEntity.getScopes() == null ? Lists.newArrayList() : homePageLayoutEntity.getScopes());
        updateOperations.set("name", homePageLayoutEntity.getName() == null ? "" : homePageLayoutEntity.getName());
        updateOperations.set("homePageCardEntityList", homePageLayoutEntity.getHomePageCardEntityList() == null ? Lists.newArrayList() : homePageLayoutEntity.getHomePageCardEntityList());
        updateOperations.set("description", homePageLayoutEntity.getDescription() == null ? "" : homePageLayoutEntity.getDescription());
        updateOperations.set("isChange", homePageLayoutEntity.isChange());
        updateOperations.set("appTemplateId", homePageLayoutEntity.getAppTemplateId() == null ? "" : homePageLayoutEntity.getAppTemplateId());
        updateOperations.set("status", homePageLayoutEntity.getStatus());
        updateOperations.set("updaterId", homePageLayoutEntity.getUpdaterId());
        updateOperations.set("updateTime", homePageLayoutEntity.getUpdateTime());
        updateOperations.set("customerLayout", homePageLayoutEntity.getCustomerLayout() == null ? "" : homePageLayoutEntity.getCustomerLayout());
        updateOperations.set("dataVersion", homePageLayoutEntity.getDataVersion());
        updateOperations.set("pageLayoutType", homePageLayoutEntity.getPageLayoutType());
        updateOperations.set("priorityLevel", homePageLayoutEntity.getPriorityLevel());
        updateOperations.set("fromOldCrmHomePage", homePageLayoutEntity.getFromOldCrmHomePage());
        updateOperations.set("iconIndex", homePageLayoutEntity.getIconIndex());
        if (CollectionUtils.isNotEmpty(homePageLayoutEntity.getCustomerLayoutList())) {
            updateOperations.set("customerLayoutList", homePageLayoutEntity.getCustomerLayoutList());
        }
        updateOperations.set("pageMultiType", homePageLayoutEntity.getPageMultiType());
        updateOperations.set("defaultLabelIndex", homePageLayoutEntity.getDefaultLabelIndex());
        //沙盒复制 paasApp时，会复制关联的首页，此时会触发并发写库导致的异常：11000 (DuplicateKey) 所以catch处理
        try {
            resultEntity = datastore.findAndModify(query, updateOperations, false, true);
        } catch (RuntimeException e) {
            Throwable cause = e;
            while ((cause.getCause() != null)) {
                cause = cause.getCause();
            }
            if (cause instanceof MongoCommandException) {
                MongoCommandException mongoException = (MongoCommandException) cause;
                if (mongoException.getErrorCode() == 11000) {
                    log.error("there is error when findAndModify:{}", mongoException.getErrorMessage());
                } else {
                    throw e;
                }
            } else if (cause instanceof DuplicateKeyException) {
                DuplicateKeyException mongoException = (DuplicateKeyException) cause;
                log.error("there is error when findAndModify:{}", mongoException.getErrorMessage());
                resultEntity = getHomePageLayoutById(homePageLayoutEntity.getLayoutId(), null);
            } else {
                throw e;
            }
        }
        return resultEntity;
    }

    @Override
    public HomePageLayoutEntity updateHomePageLayout(int employeeId, HomePageLayoutTO homePageLayoutTO) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("layoutId").equal(homePageLayoutTO.getLayoutId());

        UpdateOperations<HomePageLayoutEntity> updateOperations = datastore.createUpdateOperations(HomePageLayoutEntity.class);
        List<String> homePageCardsEntity = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(homePageLayoutTO.getHomePageLayouts())) {
            homePageCardsEntity = buildHomePageCard(homePageLayoutTO.getHomePageLayouts());
        }
        if (CollectionUtils.isNotEmpty(homePageLayoutTO.getScopes())) {
            updateOperations.set("scopes", ScopesUtil.buildScopesToString(homePageLayoutTO.getScopes()));
        }

        updateOperations.set("name", homePageLayoutTO.getName() == null ? EMPTY : homePageLayoutTO.getName());
        updateOperations.set("description", homePageLayoutTO.getDescription() == null ? EMPTY : homePageLayoutTO.getDescription());
        updateOperations.set("homePageCardEntityList", homePageCardsEntity);
        updateOperations.set("updaterId", employeeId);
        updateOperations.set("updateTime", new Date());
        updateOperations.set("isChange", true);
        if (homePageLayoutTO.getCustomerLayout() != null) {
            updateOperations.set("customerLayout", JSONObject.toJSONString(homePageLayoutTO.getCustomerLayout()));
        }
        if (CollectionUtils.isNotEmpty(homePageLayoutTO.getCustomerLayoutList())) {
            updateOperations.set("customerLayoutList", homePageLayoutTO.getCustomerLayoutList().stream().map(item -> JSONObject.toJSONString(item)).collect(Collectors.toList()));

        }
        updateOperations.set("dataVersion", homePageLayoutTO.getDataVersion());
        updateOperations.set("pageLayoutType", homePageLayoutTO.getPageLayoutType());
        updateOperations.set("priorityLevel", homePageLayoutTO.getPriorityLevel());
        return datastore.findAndModify(query, updateOperations, false);
    }

    @Override
    public void batchUpdate(User user, List<HomePageLayoutEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        entityList.forEach(entity -> {
            Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
            query.field("tenantId").equal(user.getTenantId());
            query.field("_id").equal(entity.getLayoutId());

            UpdateOperations<HomePageLayoutEntity> updateOperations = datastore.createUpdateOperations(HomePageLayoutEntity.class);
            if (Objects.nonNull(entity.getName())) {
                updateOperations.set("name", entity.getName());
            }
            if (Objects.nonNull(entity.getDescription())) {
                updateOperations.set("description", entity.getDescription());
            }
            if (Objects.nonNull(entity.getCustomerLayout())) {
                updateOperations.set("customerLayout", entity.getCustomerLayout());
            }
            if (Objects.nonNull(entity.getCustomerLayoutList())) {
                updateOperations.set("customerLayoutList", entity.getCustomerLayoutList());
            }
            if (Objects.nonNull(entity.getThemeLayoutApiName())) {
                updateOperations.set("themeLayoutApiName", entity.getThemeLayoutApiName());
            }
            if (Objects.nonNull(entity.getObjectApiName())) {
                updateOperations.set("objectApiName", entity.getObjectApiName());
            }
            if (Objects.nonNull(entity.getNeedLogin())) {
                updateOperations.set("needLogin", entity.getNeedLogin());
            } else {
                updateOperations.unset("needLogin");
            }
            if (Objects.nonNull(entity.getThemeStyleApiName())) {
                updateOperations.set("themeStyleApiName", entity.getThemeStyleApiName());
            } else {
                updateOperations.unset("themeStyleApiName");
            }
            updateOperations.set("isChange", true);
            updateOperations.set("updaterId", user.getUserId());
            updateOperations.set("updateTime", new Date());
            datastore.findAndModify(query, updateOperations);
        });
    }

    @Override
    public HomePageLayoutEntity makeHomePageFormal(String layoutId, int employeeId, String templeId, int appType) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("layoutId").equal(layoutId);

        UpdateOperations<HomePageLayoutEntity> updateOperations = datastore.createUpdateOperations(HomePageLayoutEntity.class);
        updateOperations.set("updaterId", employeeId);
        updateOperations.set("updateTime", new Date());
        updateOperations.set("status", Status.FORMAL);
        updateOperations.set("appTemplateId", templeId);
        updateOperations.set("appType", appType);
        return datastore.findAndModify(query, updateOperations, false);
    }

    @Override
    public HomePageLayoutEntity getHomePageLayoutByName(int tenantId, int employeeId, String appId, String name, int layoutType) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("status").notEqual(Status.TEMPORARY);
        query.field("layoutType").equal(layoutType);
        query.field("name").equal(name);
        query.field("appId").equal(appId);
        if (layoutType == LayoutType.PERSONAL) {
            query.field("creatorId").equal(employeeId);
        }

        return query.get();
    }

    @Override
    public List<HomePageLayoutEntity> getEmployeeHomePageLayoutList(int tenantId, String appId, List<String> scopes, boolean enablePersonPageConfig) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("scopes").hasAnyOf(scopes);
        query.field("status").equal(Status.ENABLE);
        query.field("appId").equal(appId);

        if (!enablePersonPageConfig) {
            query.field("layoutType").notEqual(LayoutType.PERSONAL);
        }


        return query.order("-createTime").asList();
    }

    @Override
    public List<HomePageLayoutEntity> getEmployeeHomePageLayoutListByFilter(int tenantId, List<String> scopes, Filter filter) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("scopes").hasAnyOf(scopes);

        filter.addFilter(query);

        return query.order("-createTime").asList();
    }

    @Override
    public List<HomePageLayoutEntity> getHomePageLayoutList(int tenantId, String appId) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("status").in(Lists.newArrayList(Status.ENABLE, Status.DISABLE));
        query.field("appId").equal(appId);
        query.field("layoutType").notEqual(LayoutType.PERSONAL);

        return query.order("-createTime").asList();
    }

    @Override
    public List<HomePageLayoutEntity> getHomePageLayoutListByFilter(int tenantId, Filter filter) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("status").in(Lists.newArrayList(Status.ENABLE, Status.DISABLE, Status.TEMPORARY));
        filter.addFilter(query);

        return query.order("-createTime").asList();
    }

    @Override
    public List<HomePageLayoutEntity> getHomePageLayoutListBytenantId(int tenantId, Filter filter) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        filter.addFilter(query);

        return query.order("-createTime").asList();
    }

    @Override
    public long getPageCount(String appId, int tenantId, int layoutType, int employeeId) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("status").notEqual(Status.TEMPORARY);
        if (layoutType != 0) {
            query.field("layoutType").equal(layoutType);
        }
        query.field("appId").equal(appId);
        query.field("sourceType").equal(SourceType.CUSTOMER);
        if (employeeId != 0) {
            query.field("creatorId").equal(employeeId);
        }
        query.field("fromOldCrmHomePage").doesNotExist().or(query.criteria("fromOldCrmHomePage").equal(false));
        return query.countAll();
    }


    @Override
    public HomePageLayoutEntity updateHomePageStatus(String layoutId, int status, int employeeId) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("layoutId").equal(layoutId);
        UpdateOperations<HomePageLayoutEntity> updateOperations = datastore.createUpdateOperations(HomePageLayoutEntity.class);
        updateOperations.set("status", status);
        updateOperations.set("updaterId", employeeId);
        updateOperations.set("updateTime", new Date());

        return datastore.findAndModify(query, updateOperations, false);
    }

    @Override
    public HomePageLayoutEntity getHomePageLayoutBySourceId(int tenantId, String sourceId, String sourceType) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("sourceId").equal(sourceId);
        if (StringUtils.isNotEmpty(sourceType)) {
            query.field("sourceType").equal(sourceType);
        }

        return query.get();
    }

    @Override
    public HomePageLayoutEntity getCreatorId(String layoutId) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("layoutId").equal(layoutId);
        query.field("status").notEqual(Status.TEMPORARY);

        return query.get();
    }

    @Override
    public HomePageLayoutEntity getSystemHomePageLayoutEntity(int tenantId, String appId, List<String> scopes) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("appId").equal(appId);
        query.field("layoutType").equal(LayoutType.SYSTEM);
        query.field("scopes").equal(scopes);

        return query.get();
    }

    @Override
    public List<HomePageLayoutEntity> getHomePageForConsole(int tenantId, String appId, int layoutType) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        if (tenantId > 0) {
            query.field("tenantId").equal(tenantId);
        }
        if (!StringUtils.isEmpty(appId)) {
            query.field("appId").equal(appId);
        }
        if (layoutType > 0) {
            query.field("layoutType").equal(layoutType);
        }
        return query.order("-updateTime").asList();
    }

    @Override
    public List<HomePageLayoutEntity> getHomePageForConsole(int tenantId, String appId, List<Integer> layoutTypes) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        if (tenantId > 0) {
            query.field("tenantId").equal(tenantId);
        }
        if (!StringUtils.isEmpty(appId)) {
            query.field("appId").equal(appId);
        }
        if (CollectionUtils.isNotEmpty(layoutTypes)) {
            query.field("layoutType").in(layoutTypes);
        }
        return query.order("-updateTime").asList();
    }

    @Override
    public List<HomePageLayoutEntity> getCustomerLayoutByApiNames(int tenantId, List<String> apiNames) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").in(apiNames);
        query.field("status").equal(Status.ENABLE);

        return query.order("-updateTime").asList();
    }

    @Override
    public List<HomePageLayoutEntity> findByApiNamesIncludeDisable(int tenantId, List<String> apiNames) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").in(apiNames);
        query.field("status").in(Lists.newArrayList(Status.ENABLE, Status.DISABLE));
        return query.order("createTime").asList();
    }

    @Override
    public HomePageLayoutEntity getHomePageByTenantIdAndApiName(int tenantId, String apiName, Filter filter) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").equal(apiName);
        if (filter != null) {
            filter.addFilter(query);
        }
        return query.get();
    }

    @Override
    public HomePageLayoutEntity findAndModifyHomePage(int tenantId, String apiName, HomePageLayoutEntity homePageLayoutEntity) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").equal(apiName);

        UpdateOperations<HomePageLayoutEntity> updateOperations = datastore.createUpdateOperations(HomePageLayoutEntity.class);
        updateOperations.setOnInsert("layoutId", TempleIdUtil.buildId(tenantId));
        updateOperations.setOnInsert("tenantId", tenantId);
        updateOperations.setOnInsert("apiName", apiName);
        updateOperations.setOnInsert("layoutType", homePageLayoutEntity.getLayoutType());
        updateOperations.setOnInsert("appId", homePageLayoutEntity.getAppId());
        updateOperations.setOnInsert("appType", homePageLayoutEntity.getAppType());
        updateOperations.setOnInsert("sourceType", homePageLayoutEntity.getSourceType());
        updateOperations.setOnInsert("sourceId", homePageLayoutEntity.getSourceId() == null ? "" : homePageLayoutEntity.getSourceId());
        updateOperations.setOnInsert("creatorId", homePageLayoutEntity.getCreatorId());
        updateOperations.setOnInsert("createTime", homePageLayoutEntity.getCreateTime());
        updateOperations.set("scopes", homePageLayoutEntity.getScopes() == null ? Lists.newArrayList() : homePageLayoutEntity.getScopes());
        updateOperations.set("name", homePageLayoutEntity.getName() == null ? "" : homePageLayoutEntity.getName());
        updateOperations.set("homePageCardEntityList", homePageLayoutEntity.getHomePageCardEntityList() == null ? Lists.newArrayList() : homePageLayoutEntity.getHomePageCardEntityList());
        updateOperations.set("description", homePageLayoutEntity.getDescription() == null ? "" : homePageLayoutEntity.getDescription());
        updateOperations.set("isChange", homePageLayoutEntity.isChange());
        updateOperations.set("appTemplateId", homePageLayoutEntity.getAppTemplateId() == null ? "" : homePageLayoutEntity.getAppTemplateId());
        updateOperations.set("status", homePageLayoutEntity.getStatus());
        updateOperations.set("updaterId", homePageLayoutEntity.getUpdaterId());
        updateOperations.set("updateTime", homePageLayoutEntity.getUpdateTime());
        updateOperations.set("customerLayout", homePageLayoutEntity.getCustomerLayout() == null ? "" : homePageLayoutEntity.getCustomerLayout());
        updateOperations.set("dataVersion", homePageLayoutEntity.getDataVersion());
        updateOperations.set("pageLayoutType", homePageLayoutEntity.getPageLayoutType());
        updateOperations.set("priorityLevel", homePageLayoutEntity.getPriorityLevel());
        if (CollectionUtils.isNotEmpty(homePageLayoutEntity.getCustomerLayoutList())) {
            updateOperations.set("customerLayoutList", homePageLayoutEntity.getCustomerLayoutList());
        }
        updateOperations.set("iconIndex", homePageLayoutEntity.getIconIndex());
        updateOperations.set("pageMultiType", homePageLayoutEntity.getPageMultiType());
        updateOperations.set("defaultLabelIndex", homePageLayoutEntity.getDefaultLabelIndex());
        if (homePageLayoutEntity.getApplyType() != null) {
            updateOperations.setOnInsert("applyType", homePageLayoutEntity.getApplyType());
        }

        return datastore.findAndModify(query, updateOperations, false, true);
    }

    @Override
    public HomePageLayoutEntity updateHomePageStatus(int tenantId, String apiName, int status, int employeeId) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").equal(apiName);

        UpdateOperations<HomePageLayoutEntity> updateOperations = datastore.createUpdateOperations(HomePageLayoutEntity.class);
        updateOperations.set("status", status);
        updateOperations.set("updaterId", employeeId);
        updateOperations.set("updateTime", new Date());

        return datastore.findAndModify(query, updateOperations, false);
    }

    @Override
    public void deleteCustomerPage(int tenantId, int appType) {

        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("appType").equal(appType);

        UpdateOperations<HomePageLayoutEntity> updateOperations = datastore.createUpdateOperations(HomePageLayoutEntity.class);
        updateOperations.set("status", Status.TEMPORARY);

        datastore.update(query, updateOperations, false);
    }

    @Override
    public void deleteHomePageLayouts(int tenantId, int appType, String appId) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("appType").equal(appType);
        query.field("appId").equal(appId);

        UpdateOperations<HomePageLayoutEntity> updateOperations = datastore.createUpdateOperations(HomePageLayoutEntity.class);
        updateOperations.set("status", Status.TEMPORARY);

        datastore.update(query, updateOperations, false);
    }

    @Override
    public void deleteByIds(User user, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        query.field("_id").in(ids);
        query.field("status").notEqual(Status.TEMPORARY);
        UpdateOperations<HomePageLayoutEntity> updateOperations = datastore.createUpdateOperations(HomePageLayoutEntity.class);
        updateOperations.set("status", Status.TEMPORARY);
        updateOperations.set("updaterId", user.getUserId());
        updateOperations.set("updateTime", new Date());
        datastore.update(query, updateOperations);
    }

    @Override
    public List<HomePageLayoutEntity> getEnableHomePageLayoutList(int tenantId, int appType, String appId, int applyType) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("appType").equal(appType);
        query.field("appId").equal(appId);
        query.field("status").equal(Status.ENABLE);
        query.field("applyType").equal(applyType);

        return query.order("-updateTime").asList();
    }

    @Override
    public void updateHomepage2CustomPage(Integer tenantId, List<String> apiNames, Integer employeeId, String appId) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").in(apiNames);
        query.field("appId").equal(Constant.APP_CRM);
        List<HomePageLayoutEntity> homePageLayoutEntities = query.asList();
        UpdateOperations<HomePageLayoutEntity> updateOperations = datastore.createUpdateOperations(HomePageLayoutEntity.class);
        updateOperations.set("appType", BizType.CUSTOMER.getType());
        updateOperations.set("fromOldCrmHomePage", true);
        updateOperations.set("updaterId", employeeId);
        updateOperations.set("updateTime", new Date());
        updateOperations.set("applyType", 0);
        updateOperations.set("appId", appId);
        datastore.update(query, updateOperations);

    }

    @Override
    public void updateCustomPage2Homepage(Integer tenantId, Integer employeeId, String appId) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("fromOldCrmHomePage").equal(true);
        query.field("appType").equal(BizType.CUSTOMER.getType());
        query.field("appId").equal(appId);
        UpdateOperations<HomePageLayoutEntity> updateOperations = datastore.createUpdateOperations(HomePageLayoutEntity.class);
        updateOperations.set("appType", BizType.CRM.getType());
        updateOperations.set("fromOldCrmHomePage", false);
        updateOperations.set("updaterId", employeeId);
        updateOperations.set("appId", Constant.APP_CRM);
        updateOperations.set("updateTime", new Date());
        datastore.update(query, updateOperations);
    }

    @Override
    public void deleteSCRMCustomerPage(Integer enterpriseId) {
        Query<HomePageLayoutEntity> query = datastore.createQuery(HomePageLayoutEntity.class);
        query.field("fromOldCrmHomePage").equal(true);
        query.field("appType").equal(BizType.CUSTOMER.getType());
        query.field("appId").equal("PortalPage");
        query.field("applyType").equal(0);

        UpdateOperations<HomePageLayoutEntity> updateOperations = datastore.createUpdateOperations(HomePageLayoutEntity.class);
        updateOperations.set("status", Status.TEMPORARY);

        datastore.update(query, updateOperations, false);
    }

    private List<String> buildHomePageCard(List<HomePageLayoutCard> homePageLayoutCardList) {
        List<String> homePageCardsEntity = Lists.newArrayList();
        homePageLayoutCardList.stream().forEach(homePageCard -> {
            if (homePageCard != null) {
                if (CollectionUtils.isEmpty(homePageCard.getHomePageLayoutFilters())) {
                    homePageCard.setHomePageLayoutFilters(null);
                }
                if (CollectionUtils.isEmpty(homePageCard.getHomePageLayoutTools())) {
                    homePageCard.setHomePageLayoutTools(null);
                }
                homePageCardsEntity.add(gson.toJson(homePageCard));
            }
        });
        return homePageCardsEntity;
    }

    @Override
    public void batchSave(User user, List<HomePageLayoutEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        entityList.forEach(entity -> {
            if (StringUtils.isBlank(entity.getLayoutId())) {
                entity.setLayoutId(TempleIdUtil.buildId(user.getTenantId()));
            }
            entity.setTenantId(user.getTenantId());
            entity.setCreatorId(user.getUserId());
            entity.setCreateTime(new Date());
            entity.setUpdaterId(user.getUserId());
            entity.setUpdateTime(entity.getCreateTime());
        });
        datastore.save(entityList);
    }
}
