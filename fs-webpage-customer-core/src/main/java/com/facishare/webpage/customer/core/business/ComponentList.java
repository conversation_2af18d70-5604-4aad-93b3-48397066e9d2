package com.facishare.webpage.customer.core.business;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.core.config.CustomerCoreConfig;
import com.facishare.webpage.customer.core.model.Component;
import com.facishare.webpage.customer.core.model.ComponentDto;
import com.github.autoconf.ConfigFactory;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Created by she<PERSON> on 19/12/11.
 */
public  class ComponentList {

    @Resource
    private CustomerCoreConfig customerCoreConfig;

    private List<Component> componentList;

    private String configName;

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public ComponentList() {
    }

    public ComponentList(String configName) {
        this.configName = configName;
    }


    public void init() {
        ConfigFactory.getInstance().getConfig(configName, iConfig -> {
            componentList = JSONObject.parseArray(iConfig.getString(), Component.class);
        });
    }

    public List<ComponentDto> getComponentDtoList() {
        return customerCoreConfig.convert2Dto(componentList);
    }

    public List<ComponentDto> mergeRegiserComponentDtoList(Map<String, List<Menu>> menus) {
        return customerCoreConfig.convert2Dto(componentList,menus);
    }


}
