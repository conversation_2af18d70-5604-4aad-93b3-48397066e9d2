package com.facishare.webpage.customer.api.model;

import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by zhangyu on 2020/11/12
 */
@Data
@Builder
public class PaaSAppVO {

    private String appId;

    private List<String> scopeNameList;

    private List<Scope> scopeList;
    private List<ScopeForCross> scopeListForCross;

    private String name;

    private List<String> searchWords;

    private String description;

    private String icon;
    /**
     * icon统一使用iconIndex
     */
    private String iconIndex;
    /**
     * 上传图标信息
     * IconType
     */
    private String uploadIconType;

    private String sourceType;

    private int status;

    private String createName;

    /**
     * 是否允许该应用 使用个人级应用视图
     */
    private boolean useUserPageTempleFlag = false;
    /**
     * 应用类型
     */
    private int appType;
    /**
     * 适用端
     */
    private String accessType;

    /**
     * 应用web端跳转地址
     */
    private String webUrl;

    private String webManagerUrl;
    private String appUrl;
    private int showPaasPage;
    //应用管理员，目前只有互联应用有用到
    private List<Scope> admins;
    //不能设置互联应用管理员的提示语   提示语不为空时，前端不显示设置管理员按钮
    private String unableSetManagerText;
    private Boolean supportExtendObject;

    private Integer tenantId;

    private Long createTime;
    private Long updateTime;
    /*
     * 是否支持关联CRM对象
     */
    private Boolean associationCrmObject;
    private String supportIdentityTypes;
    private Boolean supportNoFsAccount;
    private Boolean hasLinkAppAdmin = true;

    private Integer manageMode;
    private Integer authedRoleNum;

    /**
     * parentAppId
     */
    private String parentAppId;
    private String plugins;
    private Integer availableTime;

    private Boolean customIcon;


    @Builder.Default
    private List<String> namePreTransKeyList = Lists.newArrayList();
    @Builder.Default
    private List<String> descPreTransKeyList = Lists.newArrayList();


}
