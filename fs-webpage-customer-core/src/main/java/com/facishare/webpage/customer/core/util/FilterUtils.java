package com.facishare.webpage.customer.core.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.arg.BatchGetDepartmentByPrincipalArg;
import com.facishare.organization.api.model.department.result.BatchGetDepartmentByPrincipalResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.paas.I18N;
import com.facishare.webpage.customer.api.model.GetGlobalFilter;
import com.facishare.webpage.customer.api.utils.I18NKey;
import com.facishare.webpage.customer.core.resource.BiReportResource;
import com.facishare.webpage.customer.core.model.DateFilter;
import com.facishare.webpage.customer.core.model.SelectorFilter;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2021/2/23
 */
public class FilterUtils {

    private static Logger logger = LoggerFactory.getLogger(FilterUtils.class);

    @Resource
    private DepartmentProviderService departmentProviderService;

    @Resource
    private BiReportResource biReportResource;

    public List<JSONObject> covertFilters(int tenantId, Integer employeeId, Long outerUId, List<JSONObject> filters) {
        if (CollectionUtils.isEmpty(filters)) {
            return buildDefaultFilters(employeeId, outerUId);
        }
        for (JSONObject jsonObject : filters) {
            if (jsonObject.getString("filterType").equals("selector")) {
                JSONObject selectorData = jsonObject.getJSONObject("filterData");
                jsonObject.put("filterData", covertSelector(tenantId, employeeId, outerUId, selectorData));
            }
        }
        return filters;
    }

    public JSONObject covertSelector(int tenantId, Integer employeeId, Long outerUId, JSONObject selector) {
        if (selector == null) {
            //兼容前端错误： 偶现filter会丢失filterData字段
            return null;
        }
        Integer type = selector.getInteger("type");
        JSONArray defaultEmpsAndDeps = selector.getJSONArray("empsAndDeps");
        if (defaultEmpsAndDeps == null) {
            defaultEmpsAndDeps = new JSONArray();
        }
        if (type == null) {
            if (CollectionUtils.isEmpty(defaultEmpsAndDeps)) {
                return null;
            } else {
                return selector;
            }
        }

        switch (type) {
            case 1:
                SelectorFilter.EmpsAndDep empsAndDep = buildPersonal(employeeId, outerUId);
                if (null != empsAndDep) {
                    defaultEmpsAndDeps.addAll(Lists.newArrayList(JSON.toJSON(empsAndDep)));
                    defaultEmpsAndDeps = removeDuplicates(defaultEmpsAndDeps);
                }
                selector.put("empsAndDeps", defaultEmpsAndDeps);
                return selector;
            case 2:
                selector.put("isAll", true);
                return selector;
            case 3:
                List<SelectorFilter.EmpsAndDep> empsAndDeps = getResponsibleDep(tenantId, employeeId);
                if (CollectionUtils.isEmpty(empsAndDeps)) {
                    SelectorFilter.EmpsAndDep personal = buildPersonal(employeeId, outerUId);
                    empsAndDeps.add(personal);
                }
                if (CollectionUtils.isNotEmpty(empsAndDeps)) {
                    for (SelectorFilter.EmpsAndDep x : empsAndDeps) {
                        defaultEmpsAndDeps.add(JSON.toJSON(x));
                    }
                    defaultEmpsAndDeps = removeDuplicates(defaultEmpsAndDeps);
                }
                selector.put("empsAndDeps", defaultEmpsAndDeps);
                return selector;
            default:
                return selector;
        }
    }


    public List<JSONObject> covertFilters(int tenantId, Integer employeeId, String enterpriseAccount, Long outerUId,
                                          List<JSONObject> filters, JSONObject globalFilter, String specifiedClientType, boolean isNeedBiFilter) {
        if (CollectionUtils.isEmpty(filters)) {
            return buildDefaultFilters(employeeId, outerUId);
        }
        for (JSONObject jsonObject : filters) {
            if (jsonObject.getString("filterType").equals("selector")) {
                JSONObject selectorData = jsonObject.getJSONObject("filterData");
                jsonObject.put("filterData", covertSelector(tenantId, employeeId, enterpriseAccount, outerUId,
                        selectorData, globalFilter, specifiedClientType, isNeedBiFilter));
            }
        }
        return filters;
    }


    public JSONObject covertSelector(int tenantId, Integer employeeId, String enterpriseAccount, Long outerUId,
                                     JSONObject selector, JSONObject globalFilter, String specifiedClientType, boolean isNeedBIFilter) {
        if (selector == null) {
            //兼容前端错误： 偶现filter会丢失filterData字段
            return null;
        }
        Integer type = selector.getInteger("type");
        JSONArray defaultEmpsAndDeps = selector.getJSONArray("empsAndDeps");
        if (defaultEmpsAndDeps == null) {
            defaultEmpsAndDeps = new JSONArray();
        }

        if (type == null) {
            if (CollectionUtils.isEmpty(defaultEmpsAndDeps)) {
                return null;
            } else {
                return selector;
            }
        }
        if (isNeedBIFilter
                && Objects.nonNull(globalFilter)
                && StringUtils.isNotEmpty(specifiedClientType)
                && Lists.newArrayList(ClientTypeEnum.iOS.getValue(), ClientTypeEnum.Android.getValue()).contains(specifiedClientType)) {
            List<GetGlobalFilter.EmployeeAndCircles> employeeAndCircles;
            try {
                employeeAndCircles = getBiConvertFilter(tenantId, employeeId, enterpriseAccount, globalFilter);
            } catch (Exception e) {
                logger.error("getBiConvertFilter error! tenantId:{},employeeId:{},enterpriseAccount:{},globalFilter:{}", tenantId, employeeId, enterpriseAccount, globalFilter, e);
                return convertFilter(tenantId, employeeId, type, defaultEmpsAndDeps, outerUId, selector);
            }
            selector.put("empsAndDeps", employeeAndCircles);
            return selector;
        }

        return convertFilter(tenantId, employeeId, type, defaultEmpsAndDeps, outerUId, selector);

    }

    private JSONObject convertFilter(int tenantId, Integer employeeId, Integer type, JSONArray defaultEmpsAndDeps, Long outerUId, JSONObject selector) {
        switch (type) {
            case 1:
                SelectorFilter.EmpsAndDep empsAndDep = buildPersonal(employeeId, outerUId);
                if (null != empsAndDep) {
                    defaultEmpsAndDeps.addAll(Lists.newArrayList(JSON.toJSON(empsAndDep)));
                    defaultEmpsAndDeps = removeDuplicates(defaultEmpsAndDeps);
                }
                selector.put("empsAndDeps", defaultEmpsAndDeps);
                return selector;
            case 2:
                selector.put("isAll", true);
                return selector;
            case 3:
                List<SelectorFilter.EmpsAndDep> empsAndDeps = getResponsibleDep(tenantId, employeeId);
                if (CollectionUtils.isEmpty(empsAndDeps)) {
                    SelectorFilter.EmpsAndDep personal = buildPersonal(employeeId, outerUId);
                    empsAndDeps.add(personal);
                }
                if (CollectionUtils.isNotEmpty(empsAndDeps)) {
                    for (SelectorFilter.EmpsAndDep x : empsAndDeps) {
                        defaultEmpsAndDeps.add(JSON.toJSON(x));
                    }
                    defaultEmpsAndDeps = removeDuplicates(defaultEmpsAndDeps);
                }
                selector.put("empsAndDeps", defaultEmpsAndDeps);
                return selector;
            default:
                return selector;
        }
    }


    private List<GetGlobalFilter.EmployeeAndCircles> getBiConvertFilter(Integer tenantId, Integer employeeId, String enterpriseAccount, JSONObject globalFilter) {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-fs-Enterprise-Id", String.valueOf(tenantId));
        headers.put("X-fs-Employee-Id", String.valueOf(employeeId));
        headers.put("X-fs-Enterprise-Account", enterpriseAccount);
        GetGlobalFilter.Result result = biReportResource.getGlobalFilter(headers, GetGlobalFilter.Arg.builder().globalFilter(globalFilter).build());
        if (200 != result.getCode() || Objects.isNull(result.getData())) {
            throw new RuntimeException("getBiConvertFilter error! code:" + result.getCode() + ",msg:" + result.getMessage());
        }
        return result.getData().getEmployeeAndCircles();
    }

    private JSONArray removeDuplicates(JSONArray jsonArray) {
        JSONArray newJsonArray = new JSONArray();
        HashSet<String> hashSet = new HashSet<>();
        jsonArray.forEach(x -> {
            JSONObject jsonObject = (JSONObject) x;
            if (hashSet.add(String.join("_", jsonObject.getString("id"), jsonObject.getString("type")))) {
                newJsonArray.add(jsonObject);
            }
        });
        return newJsonArray;
    }


    private List<SelectorFilter.EmpsAndDep> getResponsibleDep(int tenantId, Integer employeeId) {
        if (employeeId == null) {
            return Lists.newArrayList();
        }
        BatchGetDepartmentByPrincipalArg arg = new BatchGetDepartmentByPrincipalArg();
        arg.setPrincipalIds(Lists.newArrayList(employeeId));
        arg.setEnterpriseId(tenantId);
        arg.setRunStatus(RunStatus.ACTIVE);
        List<Integer> departmentIds = Lists.newArrayList();
        try {
            BatchGetDepartmentByPrincipalResult result = departmentProviderService.batchGetDepartmentByPrincipal(arg);
            departmentIds = result.getDepartments().stream()
                    .map(departmentDto -> departmentDto.getDepartmentId())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("batchGetDepartmentByPrincipal error", e);
        }
        if (CollectionUtils.isEmpty(departmentIds)) {
            return Lists.newArrayList();
        }
        return departmentIds.stream().map(x -> {
            SelectorFilter.EmpsAndDep empsAndDep = new SelectorFilter.EmpsAndDep();
            empsAndDep.setId(String.valueOf(x));
            empsAndDep.setType(4);
            return empsAndDep;
        }).collect(Collectors.toList());
    }

    private List<JSONObject> buildDefaultFilters(Integer employeeId, Long outerUId) {
        List<JSONObject> filters = Lists.newArrayList();
        SelectorFilter selectorFilter = new SelectorFilter();
        SelectorFilter.EmpsAndDep empsAndDep = buildPersonal(employeeId, outerUId);
        selectorFilter.setEmpsAndDeps(Lists.newArrayList(empsAndDep));
        JSONObject selector = new JSONObject();
        selector.put("filterType", "selector");
        selector.put("filterData", JSON.toJSON(selectorFilter));
        filters.add(selector);

        DateFilter dateFilter = new DateFilter();
        dateFilter.setStartTime(String.valueOf(0));
        dateFilter.setEndTime(String.valueOf(0));
        dateFilter.setDateId(String.valueOf(4));
        dateFilter.setDateType(I18N.text(I18NKey.THIS_MONTH));

        JSONObject date = new JSONObject();
        date.put("filterType", "date");
        date.put("filterData", JSON.toJSON(dateFilter));
        filters.add(date);

        JSONObject globalOfFilter = new JSONObject();
        globalOfFilter.put("enableEmpFilterOfGlobalFilter", 0);
        globalOfFilter.put("enableDateFilterOfGlobalFilter", 0);
        globalOfFilter.put("enablePresetEmpFilterOfGlobalFilter", 0);
        globalOfFilter.put("enablePresetDateFilterOfGlobalFilter", 0);

        JSONObject globalFilter = new JSONObject();
        globalFilter.put("filterType", "pageDefault");
        globalFilter.put("filterData", globalOfFilter);

        filters.add(globalFilter);

        return filters;
    }

    private SelectorFilter.EmpsAndDep buildPersonal(Integer employeeId, Long outerUId) {
        SelectorFilter.EmpsAndDep empsAndDep = new SelectorFilter.EmpsAndDep();
        empsAndDep.setType(0);
        if (outerUId != null) {
            empsAndDep.setId(String.valueOf(outerUId));
        } else {
            empsAndDep.setId(String.valueOf(employeeId));
        }

        return empsAndDep;
    }

    public static void main(String[] args) {
        FilterUtils filterUtils = new FilterUtils();
        JSONObject selectorFilter = new JSONObject();
        selectorFilter.put("filterType", "selector");
        JSONObject filterData = new JSONObject();
        filterData.put("type", 3);
        filterData.put("canEdit", true);
        selectorFilter.put("filterData", filterData);

        List<JSONObject> jsonObjectList = filterUtils.covertFilters(71574, 1017, null, Lists.newArrayList(selectorFilter));
        System.out.println(jsonObjectList);
    }

}
