package com.facishare.webpage.customer.api.model.arg;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.utils.UploadIcon;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@Data
public class CreateLinkAppArg implements Serializable {

    private String name;

    private String description;

    private String icon;
    /**
     * 预置icon统一使用iconIndex
     */
    private String iconIndex;

    private List<Scope> scopeList;
    /**
     * 自定义图标上传
     */
    private UploadIcon uploadIcon;

    /**
     * 应用web端跳转地址 todo
     */
    private String weburl;

    public void valid() {
        if (StringUtils.isEmpty(name) || CollectionUtils.isEmpty(scopeList)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }

    }
}
