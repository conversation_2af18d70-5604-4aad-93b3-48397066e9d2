package com.facishare.webpage.customer.api.model;

import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 用户信息聚合类 包含了 UserInfo 和 OuterUserInfo 的所有属性
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User {

    public static final String SYSTEM_USER_ID = "-10000";
    // UserInfo 属性
    private int tenantId;
    private int userId;
    private String enterpriseAccount;

    // OuterUserInfo 属性
    private Long outUserId;
    private Long outTenantId;
    private String appId;
    private Integer outLinkType;
    private Integer upstreamOwnerId;
    private String outIdentityType;

    public static User of(int tenantId, int userId) {
        return User.builder().tenantId(tenantId).userId(userId).build();
    }

    public static User of(String enterpriseId, Integer employeeId) {
        return User.builder().tenantId(Integer.parseInt(enterpriseId)).userId(employeeId).build();
    }

    public static User getSystemUser(String tenantId) {
        return User.builder().tenantId(Integer.parseInt(tenantId)).userId(Integer.parseInt(SYSTEM_USER_ID)).build();
    }

    /**
     * 将 UserInfo 和 OuterUserInfo 转换为 User 对象
     *
     * @param userInfo      UserInfo对象
     * @param outerUserInfo OuterUserInfo对象
     * @return User对象
     */
    public static User of(UserInfo userInfo, OuterUserInfo outerUserInfo) {
        if (userInfo == null && outerUserInfo == null) {
            return null;
        }

        UserBuilder builder = User.builder();

        // 设置 UserInfo 属性
        if (userInfo != null) {
            builder.tenantId(userInfo.getEnterpriseId())
                    .userId(userInfo.getEmployeeId())
                    .enterpriseAccount(userInfo.getEnterpriseAccount());
        }

        // 设置 OuterUserInfo 属性
        if (outerUserInfo != null) {
            builder.appId(outerUserInfo.getAppId())
                    .outIdentityType(String.valueOf(outerUserInfo.getIdentityType()))
                    .outLinkType(outerUserInfo.getOutLinkType())
                    .outUserId(outerUserInfo.getOutUserId())
                    .outTenantId(outerUserInfo.getOutTenantId())
                    .upstreamOwnerId(outerUserInfo.getUpstreamOwnerId());
        }

        return builder.build();
    }

    public boolean isOutUser() {
        return outTenantId != null && outUserId != null;
    }

    public boolean isOutGuestUser() {
        return this.isOutUser() && Objects.equals(outUserId, 1000000000L)
                && Objects.equals(outTenantId, 1000000000L);
    }
}
