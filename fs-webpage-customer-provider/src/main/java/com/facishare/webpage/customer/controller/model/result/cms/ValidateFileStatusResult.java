package com.facishare.webpage.customer.controller.model.result.cms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ValidateFileStatusResult {
    List<ValidateFileStatusInfo> validateFileList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ValidateFileStatusInfo {
        String apiName;
        boolean status;
    }
}
