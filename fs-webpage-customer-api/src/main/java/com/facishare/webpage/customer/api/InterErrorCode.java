package com.facishare.webpage.customer.api;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/10/21
 */
public class InterErrorCode {
    /**
     * 参数错误
     */
    public static final String FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR = "s302130001";
    /**
     * 首页布局卡片不能为空
     */
    public static final String CARD_CANNOT_BE_EMPTY = "s302130002";
    /**
     * 首页不存在
     */
    public static final String HOME_DOES_NOT_EXIST = "s302130003";
    /**
     * 标题不能为空
     */
    public static final String TITLE_CANNOT_BE_BLANK = "s302130004";
    /**
     * 最多添加10个预置卡片
     */
    public static final String MAX_PRESET_CARDS = "s302130005";
    /**
     * 最多添加10个图表
     */
    public static final String MAX_CHART_CARDS = "s302130006";
    /**
     * 已经存在相同的卡片
     */
    public static final String SAME_CARD = "s302130007";
    /**
     * 筛选场景错误
     */
    public static final String FILTERING_SCENE_ERRORS = "s302130008";
    /**
     * 身份校验失败
     */
    public static final String IDENTITY_VERIFICATION_FAILED = "s302130009";
    /**
     * 保存失败
     */
    public static final String SAVE_FAILED = "s302130010";
    /**
     * 应用模板不存在或已删除
     */
    public static final String PAGE_TEMPLE_NOT_FUND = "s302130011";
    /**
     * 菜单配置不存在或已删除
     */
    public static final String MENU_TEMPLE_NOT_FUND = "s302130012";
    /**
     * 默认菜单不存在
     */
    public static final String MENU_DEFAULT_NOT_FUND = "s302130013";
    /**
     * 菜单数据不存在
     */
    public static final String MENU_DATA_NOT_FUND = "s302130014";
    /**
     * 存在相同名称的首页
     */
    public static final String SAME_NAME_PAGE = "s302130015";
    /**
     * 首页名称不能为空
     */
    public static final String HOMEPAGE_NAME_NOT_EMPTY = "s302130016";
    /**
     * 首页适用范围不能为空
     */
    public static final String HOMEPAGE_SCOPE_NOR_EMPTY = "s302130017";
    /**
     * 已达上限，无法新建
     */
    public static final String MAX_HOMEPAGELAYOUT = "s302130018";
    /**
     * 非法操作
     */
    public static final String ILLEGAL_OPERATION = "s302130019";
    /**
     * 菜单创建已达上限，无法新建
     */
    public static final String MAX_MENU_LIMIT = "s302130020";

    public static final String NO_FUNCTION_PERMISSION = "s302149999";
    /**
     * 存在相同的页面
     */
    public static final String DUPLICATE_HOMEPAGE = "s302130027";
    /**
     * 模板已停用或不存在，请切换其他模板
     */
    public static final String PAGE_DISABLE = "s302130028";
    /**
     * 最多添加{0}个图表
     */
    public static final String MAX_CHART_COUNT = "s302130029";
    /**
     * 最多添加{0}其他
     */
    public static final String MAX_OTHER_COUNT = "s302130030";
    /**
     * 无上游
     */
    public static final String NO_UP_TENANT = "s302130031";
    /**
     * 无互联应用的权限
     */
    public static final String NO_CROSS_APP_PERMISSION = "s302130032";
    /**
     * 自定义菜单项已达上限
     */
    public static final String MAX_CUSTOMER_MENU = "s302130035";
    /**
     * 存在相同apiName的自定义菜单项
     */
    public static final String SAME_CUSTOMER_MENU = "s302130036";
    /**
     * 名称重复，请修改名称
     */
    public static final String SAME_NAME = "s302130037";
    /**
     * 存在相同对象+业务类型的自定义菜单项
     */
    public static final String SAME_OBJECTAPINAME_AND_OBJECTRECORDTYPE = "s302130039";
    /**
     * 请配置首页
     */
    public static final String CONFIGURE_HOMEPAGE = "s302130040";
    /**
     * 请配置菜单
     */
    public static final String CONFIGURE_MENU = "s302130041";
    /**
     * 请配置菜单首页
     */
    public static final String CONFIGURE_MENU_AND_HOMEPAGE = "s302130042";
    /**
     * 请设置全公司适用范围
     */
    public static final String SET_ALL_COMPANY = "s302130043";
    /**
     * 不允许自定义主导航
     */
    public static final String CAN_NOT_CUSTOMER_CHANNEL = "s302130075";
    /**
     * 名称不能为空
     */
    public static final String NAME_CAN_NOT_EMPTY = "s302130076";
    /**
     * 适用范围不能为空
     */
    public static final String SCOPE_CAN_NOT_EMPTY = "s302130077";
    /**
     * 菜单不能为空
     */
    public static final String MENU_CAN_NOT_EMPTY = "s302130078";
    /**
     * 超过主导航菜单的最大限制
     */
    public static final String EXCEED_MAX_LIMIT = "s302130080";
    /**
     * 请配置模板视图
     */
    public static final String PLEASE_CONFIGURE_TEMPLATE_VIEW = "s302130081";
    /**
     * 轮播图图片已失效，请重新配置
     */
    public static final String TN_IMG_NOT_FOUND = "s302130087";

    /**
     * 未授权向互联应用添加对象: {0}
     */
    public static final String ADDING_OBJECTS_TO_LINK_APP_IS_NOT_AUTHORIZED = "s302130089";
    /**
     * 未授权移除互联应用的对象: {0}
     */
    public static final String REMOVING_OBJECTS_OF_LINK_APP_IS_NOT_AUTHORIZED = "s302130090";
    /**
     * 不能单独添加从对象 {0}
     */
    public static final String NOT_ONLY_ADD_DETAIL_OBJ = "s302130091";
    /**
     * 不允许分配
     */
    public static final String NOT_ALLOWED_ALLOCATE = "s302130092";

    /**
     * 不能删除主对象,存在从对象 {0}
     */
    public static final String NOT_REMOVE_MASTER_OBJ = "s302130093";

    public static final String HAS_PRIVILEGE_ROLE_MAST_ASSIGNED_RECORD_TYPE = "s320010001";

    /**
     * 未设置菜单项
     */
    public static final String NO_MENU_ITEM = "s302130094";


}
