package com.facishare.webpage.customer.api.model.result;

import com.facishare.webpage.customer.api.model.ObjectPageComponent;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/7/25
 */
@Data
@Builder
public class FindObjectPageComponentList implements Serializable {
    List<ObjectPageComponent> objectPageComponents;
}
