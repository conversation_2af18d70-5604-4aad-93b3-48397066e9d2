package com.facishare.webpage.customer.controller.impl;

import com.facishare.cep.plugin.annotation.FSClientInfo;
import com.facishare.cep.plugin.annotation.FSOuterUserInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.constant.ErrorMessageI18NKey;
import com.facishare.webpage.customer.api.exception.ValidateException;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.controller.CmsResourceAction;
import com.facishare.webpage.customer.controller.model.arg.cms.*;
import com.facishare.webpage.customer.controller.model.result.cms.ConvertResourceResult;
import com.facishare.webpage.customer.controller.model.result.cms.OperationResult;
import com.facishare.webpage.customer.controller.model.result.cms.ResourceListResult;
import com.facishare.webpage.customer.controller.model.result.cms.ValidateFileStatusResult;
import com.facishare.webpage.customer.core.util.CollectionUtils;
import com.facishare.webpage.customer.service.CmsResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * CMS资源管理接口实现
 */
@Controller
@Slf4j
@RequestMapping("/cms/resource")
public class CmsResourceActionImpl implements CmsResourceAction {

    @Autowired
    private CmsResourceService cmsResourceService;

    @RequestMapping(value = "getListByWorkSpace", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public ResourceListResult getListByWorkSpace(@FSUserInfo UserInfo userInfo,
                                                 @FSClientInfo ClientInfo clientInfo,
                                                 @RequestBody GetResourceListArg arg) {
        User user = RequestContextManager.getUser();
        return cmsResourceService.getResourceListByWorkSpace(user, arg);
    }


    @RequestMapping(value = "batchSaveFileInfo", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public OperationResult batchSaveFileInfo(@FSUserInfo UserInfo userInfo,
                                             @FSClientInfo ClientInfo clientInfo,
                                             @RequestBody SaveFileInfoArg arg) {
        User user = RequestContextManager.getUser();
        cmsResourceService.batchSaveFileInfo(user, arg);
        return OperationResult.success();
    }

    @RequestMapping(value = "batchDelete", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public OperationResult batchDelete(@FSUserInfo UserInfo userInfo,
                                       @FSClientInfo ClientInfo clientInfo,
                                       @RequestBody DeleteResourceArg args) {
        User user = RequestContextManager.getUser();
        List<OperationResult.OperationInfo> operationInfos = cmsResourceService.batchDeleteResource(user, args);
        return OperationResult.builder().operationInfoList(operationInfos).success(true).build();
    }

    @RequestMapping(value = "move", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public OperationResult move(@FSUserInfo UserInfo userInfo,
                                @FSClientInfo ClientInfo clientInfo,
                                @RequestBody MoveResourceArg args) {
        User user = RequestContextManager.getUser();
        cmsResourceService.moveResource(user, args);
        return OperationResult.success();
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public OperationResult update(@FSUserInfo UserInfo userInfo,
                                  @FSClientInfo ClientInfo clientInfo,
                                  @RequestBody UpdateResourceArg arg) {
        User user = RequestContextManager.getUser();
        cmsResourceService.updateResource(user, arg);
        return OperationResult.success();
    }

    @RequestMapping(value = "enable", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public OperationResult enable(@FSUserInfo UserInfo userInfo,
                                  @FSClientInfo ClientInfo clientInfo,
                                  @RequestBody EnableResourceArg args) {
        User user = RequestContextManager.getUser();
        cmsResourceService.enableResource(user, args);
        return OperationResult.success();
    }

    @RequestMapping(value = "disable", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public OperationResult disable(@FSUserInfo UserInfo userInfo,
                                   @FSClientInfo ClientInfo clientInfo,
                                   @RequestBody DisableResourceArg args) {
        User user = RequestContextManager.getUser();
        cmsResourceService.disableResource(user, args);
        return OperationResult.success();
    }


    @RequestMapping(value = "batchGetFileInfo", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public ConvertResourceResult convertResourceByApiName(@FSUserInfo UserInfo userInfo,
                                                          @FSClientInfo ClientInfo clientInfo,
                                                          @FSOuterUserInfo OuterUserInfo outerUserInfo,
                                                          @RequestBody ConvertResourceArg arg) {
        // 参数校验
        if (CollectionUtils.empty(arg.getApiNameList())) {
            log.error("ApiName parameter is required");
            throw ValidateException.fromI18N(ErrorMessageI18NKey.PARAMS_ERROR);
        }
        User user = User.of(userInfo.getEnterpriseId(), userInfo.getEmployeeId());
        // 获取资源URL
        List<ConvertResourceResult.ConvertResourceInfo> resourceInfoList = cmsResourceService.convertResourceByApiName(user, outerUserInfo, arg.getApiNameList());
        return ConvertResourceResult.builder().resourceInfos(resourceInfoList).build();
    }


    @RequestMapping(value = "validateFileStatus", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public ValidateFileStatusResult validateFileStatus(@FSUserInfo UserInfo userInfo,
                                                             @FSClientInfo ClientInfo clientInfo,
                                                             @FSOuterUserInfo OuterUserInfo outerUserInfo,
                                                             @RequestBody ValidateFileStatusArg arg) {
        // 参数校验
        if (CollectionUtils.empty(arg.getApiNameList())) {
            log.error("ApiName parameter is required");
            throw ValidateException.fromI18N(ErrorMessageI18NKey.PARAMS_ERROR);
        }
        User user = User.of(userInfo.getEnterpriseId(), userInfo.getEmployeeId());
        // 校验
        List<ValidateFileStatusResult.ValidateFileStatusInfo> validateFileStatus = cmsResourceService.validateFileStatus(user, outerUserInfo, arg.getApiNameList());
        return ValidateFileStatusResult.builder().validateFileList(validateFileStatus).build();
    }


}
