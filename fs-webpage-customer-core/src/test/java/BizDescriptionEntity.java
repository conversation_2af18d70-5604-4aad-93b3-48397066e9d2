import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class BizDescriptionEntity {

    /**
     * 唯一id
     */
    private String id;

    /**
     * 业务Id
     */
    private String bizId;

    /**
     * 业务类型(暂时只有应用)
     */
    private String category;

    /**
     * i18n名字key
     */
    private String i18nKey;

    /**
     * 默认名字
     */
    private String name;

    /**
     * 应用图标
     */
    private BizDescription.Icon icon;

    /**
     * 点击事件
     */
    private BizDescription.Action action;

    /**
     * 作为菜单的数据
     */
    private BizDescription.MainTab mainTab;

    @NoArgsConstructor
    @Data
    public static class Icon {
        private String path;
    }

    @NoArgsConstructor
    @Data
    public static class Action {
        private String url;
        private String ios;
        private String android;
    }

    @NoArgsConstructor
    @Data
    public static class MainTab {
        /**
         * 能否作为菜单
         */
        private boolean isMainTabCompatible;

        /**
         * 选为菜单后是否需要隐藏应用列表里的此应用
         */
        private boolean hideInAppsCenter = true;

        /**
         * 菜单上的action
         */
        private BizDescription.MainTab.Page page;

        /**
         * 菜单选中和未选中的图标
         */
        private BizDescription.MainTab.IconX icon;

        /**
         * 670前的图标
         */
        private BizDescription.MainTab.IconX iconBefore670;

        @NoArgsConstructor
        @Data
        public static class Page {
            private String url;
            private String ios;
            private String android;
        }

        @NoArgsConstructor
        @Data
        public static class IconX {
            private String normal;
            private String highlight;
        }
    }
}
