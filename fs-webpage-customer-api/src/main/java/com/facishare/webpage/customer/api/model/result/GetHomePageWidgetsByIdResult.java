package com.facishare.webpage.customer.api.model.result;

import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.facishare.webpage.customer.api.model.PageWidgetVO;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2019/9/11
 */
@Data
public class GetHomePageWidgetsByIdResult implements Serializable {
    /**
     * 页面中的组件
     */
    private List<PageWidgetVO> widgetList;
}
