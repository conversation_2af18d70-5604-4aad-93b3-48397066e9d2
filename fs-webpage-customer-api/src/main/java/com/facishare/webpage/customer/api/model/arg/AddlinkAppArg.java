package com.facishare.webpage.customer.api.model.arg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddlinkAppArg implements Serializable {
    private static final long serialVersionUID = 1L;

    private String linkAppId;
    private Integer tenantId;
    private Integer status;



}
