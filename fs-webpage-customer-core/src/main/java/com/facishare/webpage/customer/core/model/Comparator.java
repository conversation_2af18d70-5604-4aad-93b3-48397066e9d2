package com.facishare.webpage.customer.core.model;

import com.facishare.webpage.customer.core.util.CollectionUtils;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.apache.commons.collections4.CollectionUtils.containsAny;

public enum Comparator {
    IN {
        @Override
        public boolean doCompare(Object leftValue, Object rightValue) {
            List<String> values = (List<String>) rightValue;
            if (leftValue instanceof Collection) {
                return values.containsAll((Collection) leftValue);
            }
            return values.contains(leftValue.toString());
        }
    },
    NIN {
        @Override
        public boolean doCompare(Object leftValue, Object rightValue) {
            return !IN.doCompare(leftValue, rightValue);
        }
    },
    EQ {
        @Override
        public boolean isConvertString() {
            return true;
        }

        @Override
        public boolean doCompare(Object leftValue, Object rightValue) {
            if (!(leftValue instanceof String) || !(rightValue instanceof String)) {
                return false;
            }
            return StringUtils.equals(leftValue.toString(), rightValue.toString());
        }
    },
    N {
        @Override
        public boolean isConvertString() {
            return true;
        }

        @Override
        public boolean doCompare(Object leftValue, Object rightValue) {
            return !EQ.doCompare(leftValue, rightValue);
        }
    },

    HASANYOF {
        @Override
        public boolean doCompare(Object leftValue, Object rightValue) {
            List filterValues = CollectionUtils.nullToEmpty((List) rightValue);
            if (leftValue instanceof Collection) {
                return containsAny((Collection) leftValue, filterValues);
            }
            return filterValues.contains(leftValue.toString());
        }
    },
    NHASANYOF {
        @Override
        public boolean doCompare(Object leftValue, Object rightValue) {
            if (isValueEmpty(leftValue) || isValueEmpty(rightValue)) {
                return true;
            }
            return !HASANYOF.doCompare(leftValue, rightValue);
        }
    };

    public abstract boolean doCompare(Object leftValue, Object rightValue);

    public boolean isConvertString() {
        return false;
    }

    public static boolean isValueEmpty(Object value) {
        if (Objects.isNull(value)) {
            return true;
        }
        if (Strings.isNullOrEmpty(value.toString())) {
            return true;
        }
        if (value instanceof Collection) {
            return CollectionUtils.empty((Collection) value);
        }
        if (value instanceof Map) {
            return CollectionUtils.empty((Map) value);
        }
        return false;
    }
}
