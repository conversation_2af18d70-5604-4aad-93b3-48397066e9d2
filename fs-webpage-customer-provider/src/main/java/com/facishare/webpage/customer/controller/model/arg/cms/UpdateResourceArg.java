package com.facishare.webpage.customer.controller.model.arg.cms;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 更新资源参数
 */
@Data
public class UpdateResourceArg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 更新资源列表
     */
    private List<UpdateResourceItem> resourceList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UpdateResourceItem {

    /**
     * 文件ApiName
     */
    private String apiName;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 状态(0-启用；1-禁用；-1-删除)
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;

    /**
     * 节点类型(dir-文件夹；file-文件)
     */
    private String nodeType;

    /**
     * TNPath / NPath
     */
    private String path;

    /**
     * 类型(image-图片/video-视频/doc-文档/audio-音频)
     */
    private String type;

    /**
     * 互联网文件格式(比如：image/jpeg、image/png)
     */
    private String mimeType;

    private String extra;
    /**
     * 父节点名称
     */
    private String parentApiName;
    }
}
