package com.facishare.webpage.customer.api.model.arg;

import com.facishare.cep.plugin.enums.ClientTypeEnum;
import lombok.Data;

/**
 * <AUTHOR> create by wangsh on 2023/11/28
 */
@Data
public class GetTenantAppStatusArg {
    /**
     * 租户Id
     */
    private int tenantId;
    /**
     * 用户Id
     */
    private int employeeId;
    /**
     * 终端类型
     */
    private ClientTypeEnum clientTypeEnum;
    /**
     * 应用Id
     * 默认值是企信的AppId
     */
    private String appId;
    /**
     * 是否关注终端类型
     */
    private boolean followClientType;

}
