package com.facishare.webpage.customer.api.model.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2020/12/23
 */
@Data
public class QueryVendorComponentsResult implements Serializable {

    private List<ComponentItem> componentItemList;

    @Data
    public static class ComponentItem implements Serializable{
        private String cardId;
        private String appId;
        private String header;
        private String type;
    }

}
