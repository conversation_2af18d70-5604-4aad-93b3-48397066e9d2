package com.facishare.webpage.customer.api.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/14 3:38 下午
 */
@Data
@Builder
public class PaasAppRestVO implements Serializable {

    private String appId;
    private String name;
    private String appIntroduce;
    private String appIntroduceTranslateKey;
    private List<String> preAppIntroduceTranslateKeys;
    private String icon;
    private String jumpUrl;
    private int status;
    //是否有移动应用视图
    private boolean hasAppView;

    //是否有移动应用视图
    private boolean hasWebView;
    private String translateKey;
    private String defaultTranslateKey;
    private List<String> preTranslateKeys;
    private int appType;
    //Map<底导航布局的视图名称,页面id>
    private Map<String, String> footLayoutTemplateMap;
    //Map<web视图名称,页面id>
    private Map<String, String> webTemplateMap;


}
