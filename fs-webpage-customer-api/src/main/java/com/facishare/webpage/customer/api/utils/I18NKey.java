package com.facishare.webpage.customer.api.utils;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Data : 2024/9/30
 * @Description :
 */
public class I18NKey {
    //{0}非空
    public static final String NOT_NULL ="paas.sf.of_not_empty";
    // menus 中的必填字段为空
    public static final String MENU = "webpage.MENU";
    // 默认页面
    public static final String Default_Page ="webpage.Default_Page";
    // 迁移成功
    public static final String MIGRATION_SUCCESSFUL = "webpage.MIGRATION_SUCCESSFUL";
    // 迁移失败，已经重置迁移状态，具体失败原因请联系管理员排查
    public static final String TRANSLATION_MIGRATION_FAILED = "webpage.TRANSLATION_MIGRATION_FAILED";
    // 应用停用提示
    public static final String NOTIFICATION_OF_APPLICATION_DEACTIVATION = "webpage.NOTIFICATION_OF_APPLICATION_DEACTIVATION";
    // 应用启用提示
    public static final String TRANSLATION_APP_ACTIVATED = "webpage.TRANSLATION_APP_ACTIVATED";
    // 应用内部使用权限提示
    public static final String INTERNAL_USE_ONLY_PERMISSION_PROMPT = "webpage.INTERNAL_USE_ONLY_PERMISSION_PROMPT";
    // 不能设置管理员
    public static final String CANNOT_SET_THE_ADMINISTRATOR = "webpage.CANNOT_SET_THE_ADMINISTRATOR";
    // 询价工具
    public static final String INQUIRY_TOOL = "webpage.INQUIRY_TOOL";
    // 筛选器
    public static final String FILTER = "webpage.FILTER";
    // 迁移回滚成功
    public static final String MIGRATION_ROLLBACK_SUCCESSFUL = "webpage.MIGRATION_ROLLBACK_SUCCESSFUL";
    // 租户级视图
    public static final String TENANT_LEVEL_VIEW = "webpage.TENANT_LEVEL_VIEW";
    // 列表页
    public static final String TRANSLATE_LIST_PAGE = "webpage.TRANSLATE_LIST_PAGE";
    // 系统
    public static final String SYSTEM = "webpage.SYSTEM";
    // (已禁用)
    public static final String DISABLED = "webpage.DISABLED";
    // 自定义页面
    public static final String CUSTOM_PAGE = "webpage.CUSTOM_PAGE";
    // 首页
    public static final String HOME_PAGE = "webpage.HOME_PAGE";
    //最近使用
    public static final String RECENT = "最近使用";  // ignoreI18n
    //crm提醒
    public static final String CRM_REMIND = "so.menu.item.CrmRemind";
    // crm待办
    public static final String CRM_TODO = "so.menu.item.CrmToDo";
    // 所有角色
    public static final String TRANSLATE_ALL_CHARACTERS = "webpage.TRANSLATE_ALL_CHARACTERS";
    // 全部业务类型
    public static final String ALL_BUSINESS_TYPES = "webpage.ALL_BUSINESS_TYPES";
    // 自定义页面已被删除或禁用
    public static final String THE_CUSTOM_PAGE_HAS_BEEN_DELETED_OR_DISABLED = "webpage.THE_CUSTOM_PAGE_HAS_BEEN_DELETED_OR_DISABLED";
    // H5页面已被删除或禁用
    public static final String THE_PAGE_HAS_BEEN_DELETED_OR_DISABLED = "webpage.THE_PAGE_HAS_BEEN_DELETED_OR_DISABLED";
    // 本月
    public static final String THIS_MONTH = "webpage.THIS_MONTH";
    // CRM对象已被删除或禁用
    public static final String CRM_OBJECT_TO_BE_TRANSLATED_HAS_BEEN_DELETED_OR_DISABLED = "webpage.CRM_OBJECT_TO_BE_TRANSLATED_HAS_BEEN_DELETED_OR_DISABLED";
    // 本季度
    public static final String THIS_QUARTER = "webpage.THIS_QUARTER";
    // 全部企业
    public static final String ALL_ENTERPRISES = "webpage.ALL_ENTERPRISES";
    // 未分组
    public static final String UNGROUPED = "webpage.UNGROUPED";
    // 菜单入口
    public static final String MENU_ENTRANCE = "webpage.MENU_ENTRANCE";
    // 自定义页面
    public static final String CUSTOMIZE_PAGE = "webpage.CUSTOMIZE_PAGE";
    // 标准主导航
    public static final String STANDARD_MAIN_NAVIGATION = "webpage.STANDARD_MAIN_NAVIGATION";
    // 渠道门户
    public static final String CHANNEL_PORTAL = "webpage.CHANNEL_PORTAL";
    // 快速新建
    public static final String QUICK_CREATE = "webpage.QUICK_CREATE";
    // 搜索框
    public static final String SEARCH_BOX = "webpage.SEARCH_BOX";
    // 顶部工具栏
    public static final String TOP_TOOLBAR = "webpage.TOP_TOOLBAR";
}
