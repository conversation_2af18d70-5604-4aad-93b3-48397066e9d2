package com.facishare.webpage.customer.core.service;

import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.core.model.CalculateUrlArg;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface FileService {
    Map<String, String> batchTnConvertNPath(User user, Set<String> tNPath);

    Map<String, String> batchGetSignatureByNPath(User user, Collection<String> nPath);

    Map<String, String> batchCalculateUrl(User user, List<CalculateUrlArg> args);
}
