package com.facishare.webpage.customer.api.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Data : 2025/3/4
 * @Description : 适用端类型(web-web端；mobile-移动端，默认web)
 */
@Getter
public enum ClientType {
    web("web"),
    mobile("mobile");

    private final String value;

    ClientType(String value) {
        this.value = value;
    }

    public boolean same(String value){
        if(web.equals(this)){
            return StringUtils.isBlank(value) || web.getValue().equals(value);
        }
        return this.getValue().equals(value);
    }
}
