package com.facishare.webpage.customer.controller.model.arg.cms;

import lombok.Data;
import java.io.Serializable;

/**
 * 工作区列表查询参数
 */
@Data
public class WorkSpaceListArg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工作区名称（模糊匹配）
     */
    private String name;

    /**
     * 状态：1-启用，0-禁用，null-不限
     */
    private Integer status;

    /**
     * 页码，从1开始
     */
    private Integer pageNum = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize = 20;
}
