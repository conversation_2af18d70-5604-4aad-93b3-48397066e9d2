package com.facishare.webpage.customer.api.constant;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/17
 */
public enum CardType {
    Preset(1),  //预置
    Chart(2),   //用户自定义统计图
    Report(3),  //用户自定义报表
    Schedule(101),  //日程
    Task(102),  //任务
    Filter(103),    //场景
    Tool(104);  //工具

    private int type;

    CardType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public static boolean isPreset(int type) {

        if (type == CardType.Filter.type || type == CardType.Schedule.type
                || type == CardType.Task.type || type == CardType.Tool.type) {
            return true;
        }
        return false;

    }

    public static boolean isChart(int type) {

        if (type == CardType.Chart.type || type == CardType.Report.type
                || type == CardType.Preset.type) {
            return true;
        }
        return false;

    }

    public static boolean isFilter(int type) {
        if (type == CardType.Filter.type) {
            return true;
        }
        return false;
    }
}
