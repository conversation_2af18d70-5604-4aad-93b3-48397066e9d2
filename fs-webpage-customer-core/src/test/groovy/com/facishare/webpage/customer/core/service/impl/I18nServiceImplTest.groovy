package com.facishare.webpage.customer.core.service.impl

import com.facishare.qixin.i18n.QixinI18nService
import com.facishare.qixin.i18n.resource.OnTimeTranslateValueResource
import com.facishare.qixin.i18n.resource.model.OnTimeTranslate
import com.facishare.webpage.customer.api.constant.TranslateI18nUtils
import com.facishare.webpage.customer.api.model.I18nTrans
import com.facishare.webpage.customer.api.utils.RequestContextManager
import com.facishare.webpage.customer.core.business.ComponentListManager
import com.facishare.webpage.customer.core.config.AllWidgetsConfig
import com.facishare.webpage.customer.core.config.WidgetCollectionConfig
import com.facishare.webpage.customer.core.service.UIPaasLicenseService
import com.facishare.webpage.customer.core.util.WebPageGraySwitch
import com.fxiaoke.common.release.GrayReleaseBiz
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.api.Localization
import com.fxiaoke.i18n.util.LangIndex
import com.google.common.collect.Lists
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

class I18nServiceImplTest extends Specification {

    // 被测试的服务
    I18nServiceImpl service

    // 依赖的模拟对象
    OnTimeTranslateValueResource onTimeTranslateValueResource
    UIPaasLicenseService uiPaasLicenseService
    QixinI18nService qixinI18nService
    ComponentListManager componentListManager
    WidgetCollectionConfig widgetCollectionConfig
    AllWidgetsConfig allWidgetsConfig

    def setupSpec() {
        // 标准I18n模拟设置
        def i18nClient = Mock(I18nClient)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []

        def langIndex = Mock(LangIndex)
        Whitebox.setInternalState(LangIndex, "SINGLETON", langIndex)
        langIndex.getServerSaveIndex(_) >> 0

        // 标准灰度开关模拟
        def grayReleaseBiz = Mock(GrayReleaseBiz)
        Whitebox.setInternalState(WebPageGraySwitch, "biz", grayReleaseBiz)
        grayReleaseBiz.isAllow(*_) >> true
    }

    def setup() {
        // 初始化Mock对象
        onTimeTranslateValueResource = Mock(OnTimeTranslateValueResource)
        uiPaasLicenseService = Mock(UIPaasLicenseService)
        qixinI18nService = Mock(QixinI18nService)
        componentListManager = Mock(ComponentListManager)
        widgetCollectionConfig = Mock(WidgetCollectionConfig)
        allWidgetsConfig = Mock(AllWidgetsConfig)

        // 创建被测试类的实例并注入依赖
        service = new com.facishare.webpage.customer.core.service.impl.I18nServiceImpl()
        service.onTimeTranslateValueResource = onTimeTranslateValueResource
        service.uiPaasLicenseService = uiPaasLicenseService
        service.qixinI18nService = qixinI18nService
        service.componentListManager = componentListManager
        service.widgetCollectionConfig = widgetCollectionConfig
        service.allWidgetsConfig = allWidgetsConfig

        // 设置静态工具类
        GroovyMock(TranslateI18nUtils, global: true)
        GroovyMock(RequestContextManager, global: true)
    }

    /**
     * 测试getCurrentI18nValue方法，正常情况下能正确获取翻译值
     */
    @Unroll
    def "getCurrentI18nValue_正常情况_返回翻译值"() {
        given: "准备测试数据"
        def tenantId = 12345
        def key = "test.key"
        def defaultValue = "默认值"
        def isOnTime = true
        def lang = "zh-CN"
        def isNeedValidateLicense = true
        def expectedValue = "翻译后的值"

        // 创建模拟的本地化对象
        def localization = new Localization()
        localization.set(lang, expectedValue)

        // 创建正确的TranslateInfo对象
        OnTimeTranslate.TranslateInfo translateInfo = Mock(OnTimeTranslate.TranslateInfo)
        translateInfo.toLocalization() >> localization

        // 创建结果对象
        OnTimeTranslate.Result result = new OnTimeTranslate.Result()
        result.setErrorCode("0")

        Map<String, OnTimeTranslate.TranslateInfo> translateInfoMap = new HashMap<>()
        translateInfoMap.put(key, translateInfo)
        result.setResult(translateInfoMap)

        and: "设置模拟行为"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true
        onTimeTranslateValueResource.load(_) >> result

        when: "调用被测方法"
        def actual = service.getCurrentI18nValue(tenantId, key, defaultValue, isOnTime, lang, isNeedValidateLicense)

        then: "验证返回值"
        actual == expectedValue
    }

    /**
     * 测试当key为空时，应返回默认值
     */
    @Unroll
    def "getCurrentI18nValue_空Key_返回默认值"() {
        given: "准备测试数据"
        def tenantId = 12345
        def key = emptyKey
        def defaultValue = "默认值"
        def isOnTime = true
        def lang = "zh-CN"
        def isNeedValidateLicense = true

        when: "调用被测方法"
        def actual = service.getCurrentI18nValue(tenantId, key, defaultValue, isOnTime, lang, isNeedValidateLicense)

        then: "验证返回默认值"
        actual == defaultValue
        0 * onTimeTranslateValueResource.load(_) // 不应调用资源加载

        where:
        emptyKey << [null, "", " "]
    }

    /**
     * 测试无License权限时，返回默认值
     */
    def "getCurrentI18nValue_无License权限_返回默认值"() {
        given: "准备测试数据"
        def tenantId = 12345
        def key = "test.key"
        def defaultValue = "默认值"
        def isOnTime = true
        def lang = "zh-CN"
        def isNeedValidateLicense = true

        and: "设置无License权限"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> false

        when: "调用被测方法"
        def actual = service.getCurrentI18nValue(tenantId, key, defaultValue, isOnTime, lang, isNeedValidateLicense)

        then: "验证返回默认值"
        actual == defaultValue
        0 * onTimeTranslateValueResource.load(_) // 不应调用资源加载
    }

    /**
     * 测试非实时模式下调用工具类获取
     */
    def "getCurrentI18nValue_非实时模式_调用工具类获取"() {
        given: "准备测试数据"
        def tenantId = 12345
        def key = "test.key"
        def defaultValue = "默认值"
        def isOnTime = false  // 非实时模式
        def lang = "zh-CN"
        def isNeedValidateLicense = true
        def expectedValue = "期望值"

        and: "设置模拟行为"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true

        // 创建本地化对象
        def localization = new Localization()
        localization.set(lang, expectedValue)

        // 设置I18nClient的模拟行为
        def i18nClient = Spy(I18nClient)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.get(key, tenantId) >> localization

        when: "调用被测方法"
        def actual = service.getCurrentI18nValue(tenantId, key, defaultValue, isOnTime, lang, isNeedValidateLicense)

        then: "验证结果"
        actual == expectedValue
        0 * onTimeTranslateValueResource.load(_) // 非实时模式不调用资源加载
    }

    /**
     * 测试加载资源时发生异常，应回退到TranslateI18nUtils
     */
    def "getCurrentI18nValue_加载异常_回退到工具类"() {
        given: "准备测试数据"
        def tenantId = 12345
        def key = "test.key"
        def defaultValue = "默认值"
        def isOnTime = true
        def lang = "zh-CN"
        def isNeedValidateLicense = true
        def expectedValue = "异常回退值"

        and: "设置资源加载抛出异常"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true
        onTimeTranslateValueResource.load(_) >> { throw new RuntimeException("模拟异常") }

        // 明确设置TranslateI18nUtils模拟行为返回预期值
        // 创建本地化对象
        def localization = new Localization()
        localization.set(lang, expectedValue)
        def i18nClient = Spy(I18nClient)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.get(key, tenantId) >> localization

        when: "调用被测方法"
        def actual = service.getCurrentI18nValue(tenantId, key, defaultValue, isOnTime, lang, isNeedValidateLicense)

        then: "验证结果"
        actual == expectedValue
    }

    /**
     * 测试errorCode不为0时，应回退到TranslateI18nUtils
     */
    def "getCurrentI18nValue_ErrorCode不为0_回退到工具类"() {
        given: "准备测试数据"
        def tenantId = 12345
        def key = "test.key"
        def defaultValue = "默认值"
        def isOnTime = true
        def lang = "zh-CN"
        def isNeedValidateLicense = true
        def expectedValue = "错误码回退值"

        and: "设置错误码不为0"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true

        OnTimeTranslate.Result result = new OnTimeTranslate.Result()
        result.setErrorCode("1")
        onTimeTranslateValueResource.load(_) >> result

        // 创建本地化对象
        def localization = new Localization()
        localization.set(lang, expectedValue)
        def i18nClient = Spy(I18nClient)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.get(key, tenantId) >> localization

        when: "调用被测方法"
        def actual = service.getCurrentI18nValue(tenantId, key, defaultValue, isOnTime, lang, isNeedValidateLicense)

        then: "验证结果"
        actual == expectedValue
    }

    /**
     * 测试getOnTimeValueIncludeCache方法正常情况
     */
    def "getOnTimeValueIncludeCache_正常情况_返回本地化对象"() {
        given: "准备测试数据"
        def tenantId = 12345
        def keyList = ["key1", "key2"]
        def isNeedValidateLicense = true

        // 创建模拟结果
        def localization1 = Mock(Localization)
        def localization2 = Mock(Localization)

        // 创建TranslateInfo模拟对象，避免NPE和NumberFormatException
        OnTimeTranslate.TranslateInfo translateInfo1 = Mock(OnTimeTranslate.TranslateInfo)
        translateInfo1.toLocalization() >> localization1

        OnTimeTranslate.TranslateInfo translateInfo2 = Mock(OnTimeTranslate.TranslateInfo)
        translateInfo2.toLocalization() >> localization2

        // 创建结果对象
        OnTimeTranslate.Result result = new OnTimeTranslate.Result()
        result.setErrorCode("0")

        Map<String, OnTimeTranslate.TranslateInfo> translateInfoMap = new HashMap<>()
        translateInfoMap.put("key1", translateInfo1)
        translateInfoMap.put("key2", translateInfo2)
        result.setResult(translateInfoMap)

        and: "设置模拟行为"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true
        onTimeTranslateValueResource.load(_) >> result

        // 设置TranslateI18nUtils回退行为
        GroovyMock(TranslateI18nUtils, global: true)
        TranslateI18nUtils.getLocalizationByKey(tenantId, keyList) >> [:]

        when: "通过反射调用私有方法"
        def actual = Whitebox.invokeMethod(service, "getOnTimeValueIncludeCache", tenantId, keyList, isNeedValidateLicense)

        then: "验证结果"
        actual.size() == 2
        actual.keySet() == ["key1", "key2"] as Set
        actual["key1"] == localization1
        actual["key2"] == localization2
    }

    /**
     * 测试getTransValue方法的正常调用情况
     */
    def "getTransValue_正常情况_返回翻译映射"() {
        given: "准备测试数据"
        def tenantId = 12345
        def keyList = ["key1", "key2", "key3"]

        def localization1 = Mock(Localization)
        def localization2 = Mock(Localization)
        def localization3 = Mock(Localization)

        def expectedResult = [
                "key1": localization1,
                "key2": localization2,
                "key3": localization3
        ]

        and: "设置模拟行为"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true
        RequestContextManager.isFromManager() >> false
        RequestContextManager.isFromRest() >> false

        // 设置I18nClient的模拟行为
        def i18nClient = Mock(I18nClient)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.get(keyList, tenantId) >> expectedResult

        when: "调用被测方法"
        def result = service.getTransValue(tenantId, keyList)

        then: "验证结果"
        result.size() == 3
        result.keySet() == ["key1", "key2", "key3"] as Set
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空key列表情况下，返回空映射
     */
    def "getTransValue_空Key列表_返回空映射"() {
        given: "准备测试数据"
        def tenantId = 12345
        def keyList = emptyList
        GroovyMock(I18nClient, global: true)

        when: "调用被测方法"
        def result = service.getTransValue(tenantId, keyList)

        then: "验证结果"
        result.isEmpty()
        0 * I18nClient.getInstance().get(_, _) // 不应调用客户端获取

        where:
        emptyList << [[], null]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试syncTransValue方法，验证正确调用服务同步翻译
     */
    def "syncTransValue_正常情况_成功同步翻译值"() {
        given: "准备测试数据"
        def tenantId = 12345
        def keyToNewName = ["key1": "名称1", "key2": "名称2"]
        def lang = "zh-CN"

        // 创建模拟的Localization列表
        def localization1 = Mock(Localization)
        localization1.getKey() >> "key1"
        localization1.getTenantId() >> tenantId
        localization1.getTags() >> ["server"]
        localization1.get(lang, null) >> "名称1"

        def localization2 = Mock(Localization)
        localization2.getKey() >> "key2"
        localization2.getTenantId() >> tenantId
        localization2.getTags() >> ["server"]
        localization2.get(lang, null) >> "名称2"

        def localizations = [localization1, localization2]

        // 模拟OnTimeTranslate.Result
        def translateInfo1 = Mock(OnTimeTranslate.TranslateInfo)
        translateInfo1.toLocalization() >> localization1
        def translateInfo2 = Mock(OnTimeTranslate.TranslateInfo)
        translateInfo2.toLocalization() >> localization2

        def resultMap = [
                "key1": translateInfo1,
                "key2": translateInfo2
        ]

        def onTimeResult = new OnTimeTranslate.Result()
        onTimeResult.setErrorCode("0")
        onTimeResult.setResult(resultMap)

        and: "设置模拟行为"
        def mockMultiLanguageApp = ["multi_language_app": true]
        uiPaasLicenseService.existModule(tenantId, _) >> mockMultiLanguageApp
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true
        onTimeTranslateValueResource.load(_) >> onTimeResult
        qixinI18nService.save4Translate(localizations, tenantId, false) >> null

        // 为service设置模拟方法，返回构建好的本地化对象
        service.metaClass.buildLocalization = { Integer tid, String k, String v, String l, List<String> t ->
            if (k == "key1") return localization1
            if (k == "key2") return localization2
            return Mock(Localization)
        }

        when: "调用被测方法"
        service.syncTransValue(tenantId, keyToNewName, lang)

        then: "验证qixinI18nService被正确调用"
        noExceptionThrown()
        1 * qixinI18nService.save4Translate(_, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getTransValue_带语言参数_返回指定语言翻译
     */
    def "getTransValue_带语言参数_返回指定语言翻译"() {
        given: "准备测试数据"
        def tenantId = 12345
        def keyList = ["key1", "key2"]
        def lang = "zh-CN"

        def expectedResult = [
                "key1": "翻译值1",
                "key2": "翻译值2"
        ]

        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true
        RequestContextManager.isFromManager() >> true
        RequestContextManager.isFromRest() >> false

        def localization1 = new Localization()
        localization1.set(lang, "翻译值1")

        def localization2 = new Localization()
        localization2.set(lang, "翻译值2")

        def transValueMap = [
                "key1": localization1,
                "key2": localization2
        ]

        qixinI18nService.onTimeLoad(_, _, _) >> transValueMap;

        // 创建本地化对象
        def i18nClient = Spy(I18nClient)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.get(keyList, tenantId) >> transValueMap

        when: "调用被测方法"
        def result = service.getTransValue(tenantId, keyList, lang)

        then: "验证结果"
        result == expectedResult
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOnTimeTransValue方法，实时获取翻译值
     */
    def "getOnTimeTransValue_实时模式_返回翻译值"() {
        given: "准备测试数据"
        def tenantId = 12345
        def keyList = ["key1", "key2"]
        def lang = "zh-CN"
        def isOnTime = true
        def isNeedValidateLicense = true

        def expectedResult = [
                "key1": "实时值1",
                "key2": "实时值2"
        ]

        // 创建本地化对象
        def localization1 = Mock(Localization)
        localization1.get(lang, null) >> "实时值1"

        def localization2 = Mock(Localization)
        localization2.get(lang, null) >> "实时值2"

        // 创建TranslateInfo模拟对象
        def translateInfo1 = Mock(OnTimeTranslate.TranslateInfo)
        translateInfo1.toLocalization() >> localization1

        def translateInfo2 = Mock(OnTimeTranslate.TranslateInfo)
        translateInfo2.toLocalization() >> localization2

        // 创建结果对象
        OnTimeTranslate.Result onTimeResult = new OnTimeTranslate.Result()
        onTimeResult.setErrorCode("0")

        Map<String, OnTimeTranslate.TranslateInfo> resultMap = new HashMap<>()
        resultMap.put("key1", translateInfo1)
        resultMap.put("key2", translateInfo2)
        onTimeResult.setResult(resultMap)

        and: "设置模拟行为"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true
        onTimeTranslateValueResource.load(_) >> onTimeResult

        when: "调用被测方法"
        def result = service.getOnTimeTransValue(tenantId, keyList, lang, isOnTime, isNeedValidateLicense)

        then: "验证结果"
        result == expectedResult
    }

    /**
     * 测试getOnTimeTransValue方法，非实时模式下使用工具类获取值
     */
    def "getOnTimeTransValue_非实时模式_使用工具类获取值"() {
        given: "准备测试数据"
        def tenantId = 12345
        def keyList = ["key1", "key2"]
        def lang = "zh-CN"
        def isOnTime = false
        def isNeedValidateLicense = true

        def expectedResult = [
                "key1": "非实时值1",
                "key2": "非实时值2"
        ]

        and: "设置静态工具类行为"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true

        // 创建本地化对象用于I18nClient.get返回值
        def localization1 = new Localization()
        localization1.set(lang, "非实时值1")
        def localization2 = new Localization()
        localization2.set(lang, "非实时值2")

        // 设置I18nClient的模拟
        def i18nClient = Mock(I18nClient)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        I18nClient.getInstance().get("key1", tenantId) >> localization1
        I18nClient.getInstance().get("key2", tenantId) >> localization2

        when: "调用被测方法"
        def result = service.getOnTimeTransValue(tenantId, keyList, lang, isOnTime, isNeedValidateLicense)

        then: "验证结果"
        result == expectedResult
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试syncTransValueIncludePreKey方法，同步预设翻译键值
     */
    def "syncTransValueIncludePreKey_正常情况_同步翻译值"() {
        given: "准备测试数据"
        def tenantId = 12345
        def lang = "zh-CN"
        def transArg1 = new I18nTrans.TransArg()
        def transArg2 = new I18nTrans.TransArg()
        def transArgList = [transArg1, transArg2]

        and: "模拟buildSyncParam方法"
        service.metaClass.buildSyncParam = { Integer tid, List<I18nTrans.TransArg> args, String language, boolean isReplace ->
            return []
        }

        and: "模拟syncTransValue方法"
        service.metaClass.syncTransValue = { Integer tid, List<Localization> localizations ->
            // 不做任何操作
        }

        when: "调用被测方法"
        service.syncTransValueIncludePreKey(tenantId, transArgList, lang)

        then: "验证结果"
        noExceptionThrown()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试syncTransValueIncludePreKeyV2方法，同步预设翻译键值(V2版本)
     */
    def "syncTransValueIncludePreKeyV2_正常情况_同步翻译值"() {
        given: "准备测试数据"
        def tenantId = 12345
        def lang = "zh-CN"
        def transArg1 = new I18nTrans.TransArg()
        transArg1.setOldKeyList(Lists.newArrayList())
        def transArg2 = new I18nTrans.TransArg()
        transArg2.setOldKeyList(Lists.newArrayList())
        def transArgList = [transArg1, transArg2]

        and: "模拟buildSyncParamV2方法"
        service.metaClass.buildSyncParamV2 = { Integer tid, List<I18nTrans.TransArg> args, String language, boolean isReplace ->
            return []
        }

        and: "模拟syncTransValue方法"
        service.metaClass.syncTransValue = { Integer tid, List<Localization> localizations ->
            // 不做任何操作
        }

        when: "调用被测方法"
        service.syncTransValueIncludePreKeyV2(tenantId, transArgList, lang)

        then: "验证结果"
        noExceptionThrown()
    }

    /**
     * 测试buildLocalization方法
     */
    def "buildLocalization_正常参数_构建本地化对象"() {
        given: "准备测试数据"
        def tenantId = 12345
        def key = "test.key"
        def value = "测试值"
        def lang = "zh-CN"
        def tags = ["server"]

        when: "调用被测方法"
        def result = service.buildLocalization(tenantId, key, value, lang, tags)

        then: "验证结果"
        result != null
        result.getKey() == key
        result.getTenantId() == tenantId
        result.getTags() == tags
        result.get(lang, null) == value
    }

    /**
     * 测试getTransValueIncludePreKey方法，获取包含预设键的翻译值
     */
    def "getTransValueIncludePreKey_正常情况_返回翻译值"() {
        given: "准备测试数据"
        def tenantId = 12345
        def lang = "zh-CN"

        // 创建测试用TransArg
        I18nTrans.TransArg transArg1 = new I18nTrans.TransArg()
        transArg1.setCustomKey("customKey1")
        transArg1.setName("名称1")
        transArg1.setOriginalCustomKey("originalKey1")
        transArg1.setPreKeyList(["preKey1"])

        I18nTrans.TransArg transArg2 = new I18nTrans.TransArg()
        transArg2.setCustomKey("customKey2")
        transArg2.setName("名称2")
        transArg2.setOriginalCustomKey("originalKey2")
        transArg2.setPreKeyList(["preKey2"])

        def transArgList = [transArg1, transArg2]

        // 设置自定义翻译值
        def customTransValue = ["customKey1": "自定义翻译1"]

        and: "设置模拟行为"
        // 模拟静态方法
        GroovyMock(TranslateI18nUtils, global: true)
        TranslateI18nUtils.formatTransKey(tenantId, transArg1) >> transArg1
        TranslateI18nUtils.formatTransKey(tenantId, transArg2) >> transArg2

        RequestContextManager.isFromManager() >> true
        RequestContextManager.isFromRest() >> false

        // 修改service的行为
        // 创建本地化对象
        def localization1 = new Localization()
        localization1.set(lang, "自定义翻译1")
        def localization2 = new Localization()
        localization2.set(lang, "预设翻译2")
        def i18nClient = Spy(I18nClient)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.get("customKey1", tenantId) >> localization1
        i18nClient.get("customKey2", tenantId) >> localization2


        service.metaClass.getRealNameByPreKey = { Integer tid, List<String> preKeys, String name, String language ->
            if (preKeys.contains("preKey2")) {
                return "预设翻译2"
            }
            return name
        }

        when: "调用被测方法"
        def result = service.getTransValueIncludePreKey(tenantId, transArgList, lang)

        then: "验证结果"
        // 修改观察结果
        println "Result map: ${result}"
        // 创建一个包含期望键值的结果映射
        def expectedResult = [
                "customKey1": "自定义翻译1",
                "customKey2": "预设翻译2"
        ]

        result.size() == expectedResult.size()
        result.every { k, v -> expectedResult[k] == v }
    }

    /**
     * 测试getOnTimeTransValue方法正常情况
     */
    def "getOnTimeTransValue_正常情况_返回本地化对象"() {
        given: "准备测试数据"
        def tenantId = 12345
        def keyList = ["key1", "key2"]
        def isNeedValidateLicense = true

        // 创建模拟结果
        def localization1 = Mock(Localization)
        def localization2 = Mock(Localization)

        // 使用Mock避免NumberFormatException
        OnTimeTranslate.TranslateInfo translateInfo1 = Mock(OnTimeTranslate.TranslateInfo)
        translateInfo1.toLocalization() >> localization1

        OnTimeTranslate.TranslateInfo translateInfo2 = Mock(OnTimeTranslate.TranslateInfo)
        translateInfo2.toLocalization() >> localization2

        OnTimeTranslate.Result result = new OnTimeTranslate.Result()
        result.setErrorCode("0")

        Map<String, OnTimeTranslate.TranslateInfo> translateInfoMap = new HashMap<>()
        translateInfoMap.put("key1", translateInfo1)
        translateInfoMap.put("key2", translateInfo2)
        result.setResult(translateInfoMap)

        and: "设置模拟行为"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true
        onTimeTranslateValueResource.load(_) >> result

        when: "通过反射调用私有方法"
        def actual = Whitebox.invokeMethod(service, "getOnTimeTransValue", tenantId, keyList, isNeedValidateLicense)

        then: "验证结果"
        actual.size() == 2
        actual.keySet() == ["key1", "key2"] as Set
        actual["key1"] == localization1
        actual["key2"] == localization2
    }

    /**
     * 测试getOnTimeTransValue方法异常情况
     */
    def "getOnTimeTransValue_异常情况_返回空映射"() {
        given: "准备测试数据"
        def tenantId = 12345
        def keyList = ["key1", "key2"]
        def isNeedValidateLicense = true

        and: "设置模拟行为-抛出异常"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true
        onTimeTranslateValueResource.load(_) >> { throw new RuntimeException("模拟异常") }

        when: "通过反射调用私有方法"
        def actual = Whitebox.invokeMethod(service, "getOnTimeTransValue", tenantId, keyList, isNeedValidateLicense)

        then: "验证结果是空映射"
        actual.size() == 0
    }

    /**
     * 测试syncTransValue方法异常情况
     */
    def "syncTransValue_异常情况_记录错误日志"() {
        given: "准备测试数据"
        def tenantId = 12345
        def keyToNewName = ["key1": "名称1", "key2": "名称2"]
        def lang = "zh-CN"

        // 创建模拟的Localization列表
        def localizations = [Mock(Localization), Mock(Localization)]

        and: "设置模拟行为-抛出异常"
        service.metaClass.buildSyncParam = { Integer tid, Map<String, String> keyMap, String language ->
            return localizations
        }
        qixinI18nService.save4Translate(_, _, _) >> { throw new RuntimeException("模拟异常") }

        when: "调用被测方法"
        service.syncTransValue(tenantId, keyToNewName, lang)

        then: "验证异常被捕获，正常执行完成"
        noExceptionThrown()
    }

    /**
     * 测试getTransValueIncludePreKey方法空入参情况
     */
    @Unroll
    def "getTransValueIncludePreKey_空入参_返回空映射"() {
        when: "调用被测方法"
        def result = service.getTransValueIncludePreKey(tenantId, transArgList, lang)

        then: "验证返回空映射"
        result.isEmpty()

        where:
        tenantId | transArgList | lang
        null     | ["arg"]      | "zh-CN"  // 空租户ID
        12345    | null         | "zh-CN"  // 空参数列表
        12345    | []           | "zh-CN"  // 空参数列表
        12345    | ["arg"]      | null     // 空语言
        12345    | ["arg"]      | ""       // 空语言
    }

    /**
     * 测试formatTransArg方法正常情况
     */
    def "formatTransArg_正常情况_格式化参数列表"() {
        given: "准备测试数据"
        I18nTrans.TransArg validArg = new I18nTrans.TransArg()
        validArg.setCustomKey("customKey")
        validArg.setName("名称")
        validArg.setPreKeyList(["preKey1", "", null])

        I18nTrans.TransArg nullKeyArg = new I18nTrans.TransArg()
        nullKeyArg.setName("名称")

        I18nTrans.TransArg nullNameArg = new I18nTrans.TransArg()
        nullNameArg.setCustomKey("customKey")

        def transArgList = [validArg, nullKeyArg, nullNameArg, null]

        when: "通过反射调用私有方法"
        Whitebox.invokeMethod(service, "formatTransArg", transArgList)

        then: "验证列表被正确格式化"
        transArgList.size() == 1
        transArgList[0] == validArg
        validArg.getPreKeyList().size() == 1
        validArg.getPreKeyList()[0] == "preKey1"
    }

    /**
     * 测试buildSyncParam方法正常情况
     */
    def "buildSyncParam_正常情况_构建同步参数"() {
        given: "准备测试数据"
        def tenantId = 12345
        def keyToNewName = ["key1": "名称1", "key2": "名称2"]
        def lang = "zh-CN"

        // 模拟License检查
        def multiLanguageApp = ["multi_language_app": true]

        // 模拟本地化对象
        def localization1 = Mock(Localization)
        localization1.get(lang, null) >> "旧名称1"

        def localization2 = Mock(Localization)
        localization2.get(lang, null) >> "旧名称2"

        and: "设置模拟行为"
        uiPaasLicenseService.existModule(tenantId, _) >> multiLanguageApp
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true

        // 创建I18nClient模拟对象
        def i18nClient = Mock(I18nClient)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.save4Translate(tenantId, _, false) >> null

        // 创建TranslateInfo和Result对象
        def translateInfo1 = Mock(OnTimeTranslate.TranslateInfo)
        translateInfo1.toLocalization() >> localization1
        def translateInfo2 = Mock(OnTimeTranslate.TranslateInfo)
        translateInfo2.toLocalization() >> localization2

        def resultMap = [
                "key1": translateInfo1,
                "key2": translateInfo2
        ]

        def onTimeResult = new OnTimeTranslate.Result()
        onTimeResult.setErrorCode("0")
        onTimeResult.setResult(resultMap)

        // 设置资源加载行为
        onTimeTranslateValueResource.load(_) >> onTimeResult

        // 直接重写buildSyncParam私有方法
        service.metaClass.buildLocalization = { Integer tid, String k, String v, String l, List<String> t ->
            def loc = Mock(Localization)
            loc.getKey() >> k
            loc.getTenantId() >> tid
            loc.getTags() >> t
            loc.get(l, null) >> v
            return loc
        }

        when: "调用被测方法"
        def result = service.buildSyncParam(tenantId, keyToNewName, lang)

        then: "验证结果"
        result != null
        result.size() == 2
    }

    /**
     * 测试getRealNameByPreKey方法
     */
    def "getRealNameByPreKey_存在预设值_返回预设名称"() {
        given: "准备测试数据"
        def tenantId = 12345
        def preKeyList = ["preKey1", "preKey2"]
        def name = "原始名称"
        def lang = "zh-CN"
        def expectedTranslation = "预设翻译"

        // 创建Localization对象，模拟包含name的数据
        def localization = new Localization()
        localization.set(lang, expectedTranslation)
        localization.setEn(name)

        and: "设置TranslateI18nUtils模拟行为"
        // 创建本地化对象
        def i18nClient = Spy(I18nClient)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        I18nClient.getInstance().get(_, _) >> localization

        when: "调用被测方法"
        def result = service.getRealNameByPreKey(tenantId, preKeyList, name, lang)

        then: "验证结果"
        result == expectedTranslation
    }

    /**
     * 测试getRealNameByPreKey方法 - 无预设值的情况
     */
    def "getRealNameByPreKey_无预设值_返回原始名称"() {
        given: "准备测试数据"
        def tenantId = 12345
        def preKeyList = ["preKey1", "preKey2"]
        def name = "原始名称"
        def lang = "zh-CN"

        // 返回空的Localization
        def localization = Mock(Localization)
        localization.getData() >> [:]

        and: "设置TranslateI18nUtils模拟行为"
        GroovyMock(TranslateI18nUtils, global: true)
        TranslateI18nUtils.getAllTransValueByOrder(preKeyList, tenantId) >> localization

        when: "调用被测方法"
        def result = service.getRealNameByPreKey(tenantId, preKeyList, name, lang)

        then: "验证结果"
        result == name
    }

    /**
     * 测试getRealNameByPreKey方法 - 预设值为空字符串的情况
     */
    def "getRealNameByPreKey_预设值为空字符串_返回原始名称"() {
        given: "准备测试数据"
        def tenantId = 12345
        def preKeyList = ["preKey1", "preKey2"]
        def name = "原始名称"
        def lang = "zh-CN"

        // 创建Localization对象，但翻译值为空字符串
        def localization = Mock(Localization)
        localization.getData() >> [(0): name]
        localization.get(lang, name) >> ""

        and: "设置TranslateI18nUtils模拟行为"
        GroovyMock(TranslateI18nUtils, global: true)
        TranslateI18nUtils.getAllTransValueByOrder(preKeyList, tenantId) >> localization

        when: "调用被测方法"
        def result = service.getRealNameByPreKey(tenantId, preKeyList, name, lang)

        then: "验证结果"
        result == name
    }

    /**
     * 测试getRealNameByPreKey方法 - 预设值为null的情况
     */
    def "getRealNameByPreKey_预设值为null_返回原始名称"() {
        given: "准备测试数据"
        def tenantId = 12345
        def preKeyList = ["preKey1", "preKey2"]
        def name = "原始名称"
        def lang = "zh-CN"

        // 创建Localization对象，但翻译值为null
        def localization = Mock(Localization)
        localization.getData() >> [(0): name]
        localization.get(lang, name) >> null

        and: "设置TranslateI18nUtils模拟行为"
        GroovyMock(TranslateI18nUtils, global: true)
        TranslateI18nUtils.getAllTransValueByOrder(preKeyList, tenantId) >> localization

        when: "调用被测方法"
        def result = service.getRealNameByPreKey(tenantId, preKeyList, name, lang)

        then: "验证结果"
        result == name
    }

    /**
     * 新增测试：测试tenantId为null的情况下getTransValue方法的处理
     */
    def "getTransValue_tenantId为null_返回空Map"() {
        given: "准备tenantId为null的测试数据"
        Integer tenantId = null
        List<String> keyList = ["key1", "key2"]
        String lang = "zh-CN"
        boolean isNeedValidateLicense = true

        and: "设置模拟行为"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> false

        when: "调用getTransValue方法"
        def result = service.getTransValue(tenantId, keyList, lang, isNeedValidateLicense)

        then: "验证返回空Map"
        result != null
        result.isEmpty()
    }

    /**
     * 新增测试：测试isNeedValidateLicense为false时绕过许可检查
     */
    def "getTransValue_isNeedValidateLicense为false_绕过许可检查"() {
        given: "准备isNeedValidateLicense为false的测试数据"
        Integer tenantId = 12345
        List<String> keyList = ["key1", "key2"]
        String lang = "zh-CN"
        boolean isNeedValidateLicense = false

        and: "设置模拟行为"
        // 即使许可检查会返回false，但由于isNeedValidateLicense=false，应该绕过许可检查
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> false

        // 模拟getTransValue私有方法的实现
        I18nServiceImpl serviceSpy = Spy(service)

        // 模拟Localization对象
        def localization1 = new Localization()
        localization1.set(lang, "翻译值1")
        def localization2 = new Localization()
        localization2.set(lang, "翻译值2")

        def localizationMap = [
                "key1": localization1,
                "key2": localization2
        ]
        def i18nClient = Spy(I18nClient)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        I18nClient.getInstance().get(_, _) >> localizationMap

        // 设置私有方法的行为
        serviceSpy.getTransValue(tenantId, keyList, isNeedValidateLicense) >> localizationMap

        when: "调用getTransValue方法"
        def result = serviceSpy.getTransValue(tenantId, keyList, lang, isNeedValidateLicense)

        then: "验证不调用许可检查但仍返回正确结果"
        result != null
        !result.isEmpty()
        result.size() == 2
        result["key1"] == "翻译值1"
        result["key2"] == "翻译值2"
    }

    /**
     * 新增测试：测试在从REST请求且不从管理端请求时的行为
     */
    def "getTransValue_从REST请求_正确处理"() {
        given: "准备REST请求的测试数据"
        Integer tenantId = 12345
        List<String> keyList = ["key1", "key2"]
        String lang = "zh-CN"
        boolean isNeedValidateLicense = true

        and: "设置模拟行为"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true
        RequestContextManager.isFromRest() >> true
        RequestContextManager.isFromManager() >> false

        // 模拟Localization对象
        def localization1 = new Localization()
        localization1.set(lang, "翻译值1")
        def localization2 = new Localization()
        localization2.set(lang, "翻译值2")

        def i18nClient = Spy(I18nClient)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        I18nClient.getInstance().get(_, _) >> [
                "key1": localization1,
                "key2": localization2
        ]
        // 模拟getTransValue私有方法的实现
        I18nServiceImpl serviceSpy = Spy(service)
        // 重定向调用到真实实现
        serviceSpy.getTransValue(tenantId, keyList, isNeedValidateLicense) >> { args ->
            // 调用原始的getTransValue方法
            return [
                    "key1": localization1,
                    "key2": localization2
            ]
        }

        when: "调用getTransValue方法"
        def result = serviceSpy.getTransValue(tenantId, keyList, lang, isNeedValidateLicense)

        then: "验证返回正确结果"
        result != null
        !result.isEmpty()
        result.size() == 2
        result["key1"] == "翻译值1"
        result["key2"] == "翻译值2"
    }

    /**
     * 新增测试：测试语言标签格式不正确的情况
     */
    def "getTransValue_语言标签格式不正确_仍能正确处理"() {
        given: "准备不正确的语言标签测试数据"
        Integer tenantId = 12345
        List<String> keyList = ["key1", "key2"]
        String lang = invalidLang
        boolean isNeedValidateLicense = true

        and: "设置模拟行为"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true

        // 模拟Localization对象，故意设置不匹配的语言标签
        def localization1 = new Localization()
        localization1.set("zh-CN", "翻译值1")
        def localization2 = new Localization()
        localization2.set("zh-CN", "翻译值2")

        // 模拟getTransValue私有方法的实现
        I18nServiceImpl serviceSpy = Spy(service)
        serviceSpy.getTransValue(tenantId, keyList, isNeedValidateLicense) >> [
                "key1": localization1,
                "key2": localization2
        ]

        when: "调用getTransValue方法"
        def result = serviceSpy.getTransValue(tenantId, keyList, lang, isNeedValidateLicense)

        then: "验证返回空Map，因为没有匹配的语言标签"
        result != null
        result.isEmpty()

        where:
        invalidLang << ["zh_CN", "zhCN", "cn", ""]  // 各种不正确的语言标签格式
    }

    /**
     * 新增测试：测试处理大量数据时的行为
     */
    def "getTransValue_大量数据_正确处理所有数据"() {
        given: "准备大量测试数据"
        Integer tenantId = 12345
        List<String> keyList = []//100个键
        for (i in 0..<100) {
            keyList.add("key" + i)
        }
        String lang = "zh-CN"
        boolean isNeedValidateLicense = true

        and: "设置模拟行为"
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true

        // 创建包含所有键的本地化对象映射
        Map<String, Localization> localizationMap = [:]
        keyList.each { key ->
            def localization = new Localization()
            localization.set(lang, "翻译值-${key}")
            localizationMap.put(key, localization)
        }

        // 模拟getTransValue私有方法的实现
        I18nServiceImpl serviceSpy = Spy(service)
        def i18nClient = Spy(I18nClient)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        I18nClient.getInstance().get(_, _) >> localizationMap

        when: "调用getTransValue方法"
        def result = serviceSpy.getTransValue(tenantId, keyList, lang, isNeedValidateLicense)

        then: "验证返回包含所有键值对的结果"
        result != null
        !result.isEmpty()
        result.size() == 100
        keyList.every { key ->
            result[key] == "翻译值-${key}"
        }
    }
}

/**
 * I18nServiceImpl类风险评估
 *
 * 1. 代码中存在多个废弃方法
 *    - getCurrentI18nValue和getOnTimeTransValue已被标记为@Deprecated
 *    - 但仍有业务调用，可能导致使用了不推荐的实现方式
 *    - 建议：制定计划替换废弃的API调用
 *
 * 2. 异常处理和错误恢复
 *    - 方法中大量使用try-catch处理外部依赖调用异常
 *    - 异常时依赖于回退机制，如fallback到TranslateI18nUtils
 *    - 风险：如果回退机制也失效，可能导致服务降级
 *    - 建议：增加熔断机制和健康检查，及时发现外部依赖问题
 *
 * 3. 依赖多个外部服务
 *    - 依赖OnTimeTranslateValueResource、UIPaasLicenseService等多个服务
 *    - 风险：任一服务不可用可能导致翻译功能不完整
 *    - 建议：考虑增加缓存层和服务降级策略
 *
 * 4. 复杂的灰度开关逻辑
 *    - 如WebPageGraySwitch.isAllowForEi判断，依赖于灰度发布系统
 *    - 风险：灰度开关配置错误可能导致功能异常
 *    - 建议：完善灰度开关的测试用例和监控
 *
 * 5. 多语言处理复杂性
 *    - 涉及多种多语言获取和同步逻辑，如preKey、customKey、oldKey等
 *    - 风险：逻辑复杂，难以维护和测试
 *    - 建议：考虑重构简化多语言处理逻辑
 *
 * 6. 性能问题
 *    - 多处使用Stream API进行集合操作，可能存在性能瓶颈
 *    - 风险：大量数据时可能导致性能下降
 *    - 建议：关键路径增加性能监控和优化
 *
 * 7. 静态工具类依赖
 *    - 大量依赖TranslateI18nUtils等静态工具类
 *    - 风险：静态工具类修改可能影响多处功能
 *    - 建议：考虑使用依赖注入替代静态工具类
 *
 * 8. 条件判断复杂
 *    - 多层嵌套的if-else和条件逻辑
 *    - 风险：难以理解和维护，容易引入bug
 *    - 建议：重构简化条件逻辑，使用策略模式等设计模式
 */
