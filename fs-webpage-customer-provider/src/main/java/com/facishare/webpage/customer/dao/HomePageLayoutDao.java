package com.facishare.webpage.customer.dao;

import com.facishare.qixin.sysdb.filter.Filter;
import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/5
 */
public interface HomePageLayoutDao {
    /**
     * 根据id获取首页
     *
     * @param layoutId
     * @param filter
     * @return
     */
    HomePageLayoutEntity getHomePageLayoutById(String layoutId, Filter filter);

    List<HomePageLayoutEntity> findBySiteApiNameIncludeDisable(int tenantId, String appId, String siteApiName);

    List<HomePageLayoutEntity> findBySiteApiNameIncludeDisable(int tenantId, String appId, String siteApiName, String clientType);

    List<HomePageLayoutEntity> findBySiteApiName(int tenantId, String appId, String siteApiName, String clientType);

    HomePageLayoutEntity findBySiteApiNameAndPageApiName(int tenantId,
                                                         String appId,
                                                         String siteApiName,
                                                         String pageApiName);

    HomePageLayoutEntity findHomePageBySiteApiName(int tenantId, String appId, String siteApiName, String clientType);

    /**
     * 批量获取首页
     *
     * @param layoutIds
     * @param filter
     * @return
     */
    List<HomePageLayoutEntity> getBatchHomePageLayoutByIds(List<String> layoutIds, Filter filter);

    /**
     * 插入首页
     *
     * @param appId
     * @param appType
     * @param tenantId
     * @param employeeId
     * @param homePageLayoutTO
     * @param sourceId
     * @param change
     * @return
     */
    HomePageLayoutEntity insertHomePageLayout(String appId, int appType, int tenantId, int employeeId, HomePageLayoutTO homePageLayoutTO, String sourceId, boolean change);

    /**
     * findAndModifyHomePage
     *
     * @param tenantId
     * @param homePageLayoutEntity
     * @return
     */
    HomePageLayoutEntity findAndModifyHomePage(int tenantId, HomePageLayoutEntity homePageLayoutEntity);

    /**
     * 更新首页
     *
     * @param employeeId
     * @param homePageLayoutTO
     * @return
     */
    HomePageLayoutEntity updateHomePageLayout(int employeeId, HomePageLayoutTO homePageLayoutTO);

    void batchUpdate(User user, List<HomePageLayoutEntity> entityList);

    /**
     * 修改首页状态
     *
     * @param layoutId
     * @param employeeId
     * @param templeId
     * @param appType
     * @return
     */
    HomePageLayoutEntity makeHomePageFormal(String layoutId, int employeeId, String templeId, int appType);

    /**
     * 根据 name 获取首页
     *
     * @param tenantId
     * @param employeeId
     * @param appId
     * @param name
     * @param layoutType
     * @return
     */
    HomePageLayoutEntity getHomePageLayoutByName(int tenantId, int employeeId, String appId, String name, int layoutType);

    /**
     * 获取个人首页列表
     *
     * @param tenantId
     * @param appId
     * @param scopes
     * @param enablePersonPageConfig
     * @return
     */
    List<HomePageLayoutEntity> getEmployeeHomePageLayoutList(int tenantId, String appId, List<String> scopes, boolean enablePersonPageConfig);

    /**
     * 获取个人首页列表
     *
     * @param tenantId
     * @param scopes
     * @param filter
     * @return
     */
    List<HomePageLayoutEntity> getEmployeeHomePageLayoutListByFilter(int tenantId, List<String> scopes, Filter filter);

    /**
     * 获取首页列表（管理后台）
     *
     * @param tenantId
     * @param appId
     * @return
     */
    List<HomePageLayoutEntity> getHomePageLayoutList(int tenantId, String appId);

    /**
     * 获取首页列表（管理后台）
     *
     * @param tenantId
     * @param filter
     * @return
     */
    List<HomePageLayoutEntity> getHomePageLayoutListByFilter(int tenantId, Filter filter);

    List<HomePageLayoutEntity> getHomePageLayoutListBytenantId(int tenantId, Filter filter);

    /**
     * 获取首页个数
     *
     * @param appId
     * @param tenantId
     * @param layoutType
     * @param employeeId
     * @return
     */
    long getPageCount(String appId, int tenantId, int layoutType, int employeeId);

    /**
     * 更新首页状态
     *
     * @param layoutId
     * @param status
     * @param employeeId
     * @return
     */
    HomePageLayoutEntity updateHomePageStatus(String layoutId, int status, int employeeId);

    /**
     * 根据sourceId获取首页
     *
     * @param tenantId
     * @param sourceId
     * @param sourceType
     * @return
     */
    HomePageLayoutEntity getHomePageLayoutBySourceId(int tenantId, String sourceId, String sourceType);

    /**
     * 获取创建人
     *
     * @param layoutId
     * @return
     */
    HomePageLayoutEntity getCreatorId(String layoutId);

    /**
     * 获取系统首页
     *
     * @param tenantId
     * @param appId
     * @param scopes
     * @return
     */
    HomePageLayoutEntity getSystemHomePageLayoutEntity(int tenantId, String appId, List<String> scopes);

    /**
     * 获取首页的列表（to console）
     *
     * @param tenantId
     * @param appId
     * @param layoutType
     * @return
     */
    List<HomePageLayoutEntity> getHomePageForConsole(int tenantId, String appId, int layoutType);

    List<HomePageLayoutEntity> getHomePageForConsole(int tenantId, String appId, List<Integer> layoutTypes);


    /**
     * 批量获取自定义页面
     *
     * @param tenantId
     * @param apiNames
     * @return
     */
    List<HomePageLayoutEntity> getCustomerLayoutByApiNames(int tenantId, List<String> apiNames);

    List<HomePageLayoutEntity> findByApiNamesIncludeDisable(int tenantId, List<String> apiNames);

    /**
     * 根据 apiName 获取首页
     *
     * @param tenantId
     * @param apiName
     * @param filter
     * @return
     */
    HomePageLayoutEntity getHomePageByTenantIdAndApiName(int tenantId, String apiName, Filter filter);

    /**
     * findAndModifyHomePage
     *
     * @param tenantId
     * @param apiName
     * @param homePageLayoutEntity
     * @return
     */
    HomePageLayoutEntity findAndModifyHomePage(int tenantId, String apiName, HomePageLayoutEntity homePageLayoutEntity);

    /**
     * 更改首页状态
     *
     * @param tenantId
     * @param apiName
     * @param status
     * @param employeeId
     * @return
     */
    HomePageLayoutEntity updateHomePageStatus(int tenantId, String apiName, int status, int employeeId);

    /**
     * 根据 appType 删除首页
     *
     * @param tenantId
     * @param appType
     */
    void deleteCustomerPage(int tenantId, int appType);

    /**
     * 根据 appType+appId 删除首页
     *
     * @param tenantId
     * @param appType
     * @param appId
     */
    void deleteHomePageLayouts(int tenantId, int appType, String appId);

    void deleteByIds(User user, List<String> ids);

    /**
     * 获取启用的页面数据
     *
     * @param tenantId
     * @param appType
     * @param appId
     * @param applyType
     * @return
     */
    List<HomePageLayoutEntity> getEnableHomePageLayoutList(int tenantId, int appType, String appId, int applyType);

    void updateHomepage2CustomPage(Integer enterpriseId, List<String> apiNames, Integer employeeId, String appId);

    void updateCustomPage2Homepage(Integer enterpriseId, Integer employeeId, String appId);

    void deleteSCRMCustomerPage(Integer enterpriseId);

    void batchSave(User user, List<HomePageLayoutEntity> entityList);
}
