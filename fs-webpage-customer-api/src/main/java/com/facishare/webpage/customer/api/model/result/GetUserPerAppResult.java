package com.facishare.webpage.customer.api.model.result;

import com.facishare.webpage.customer.api.model.PaaSAppVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetUserPerAppResult {
    /**
     * 该用户的应用信息
     */
    private List <PaaSAppVO> preAppInfoByUser;
}
