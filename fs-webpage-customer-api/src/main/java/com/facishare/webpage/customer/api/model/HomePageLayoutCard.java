package com.facishare.webpage.customer.api.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON>yu on 2019/9/6
 */
@Data
public class HomePageLayoutCard implements Serializable {

    private String appId;

    @SerializedName("CardID")
    private String cardId;  //卡片Id
    @SerializedName("apiName")
    private String apiName;
    @SerializedName("HomePageLayoutFilters")
    private List<HomePageLayoutFilter> homePageLayoutFilters;
    @SerializedName("HomePageLayoutTools")
    private List<HomePageLayoutTool> homePageLayoutTools;
    @SerializedName("Type")
    private int type;   //卡片类型
    @SerializedName("Title")
    private String title;   //卡片标题
    @SerializedName("Row")
    private int row;    //行
    @SerializedName("Column")
    private int column; //列
    @SerializedName("Width")
    private int width;  //宽度
    @SerializedName("Height")
    private int height; //高度
    @SerializedName("MobileHeight")
    private int mobileHeight;
    @SerializedName("URL")
    private String url; //url路径
    @SerializedName("Order")
    private int order;  //卡片顺序

}
