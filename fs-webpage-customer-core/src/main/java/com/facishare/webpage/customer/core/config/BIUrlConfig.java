package com.facishare.webpage.customer.core.config;

import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeListener;
import com.github.autoconf.api.IConfig;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Created by zhangyu on 2019/12/26
 */
public class BIUrlConfig {

    private static Logger logger = LoggerFactory.getLogger(BIUrlConfig.class);

    private Map<String, String> urlMap = Maps.newHashMap();

    private static String biUrlConfig = "fs-webpage-bi-url";

    public void init() {
        IChangeListener listener = config -> loadConfig();
        ConfigFactory.getConfig(biUrlConfig, listener, true);
    }

    private void loadConfig() {

        IConfig config = ConfigFactory.getConfig(biUrlConfig);
        Map<String, String> map = config.getAll();
        urlMap = map;
        logger.info("init BIUrlConfig config urlMap:{};", urlMap);
    }

    public String getBIUrl(String prefix) {
        return urlMap.get(prefix);
    }

    public static void main(String[] args) {
        System.setProperty("process.profile", "fstest");
        BIUrlConfig biUrlConfig = new BIUrlConfig();
        biUrlConfig.init();
        String url = biUrlConfig.getBIUrl("HomePage_BI_BASE_URL");
        System.out.println(url);
    }

}
