package com.facishare.webpage.customer.core.config;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by she<PERSON> on 19/12/11.
 */
@Slf4j
public class MenusConfig {

    private List<Menu> menus;

    private Map<String, Menu> menuCollections;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public void init() {
        ConfigFactory.getInstance().getConfig("fs-webpage-customer-menus", iConfig -> {
            menus = JSONObject.parseArray(iConfig.getString(), Menu.class);
            menuCollections = menus.stream().collect(Collectors.toMap(menu -> menu.getId(), Function.identity(), (t1, t2) -> t2));
        });
    }

    public List<Menu> getMenus() {
        return menus;
    }

    public List<Menu> getMenusByIds(List<String> menuIds) {
        if (!WebPageGraySwitch.isAllowForEi(WebPageGraySwitch.GET_MENUS_BY_IDS_GRAY_EI, 1)) {
            List<Menu> menuList = menuIds.stream().
                    map(id -> menuCollections.get(id)).
                    filter(menu -> !Objects.isNull(menu)).
                    collect(Collectors.toList());
            return menuList.stream().map(x -> {
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    return objectMapper.readValue(objectMapper.writeValueAsString(x), Menu.class);
                } catch (Exception e) {
                    log.error("clone error by menu:{}", x, e);
                    return null;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return menuIds.stream()
                .map(menuCollections::get)
                .filter(Objects::nonNull)
                .map(menu -> {
                    try {
                        return OBJECT_MAPPER.convertValue(menu, Menu.class);
                    } catch (Exception e) {
                        log.error("clone error by menu:{}", menu, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static void main(String[] args) {
        System.setProperty("process.profile", "fstest");
        MenusConfig menusConfig = new MenusConfig();
        menusConfig.init();
        for (int i = 0; i < 3; i++) {
            List<Menu> menus = menusConfig.getMenusByIds(Lists.newArrayList("CrmRemind"));
            Menu menu = menus.get(0);
            List<String> deviceTypes = menu.getDeviceTypes();
            System.out.println(deviceTypes);
            deviceTypes.add("aaa");
            menu.setDeviceTypes(deviceTypes);
        }
    }


}
