package com.facishare.webpage.customer.controller.model.arg.cms;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 保存文件信息参数
 */
@Data
public class SaveFileInfoArg implements Serializable {

    private static final long serialVersionUID = 1L;

    List<SaveFileInfo> saveFileInfoList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SaveFileInfo {

        /**
         * 所属工作区ApiName
         */
        private String workSpaceApiName;

        /**
         * 父文件夹ApiName，为空表示根目录
         */
        private String parentApiName;

        /**
         * 文件ApiName，为空则自动生成
         */
        private String apiName;

        /**
         * 文件名称
         */
        private String name;

        /**
         * 文件扩展名
         */
        private String extra;
        /**
         * 文件类型：file-文件，folder-文件夹
         */
        private String type;

        /**
         * 文件路径
         */
        private String path;

        /**
         * MIME类型
         */
        private String mimeType;

        /**
         * 文件类型 文件夹/文件
         */
        private String nodeType;
    }

}
