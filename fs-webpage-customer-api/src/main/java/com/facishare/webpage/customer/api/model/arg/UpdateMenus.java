package com.facishare.webpage.customer.api.model.arg;

import com.facishare.webpage.customer.api.model.core.Menu;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 19/12/21.
 */
public interface UpdateMenus {

    @Data
    class Arg implements Serializable {
        private int tenantId;

        private String collectionId;

        private List<Menu> menus;
    }

    @Data
    class Result implements Serializable {

    }


}
