package com.facishare.webpage.customer.controller.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.annotation.FSClientInfo;
import com.facishare.cep.plugin.annotation.FSOuterUserInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.qixin.objgroup.common.service.PaasOrgGroupService;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.constant.CardType;
import com.facishare.webpage.customer.api.constant.SourceType;
import com.facishare.webpage.customer.api.constant.Status;
import com.facishare.webpage.customer.api.exception.ValidateException;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.HomePageLayoutCard;
import com.facishare.webpage.customer.api.model.HomePageLayoutFilter;
import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.facishare.webpage.customer.api.model.LayoutType;
import com.facishare.webpage.customer.api.model.arg.CheckUserPermissionApiArg;
import com.facishare.webpage.customer.api.model.result.BaseApiResult;
import com.facishare.webpage.customer.api.service.TenantPageTempleService;
import com.facishare.webpage.customer.component.CovertCustomerManage;
import com.facishare.webpage.customer.config.ComponentConfig;
import com.facishare.webpage.customer.constant.DataVersion;
import com.facishare.webpage.customer.constant.HomePageConstant;
import com.facishare.webpage.customer.constant.PageTemplateType;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.controller.UserHomePageAction;
import com.facishare.webpage.customer.controller.model.arg.homepage.*;
import com.facishare.webpage.customer.controller.model.result.homepage.*;
import com.facishare.webpage.customer.core.util.BIUrlUtil;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity;
import com.facishare.webpage.customer.event.WebPageEventService;
import com.facishare.webpage.customer.model.UserHomePageLayoutAO;
import com.facishare.webpage.customer.service.HomePageBaseService;
import com.facishare.webpage.customer.service.UserHomePageBaseService;
import com.facishare.webpage.customer.system.PageSystemService;
import com.facishare.webpage.customer.util.BIHomePageGraySwitch;
import com.facishare.webpage.customer.util.GeneralUtil;
import com.facishare.webpage.customer.util.HomePageLayoutUtil;
import com.facishare.webpage.customer.util.component.NewDataCovertOldData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.webpage.customer.api.constant.ErrorMessageI18NKey.WEB_APP_VIEW_IN_EXISTENCE_OR_DELETE;

/**
 * Created by zhangyu on 2019/9/10
 */
@Controller
@Slf4j
@RequestMapping("/UserHomePage")
public class UserHomePageActionImpl implements UserHomePageAction {
    private static final Logger logger = LoggerFactory.getLogger(UserHomePageActionImpl.class);

    @Autowired
    private HomePageBaseService homePageBaseService;
    @Autowired
    private TenantPageTempleService tenantPageTempleService;
    @Resource
    private WebPageEventService webPageEventService;
    @Resource
    private UserHomePageBaseService userHomePageBaseService;
    @Resource
    private CovertCustomerManage covertCustomerManage;
    @Resource
    private PageSystemService pageSystemService;
    @Autowired
    private BIUrlUtil biUrlUtil;
    @Autowired
    private PaasOrgGroupService paasOrgGroupService;
    @Resource
    private ComponentConfig componentConfig;

    public void setHomePageBaseService(HomePageBaseService homePageBaseService) {
        this.homePageBaseService = homePageBaseService;
    }

    public void setTenantPageTempleService(TenantPageTempleService tenantPageTempleService) {
        this.tenantPageTempleService = tenantPageTempleService;
    }

    @Override
    @RequestMapping(value = "/GetUserHomePageLayoutByLayoutId", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetUserHomePageLayoutResult getUserHomePageLayoutByLayoutId(@FSUserInfo UserInfo userInfo,
                                                                       @FSOuterUserInfo OuterUserInfo outerUserInfo,
                                                                       @FSClientInfo ClientInfo clientInfo,
                                                                       @RequestBody GetUserHomePageLayoutArg arg) {
        if (userInfo == null || outerUserInfo == null) {
            throw new WebPageException(InterErrorCode.IDENTITY_VERIFICATION_FAILED);
        }
        if (StringUtils.isBlank(arg.getLayoutId())) {
            throw ValidateException.fromI18N(WEB_APP_VIEW_IN_EXISTENCE_OR_DELETE);
        }
        HomePageLayoutTO homePageLayoutTO = homePageBaseService.getUserHomePageLayoutByApiName(
                userInfo,
                outerUserInfo,
                arg.getLayoutId(),
                arg.getLayoutApiName(),
                BizType.APP.getType(),
                clientInfo.getLocale());
        if (homePageLayoutTO == null) {
            return new GetUserHomePageLayoutResult();
        }
        CheckUserPermissionApiArg permissionApiArg = new CheckUserPermissionApiArg();
        {
            permissionApiArg.setTempleId(homePageLayoutTO.getTempleId());
            permissionApiArg.setOuterTenantId(outerUserInfo.getOutTenantId());
            permissionApiArg.setOuterUserId(outerUserInfo.getOutUserId());
            permissionApiArg.setTenantId(userInfo.getEnterpriseId());
        }
        BaseApiResult baseApiResult = tenantPageTempleService.checkUserPermission(permissionApiArg);
        boolean checkUserPermission = (Boolean) baseApiResult.getContent();
        if (!checkUserPermission) {
            throw new WebPageException(InterErrorCode.PAGE_DISABLE);
        }
        GetUserHomePageLayoutResult result = new GetUserHomePageLayoutResult();
        result.setHomePageLayout(homePageLayoutTO);
        logger.debug("getUserHomePageLayoutByLayoutId result {}", result);

        userInfo.setEmployeeId(outerUserInfo.getUpstreamOwnerId() == null ? 0 : outerUserInfo.getUpstreamOwnerId());
        //增加埋点
        webPageEventService.sendGetWebPageEvent(userInfo, clientInfo, BizType.APP.getType(), homePageLayoutTO.getAppId());

        return result;
    }

    @Override
    @RequestMapping(value = "/GetVendorHomePageLayoutById", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetVendorUserHomePageResult getVendorUserHomePageById(@FSUserInfo UserInfo userInfo,
                                                                 @FSClientInfo ClientInfo clientInfo,
                                                                 @RequestBody GetVendorUserHomePageArg arg) {
        arg.valid();
        HomePageLayoutTO homePageLayoutTO = homePageBaseService.getUserHomePageLayoutByApiName(
                userInfo,
                null,
                arg.getLayoutId(),
                arg.getLayoutApiName(),
                BizType.APP.getType(),
                clientInfo.getLocale());
        GetVendorUserHomePageResult result = new GetVendorUserHomePageResult();
        result.setHomePageLayout(homePageLayoutTO);
        //增加埋点
        webPageEventService.sendGetWebPageEvent(userInfo, clientInfo, BizType.APP.getType(), homePageLayoutTO.getAppId());
        return result;
    }

    @Override
    @RequestMapping(value = "/getInnerUserHomePageLayoutById", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetInnerUserHomePageLayoutResult getInnerUserHomePageLayoutById(@FSUserInfo UserInfo userInfo,
                                                                           @FSClientInfo ClientInfo clientInfo,
                                                                           @RequestBody GetInnerUserHomePageLayoutArg arg) {
        arg.valid();
        HomePageLayoutTO homePageLayoutTO = homePageBaseService.getUserHomePageLayoutByApiName(userInfo,
                null,
                arg.getLayoutId(),
                arg.getLayoutApiName(),
                WebPageConstants.DEFAULT_APP_TYPE,
                clientInfo.getLocale());
        GetInnerUserHomePageLayoutResult result = new GetInnerUserHomePageLayoutResult();
        result.setHomePageLayout(homePageLayoutTO);
        //增加埋点
        webPageEventService.sendGetWebPageEvent(userInfo, clientInfo, BizType.getBusinessType(homePageLayoutTO.getAppId()).getType(), homePageLayoutTO.getAppId());
        return result;
    }

    @Override
    @RequestMapping(value = "/getHomePageByApiName", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetHomePageByApiNameResult getHomePageByApiName(@FSUserInfo UserInfo userInfo,
                                                           @FSClientInfo ClientInfo clientInfo,
                                                           @RequestBody GetHomePageByApiNameArg arg) {
        arg.valid();
        if (!BIHomePageGraySwitch.isAllowByBusiness(BIHomePageGraySwitch.BI_HOMEPAGE_ENTERPRISEACCOUNT, userInfo.getEnterpriseAccount()) || userInfo.getEmployeeId() == null) {
            return new GetHomePageByApiNameResult();
        }

        List<HomePageLayoutTO> employeeHomePageLayoutList = homePageBaseService.getEmployeeHomePageLayoutList(
                userInfo.getEnterpriseId(),
                userInfo.getEmployeeId(),
                BizType.BI.getType(),
                arg.getApiName(),
                clientInfo.getLocale(),
                true);
        List<GetHomePageByApiNameResult.HomePageData> homePageDataList = buildHomePageData(employeeHomePageLayoutList);
        GetHomePageByApiNameResult result = new GetHomePageByApiNameResult();
        result.setHomePageDataList(homePageDataList);
        return result;
    }

    @Override
    @RequestMapping(value = "/getUserCustomerLayout", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetUserCustomerLayoutResult getUserCustomerLayout(@FSUserInfo UserInfo userInfo,
                                                             @FSOuterUserInfo OuterUserInfo outerUserInfo,
                                                             @FSClientInfo ClientInfo clientInfo,
                                                             @RequestBody GetUserCustomerLayoutArg arg) {
        arg.valid();
        HomePageLayoutTO homePageLayout;
        if (PageTemplateType.USER_PAGE_TEMPLATE == arg.getPageTemplateType()) {
            homePageLayout = homePageBaseService.getUserHomePageLayoutByApiName(
                    userInfo,
                    outerUserInfo,
                    arg.getLayoutId(),
                    arg.getLayoutApiName(),
                    arg.getAppType(),
                    clientInfo.getLocale());
        } else if (PageTemplateType.SYSTEM_PAGE_TEMPLATE == arg.getPageTemplateType()) {
            HomePageLayoutEntity homePageLayoutEntity = pageSystemService.getPageEntityByLayoutId(userInfo.getEnterpriseId(), arg.getLayoutId());
            homePageLayout = HomePageLayoutUtil.buildHomePageLayoutItem(homePageLayoutEntity);
            JSONObject customerLayout = homePageLayout.getCustomerLayout();
            JSONObject jsonObject = covertCustomerManage.convertTenantLayout(userInfo, arg.getLayoutId(), homePageLayoutEntity.getAppId(), clientInfo.getLocale(), customerLayout, homePageLayout.getHomePageLayouts());
            homePageLayout.setCustomerLayout(jsonObject);
            List<JSONObject> customerLayoutList = Lists.newArrayList();
            for (JSONObject item : homePageLayout.getCustomerLayoutList()) {
                JSONObject convertTenantLayout = covertCustomerManage.convertTenantLayout(userInfo, arg.getLayoutId(), homePageLayoutEntity.getAppId(), clientInfo.getLocale(), item, homePageLayout.getHomePageLayouts());
                customerLayoutList.add(convertTenantLayout);
            }
            homePageLayout.setCustomerLayoutList(customerLayoutList);
            if (WebPageConstants.APP_CRM.equals(homePageLayout.getAppId())) {
                Map<String, Integer> widgetTypeMap = componentConfig.getWidgetTypeMap();
                List<String> crmFilterComponents = componentConfig.getCrmFilterComponentsByApp();
                List<HomePageLayoutCard> homePageLayoutCardList = NewDataCovertOldData.covertNewData(customerLayout, widgetTypeMap, crmFilterComponents);
                homePageLayout.setHomePageLayouts(homePageLayoutCardList);
                buildHomePageLayoutsUrl(userInfo, null, homePageLayout, clientInfo.getLocale());
            }
        } else {
            homePageLayout = queryTenantHomePage(userInfo, outerUserInfo, clientInfo, arg);
        }
        GetUserCustomerLayoutResult result = new GetUserCustomerLayoutResult();
        if (homePageLayout == null) {
            return result;
        }
        result.setHomePageLayout(homePageLayout);
        result.setMd5Version(getMd5Version(homePageLayout, userInfo.getEnterpriseId()));
        //增加埋点
        webPageEventService.sendGetWebPageEvent(userInfo, clientInfo, arg.getAppType(), homePageLayout.getAppId());
        return result;
    }

    private void buildHomePageLayoutsUrl(UserInfo userInfo, OuterUserInfo outerUserInfo, HomePageLayoutTO homePageLayout, Locale locale) {

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(homePageLayout.getHomePageLayouts())) {
            return;
        }

        homePageLayout.getHomePageLayouts().stream().forEach(homePageLayoutCard -> {
            String url = biUrlUtil.buildUrl(homePageLayoutCard.getCardId(), homePageLayoutCard.getType());
            homePageLayoutCard.setUrl(url);
            if (CardType.isFilter(homePageLayoutCard.getType())) {
                List<HomePageLayoutFilter> homePageLayoutFilters = buildHomePageFilter(userInfo, outerUserInfo, homePageLayout.getAppId(), homePageLayoutCard.getHomePageLayoutFilters(), locale);
                homePageLayoutCard.setHomePageLayoutFilters(homePageLayoutFilters);
            }
        });
    }

    private List<HomePageLayoutFilter> buildHomePageFilter(UserInfo userInfo, OuterUserInfo outerUserInfo, String appId, List<HomePageLayoutFilter> homePageLayoutFilters, Locale locale) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(homePageLayoutFilters)) {
            return Lists.newArrayList();
        }
        return homePageLayoutFilters.stream().map(homePageLayoutFilter -> {
            if (GeneralUtil.checkObj(homePageLayoutFilter.getFilterKey())) {
                String filterMainID = paasOrgGroupService.getTemplateIdByUpOrDown(userInfo, outerUserInfo, appId, homePageLayoutFilter.getFilterKey(), locale);
                homePageLayoutFilter.setFilterMainID(filterMainID);
            } else {
                homePageLayoutFilter.setFilterMainID(homePageLayoutFilter.getFilterKey());
            }

            return homePageLayoutFilter;
        }).collect(Collectors.toList());
    }

    private HomePageLayoutTO queryTenantHomePage(UserInfo userInfo, OuterUserInfo outerUserInfo, ClientInfo clientInfo, GetUserCustomerLayoutArg arg) {
        HomePageLayoutTO homePageLayout = homePageBaseService.getUserHomePageLayoutBySourceId(userInfo, null, arg.getLayoutId(), clientInfo.getLocale());
        if (homePageLayout != null) {
            return homePageLayout;
        }
        return homePageBaseService.getUserHomePageLayoutByApiName(
                userInfo,
                outerUserInfo,
                arg.getLayoutId(),
                arg.getLayoutApiName(),
                arg.getAppType(),
                clientInfo.getLocale());


    }

    private String getMd5Version(HomePageLayoutTO homePageLayout, Integer enterpriseId) {
        try {
            if (homePageLayout == null || Objects.isNull(enterpriseId)) {
                return null;
            }
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(homePageLayout.getStatus()).append(homePageLayout.getUpdateTime());

            // 优先使用customerLayoutList计算MD5
            List<JSONObject> customerLayoutList = homePageLayout.getCustomerLayoutList();
            if (CollectionUtils.isNotEmpty(customerLayoutList) && WebPageGraySwitch.isMD5VersionIncludeLayoutList(enterpriseId)) {
                // 将所有布局数据添加到MD5计算中
                for (JSONObject layoutItem : customerLayoutList) {
                    if (layoutItem != null) {
                        stringBuilder.append(layoutItem.toJSONString());
                    }
                }
            } else {
                // 如果customerLayoutList为空，则使用customerLayout（保持兼容性）
                JSONObject customerLayout = homePageLayout.getCustomerLayout();
                if (customerLayout != null) {
                    stringBuilder.append(customerLayout.toJSONString());
                }
            }
            return DigestUtils.md5Hex(stringBuilder.toString());
        } catch (Exception e) {
            logger.error("getMd5Version error by homePageLayout:{}", homePageLayout, e);
            // 异常情况下生成一个UUID作为返回值
            return UUID.randomUUID().toString();
        }
    }


    @Override
    @RequestMapping(value = "/getUserHomePageLayoutList", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetUserHomePageLayoutListResult getUserHomePageLayoutList(@FSUserInfo UserInfo userInfo,
                                                                     @FSClientInfo ClientInfo clientInfo,
                                                                     @RequestBody GetUserHomePageLayoutListArg arg) {
        arg.valid();
        Boolean enablePersonPageConfig = homePageBaseService.queryEnablePersonPageConfig(userInfo.getEnterpriseId());
        List<HomePageLayoutTO> homePageLayoutList =
                homePageBaseService.getEmployeeHomePageLayoutList(userInfo.getEnterpriseId(), userInfo.getEmployeeId(),
                        arg.getAppType(), arg.getAppId(), clientInfo.getLocale(), enablePersonPageConfig);
        GetUserHomePageLayoutListResult result = new GetUserHomePageLayoutListResult();
        result.setHomePageLayoutTOList(homePageLayoutList);
        return result;
    }

    @Override
    @RequestMapping(value = "/setPaaSCurrentHomePage", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public SetPaaSCurrentHomePageResult setPaaSCurrentHomePage(@FSUserInfo UserInfo userInfo,
                                                               @RequestBody SetPaaSCurrentHomePageArg arg) {
        arg.valid();
        boolean updateEmployeeCurrentHomePageLayout = userHomePageBaseService.updateEmployeeCurrentHomePageLayout(
                arg.getApiName(),
                userInfo.getEnterpriseId(),
                userInfo.getEmployeeId(),
                arg.getAppId(),
                HomePageConstant.web);
        SetPaaSCurrentHomePageResult result = new SetPaaSCurrentHomePageResult();
        result.setSuccess(updateEmployeeCurrentHomePageLayout);
        return result;
    }

    @Override
    @RequestMapping(value = "/saveUserHomePage", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public SaveUserHomePageResult saveUserHomePage(@FSUserInfo UserInfo userInfo,
                                                   @FSClientInfo ClientInfo clientInfo,
                                                   @RequestBody SaveUserHomePageArg arg) {
        Integer enterpriseId = userInfo.getEnterpriseId();
        UserHomePageLayoutAO userHomePageLayoutAO;
        if (PageTemplateType.USER_PAGE_TEMPLATE == arg.getPageTemplateType()) {
            userHomePageLayoutAO = covertCreateUserHomePageLayoutAO(arg);
        } else {
            userHomePageLayoutAO = covertEditUserHomePage(enterpriseId, arg);
        }
        String layoutId = homePageBaseService.saveUserHomePageLayout(userInfo, userHomePageLayoutAO);

        return SaveUserHomePageResult.builder().
                layoutId(layoutId).
                success(true).
                build();
    }

    private UserHomePageLayoutAO covertEditUserHomePage(int enterpriseId, SaveUserHomePageArg arg) {
        UserHomePageLayoutAO userHomePageLayoutAO = homePageBaseService.queryUserHomePageLayoutBySourceId(enterpriseId,
                arg.getLayoutId());
        if (userHomePageLayoutAO == null) {
            return covertEditTenantUserHomePageLayoutAO(arg);
        }
        userHomePageLayoutAO.setPageLayoutType(arg.getPageLayoutType());
        userHomePageLayoutAO.setCustomerLayout(arg.getCustomerLayout());
        return userHomePageLayoutAO;
    }

    /**
     * 个人级数据的构建
     *
     * @param arg
     * @return
     */
    private UserHomePageLayoutAO covertCreateUserHomePageLayoutAO(SaveUserHomePageArg arg) {
        return UserHomePageLayoutAO.builder().
                layoutId(arg.getLayoutId()).
                appType(BizType.getAppType(arg.getAppId())).
                appId(arg.getAppId()).
                layoutType(LayoutType.PERSONAL).
                status(Status.FORMAL).
                templeId(arg.getPageTemplateId()).
                customerLayout(arg.getCustomerLayout()).
                customerLayoutList(arg.getCustomerLayoutList()).
                pageMultiType(arg.getPageMultiType()).
                dataVersion(DataVersion.dataVersion_200).
                pageLayoutType(arg.getPageLayoutType()).
                sourceType(SourceType.USER_CUSTOMER).
                build();
    }

    /**
     * 编辑租户级的数据
     *
     * @param arg
     * @return
     */
    private UserHomePageLayoutAO covertEditTenantUserHomePageLayoutAO(SaveUserHomePageArg arg) {
        return UserHomePageLayoutAO.builder().
                appType(BizType.getAppType(arg.getAppId())).
                appId(arg.getAppId()).
                layoutType(LayoutType.PERSONAL).
                status(Status.FORMAL).
                templeId(arg.getPageTemplateId()).
                customerLayout(arg.getCustomerLayout()).
                dataVersion(DataVersion.dataVersion_200).
                pageLayoutType(arg.getPageLayoutType()).
                sourceType(SourceType.EDIT_TENANT).
                sourceId(arg.getLayoutId()).
                build();
    }

    private List<GetHomePageByApiNameResult.HomePageData> buildHomePageData(List<HomePageLayoutTO> homePageLayoutTOList) {
        List<GetHomePageByApiNameResult.HomePageData> homePageDataList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(homePageLayoutTOList)) {
            return homePageDataList;
        }
        homePageLayoutTOList.stream().forEach(x -> {
            GetHomePageByApiNameResult.HomePageData homePageData = new GetHomePageByApiNameResult.HomePageData();
            homePageData.setLayoutId(x.getLayoutId());
            homePageData.setCurrentPage(x.isCurrentLayout());
            homePageData.setName(x.getName());
            homePageDataList.add(homePageData);
        });
        return homePageDataList;
    }

}
