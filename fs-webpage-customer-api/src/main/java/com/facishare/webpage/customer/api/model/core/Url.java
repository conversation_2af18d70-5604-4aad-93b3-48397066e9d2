package com.facishare.webpage.customer.api.model.core;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * Created by she<PERSON> on 19/12/10.
 */
@Data
public class Url implements Serializable {

    @JSONField(ordinal = 1)
    private boolean isUseServerUrl = true;

    @JSONField(ordinal = 2)
    private String iOSUrl;

    @JSONField(ordinal = 3)
    private String androidUrl;

    @JSONField(ordinal = 4)
    private String webUrl;

    @JSONField(ordinal = 5)
    private String parameterUrl;

    @JSONField(ordinal = 6)
    private String highlightUrl;

    @JSONField(ordinal = 7)
    private String h5Url;

    @JSONField(ordinal = 8)
    private List<URLFilter> customizedUrlList;

    @JSONField(ordinal = 9)
    private String webGoJumpUrl;

    /**
     * 替换url内的参数
     *
     * @param url
     * @param upEa
     * @param upEi
     * @param appId
     * @return
     */
    public static String replaceParameterForUrl(String url, String upEa, int upEi, String appId) {
        if (StringUtils.isBlank(url)) {
            return url;
        }
        if (StringUtils.isBlank(appId)) {
            url = url.replace("${fsAppId}", "");
        } else {
            url = url.replace("${fsAppId}", appId);
        }
        if (StringUtils.isBlank(upEa)) {
            url = url.replace("${upEa}", "");
        } else {
            url = url.replace("${upEa}", upEa);
        }
        if (upEi > 0) {
            url = url.replace("${upEi}", String.valueOf(upEi));
        } else {
            url = url.replace("${upEi}", "");
        }

        return url;
    }
}
