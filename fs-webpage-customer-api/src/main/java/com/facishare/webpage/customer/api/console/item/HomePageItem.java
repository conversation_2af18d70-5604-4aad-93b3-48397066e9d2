package com.facishare.webpage.customer.api.console.item;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class HomePageItem implements Serializable {
    /**
     * 首页ID
     */
    private String layoutId;
    /**
     * 企业ID
     */
    private int tenantId;
    /**
     * 应用Id
     */
    private String appId;
    /**
     * 首页类型 1、CRM；2、应用服务
     */
    private int appType;
    /**
     * 首页应用Id
     */
    private String appTemplateId;
    /**
     * 首页类型 1、个人级；2、企业级
     */
    private int layoutType;
    /**
     * 首页适用范围
     */
    private List<String> scopes;
    /**
     * 卡片集合
     */
    private List<String> homePageCardEntityList;
    /**
     * 首页来源
     */
    private String sourceType;
    /**
     * 首页来源Id
     */
    private String sourceId;
    /**
     * 首页状态
     */
    private int status;
    /**
     * 首页名称
     */
    private String name;
    /**
     * 首页描述
     */
    private String description;
    /**
     * 预置模板是否被修改过
     */
    private boolean isChange;
    /**
     * 创建人ID
     */
    private int creatorId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 最后修改人ID
     */
    private int updaterId;
    /**
     * 最后修改时间
     */
    private Date updateTime;

}
