package com.facishare.webpage.customer.api.utils;

import com.facishare.webpage.customer.api.constant.Constant;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/11/30 4:04 下午
 */
@Slf4j
public class BizLogUtils {

    private static final String APP_NAME = ConfigHelper.getProcessInfo().getName();
    private static final String PROFILE = ConfigHelper.getProcessInfo().getProfile();
    private static final String IP = ConfigHelper.getProcessInfo().getIp();

    public static String sendBizLog(AuditLogDTO.AuditLogDTOBuilder builder) {
        TraceContext traceContext = TraceContext.get();
        AuditLogDTO dto = builder.traceId(traceContext.getTraceId()).appName(APP_NAME).profile(PROFILE).serverIp(IP).build();
        try {
            BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
        } catch (Exception e) {
            log.error("write remote log to BizLogClient error, auditLog:{}", dto, e);
        }
        return null;
    }



}
