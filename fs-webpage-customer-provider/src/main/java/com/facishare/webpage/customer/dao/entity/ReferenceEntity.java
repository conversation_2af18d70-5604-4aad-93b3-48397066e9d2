package com.facishare.webpage.customer.dao.entity;

import lombok.Data;
import org.mongodb.morphia.annotations.*;

/**
 * 引用关系实体类 用于管理资源引用关系，如站点页面引用文件资源
 */
@Data
@Entity(value = "ReferenceEntity", noClassnameStored = true)
@Indexes({
        @Index(fields = {
                @Field("tenantId"),
                @Field("targetType"),
                @Field("targetId")}, options = @IndexOptions(background = true)),
        @Index(fields = {
                @Field("tenantId"),
                @Field("sourceType"),
                @Field("sourceId")}, options = @IndexOptions(background = true)),
        @Index(fields = {
                @Field("tenantId"),
                @Field("indexName"),
                @Field("indexValue")}, options = @IndexOptions(background = true)),
        @Index(fields = {
                @Field("tenantId"),
                @Field("indexName"),
                @Field("indexValue"),
                @Field("sourceType"),
                @Field("sourceId"),
                @Field("targetType"),
                @Field("targetId")}, options = @IndexOptions(background = true, unique = true))
})
public class ReferenceEntity {
    /**
     * MongoDB ID
     */
    @Id
    private String id;

    /**
     * 租户ID
     */
    @Property("tenantId")
    private Integer tenantId;

    /**
     * 被引用者类型，如"file"
     */
    @Property("targetType")
    private String targetType;

    /**
     * 被引用者ID，如文件的apiName
     */
    @Property("targetId")
    private String targetId;

    /**
     * 引用者类型，如"site/page/component"
     */
    @Property("sourceType")
    private String sourceType;

    /**
     * 引用者ID，如组件ID
     */
    @Property("sourceId")
    private String sourceId;

    /**
     * 索引名称，用于快速查询，如"siteApiName"
     */
    @Property("indexName")
    private String indexName;

    /**
     * 索引值，如站点apiName
     */
    @Property("indexValue")
    private String indexValue;

    /**
     * 创建人ID
     */
    @Property("creatorId")
    private Integer creatorId;

    /**
     * 创建时间
     */
    @Property("createTime")
    private Long createTime;

}
