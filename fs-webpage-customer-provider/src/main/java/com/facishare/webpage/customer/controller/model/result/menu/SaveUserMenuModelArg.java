package com.facishare.webpage.customer.controller.model.result.menu;


import com.facishare.webpage.customer.controller.model.arg.BaseArg;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SaveUserMenuModelArg extends BaseArg{

    private String key;
    private String value;


    @Override
    public void valid() throws Exception {

    }
}
