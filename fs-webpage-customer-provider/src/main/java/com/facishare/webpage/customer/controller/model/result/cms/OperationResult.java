package com.facishare.webpage.customer.controller.model.result.cms;

import com.facishare.webpage.customer.dao.entity.FileEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 操作结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OperationResult implements Serializable {

    /**
     * 操作是否成功
     */
    private boolean success;

    private List<OperationInfo> operationInfoList;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class OperationInfo implements Serializable {
        private String apiName;
        private String name;
        private String type;
        private boolean success;

        public static OperationInfo of(FileEntity fileEntity, String type, boolean success) {
            return OperationInfo.builder()
                    .apiName(fileEntity.getApiName())
                    .name(fileEntity.getName())
                    .type(type)
                    .success(success)
                    .build();

        }
    }


    /**
     * 创建成功结果
     */
    public static OperationResult success() {
        return OperationResult.builder().success(true).build();
    }
}
