package com.facishare.webpage.customer.api.model.arg;

import lombok.Data;

import java.io.Serializable;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2021/10/14 3:27 下午
 */
@Data
public class QueryPaasAppArg implements Serializable {

    private int enterpriseId;
    private String enterpriseAccount;
    private Integer employeeId;
    private Locale locale;
    private String queryType;
    //应用的类型 （linkApp：互联应用，platApp:平台应用）
    private String applicationType;
    private boolean translateFlag;
    private String templeType;


}
