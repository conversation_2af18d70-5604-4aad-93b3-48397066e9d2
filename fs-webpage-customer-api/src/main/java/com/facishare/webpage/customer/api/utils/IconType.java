package com.facishare.webpage.customer.api.utils;

public interface IconType {
    /**
     * PNG类型图片
     * icon的值会直接拼接CDN地址, 前端会直接渲染图片，不处理 -> 自定义的icon (不区分面性线性)
     */
    String IMG_PNG = "image/png+xml";

    /**
     * SVG类型图片
     * 当icon类型为SVG时，根据icon的路径格式有两种处理方式：
     * 1. 如果是全路径（以http或https开头）： -> 预设的icon (区分面性线性)
     *    - 会从路径中解析出文件名（如：xxx.svg）
     *    - 如果文件名是"CRM" 或者 格式为"icon数字"（如icon1），会映射到前端固定的类上
     *    - 如果文件名既不是"CRM"也不是"icon数字"格式，则直接使用原始URL路径渲染图片
     * 2. 如果不是全路径： -> 自定义的icon
     *    - icon 值拼接路径从图片服务获取图片后直接渲染
     */
    String IMAGE_SVG = "image/svg+xml";

    /*
     * 当uploadIconType为空值（null/undefined）或空字符串("")时，会直接使用mIcon(也就是newIcon)作为图标(前端的默认图表) -> 默认的icon
     */


    // 以上为 出现在主导航时的逻辑
    // 当收纳在更多时, 1. 如果 appType = 1(自建应用, 也就是企业内跳转到外部链接的应用), 强制拿icon 2. 当uploadIconType非空时, 执行以上逻辑 3. 否则读取newIcon
}
