package com.facishare.webpage.customer.core.service;

import com.facishare.webpage.customer.api.model.I18nTrans;
import com.fxiaoke.i18n.client.api.Localization;

import java.util.List;
import java.util.Map;

public interface I18nService {


    String getCurrentI18nValue(Integer tenantId, String key, String defaultValue, boolean isOnTime, String lang, boolean isNeedValidateLicense);


    Map<String, String> getOnTimeTransValue(Integer tenantId, List<String> keyList, String langTag, boolean isOnTime, boolean isNeedValidateLicense);

    Map<String, Localization> getTransValue(Integer tenantId, List<String> keyList);

    Map<String, Localization> getLocalizationByKey(Integer tenantId, List<String> keyList, boolean isNeedValidateLicense, boolean isOnTime);

    Map<String, String> getTransValue(Integer tenantId, List<String> keyList, String lang);

    Map<String, String> getTransValue(Integer tenantId, List<String> keyList, String lang, boolean isNeedValidateLicense);

    void syncTransValue(Integer tenantId, Map<String, String> keyToNewName, String lang);

    Map<String, String> getWebComponentPreTranslate(Integer tenantId, String appId, String lang);

    Map<String, String> getAppComponentPreTranslate(Integer tenantId, String appId, String lang);

    void syncTransValueIncludePreKey(Integer tenantId, List<I18nTrans.TransArg> i18nTrans, String lang);

    void syncTransValueIncludePreKeyV2(Integer tenantId, List<I18nTrans.TransArg> transArg, String lang);

    List<Localization> buildSyncParam(Integer tenantId, List<I18nTrans.TransArg> transArg, String lang);

    List<Localization> buildSyncParam(Integer tenantId, List<I18nTrans.TransArg> transArg, String lang, boolean isReplace);

    List<Localization> buildSyncParamV2(Integer tenantId, List<I18nTrans.TransArg> transArgList, String lang, boolean isReplace);

    List<Localization> buildSyncParam(Integer tenantId, Map<String, String> keyToNewName, String lang);

    void syncTransValue(Integer tenantId, List<Localization> localizations);

    Map<String, String> getTransValueIncludePreKey(Integer tenantId, List<I18nTrans.TransArg> transArgList, String lang);

    Map<String, String> getTransValueIncludePreKeyV2(Integer tenantId, List<I18nTrans.TransArg> transArgList, String lang);

    Map<String, String> batchGetRealNameByNoneLicense(Integer tenantId, List<I18nTrans.TransArg> transArgList, String lang);

}
