package com.facishare.webpage.customer.api.service;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.arg.AutomaticSynNewViewArg;
import com.facishare.webpage.customer.api.model.arg.RollBackSynNewViewArg;
import com.facishare.webpage.customer.api.model.result.AutomaticSynNewViewResult;
import com.facishare.webpage.customer.api.model.result.RollbackSynNewViewResult;

import java.util.Map;

@RestResource(
        value = "WebPageResource",
        desc = "老版CRM迁移至新版CRM或其他PAAS应用",//ignoreI18n
        contentType = "application/json"
)
public interface SynNewViewRestService {

    @POST(value = "/webPage/synNewViewRestService/rollbackSynNewView", desc = "迁移回滚")//ignoreI18n
    RollbackSynNewViewResult rollbackSynNewVieww(@HeaderMap Map<String, String> headers, @Body RollBackSynNewViewArg arg);

    @POST(value = "/webPage/synNewViewRestService/automaticSynNewView", desc = "自动迁移")//ignoreI18n
    AutomaticSynNewViewResult automaticSynNewView(@HeaderMap Map<String, String> headers, @Body AutomaticSynNewViewArg arg);

}
