package com.facishare.webpage.customer.api.console.result;

import com.facishare.webpage.customer.api.console.item.*;
import io.protostuff.Tag;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QueryHomePageResult implements Serializable {
    @Tag(1)
    private List<HomePageItem> homePageList;
    @Tag(2)
    private List<CurrentHomePageItem> currentHomePageList;
    @Tag(3)
    private List<EmployeeConfigItem> employeeConfigList;
}
