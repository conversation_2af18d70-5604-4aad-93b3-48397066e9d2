package com.facishare.webpage.customer.service.impl;

import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.paas.auth.model.AuthContext;
import com.facishare.webpage.customer.api.constant.CmsStatus;
import com.facishare.webpage.customer.api.constant.ErrorMessageI18NKey;
import com.facishare.webpage.customer.api.exception.ValidateException;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.controller.model.arg.cms.*;
import com.facishare.webpage.customer.controller.model.result.cms.ConvertResourceResult;
import com.facishare.webpage.customer.controller.model.result.cms.OperationResult;
import com.facishare.webpage.customer.controller.model.result.cms.ResourceListResult;
import com.facishare.webpage.customer.controller.model.result.cms.ValidateFileStatusResult;
import com.facishare.webpage.customer.controller.model.vo.cms.ResourceInfoVO;
import com.facishare.webpage.customer.core.model.CalculateUrlArg;
import com.facishare.webpage.customer.core.service.FileService;
import com.facishare.webpage.customer.dao.FileEntityDao;
import com.facishare.webpage.customer.dao.ReferenceEntityDao;
import com.facishare.webpage.customer.dao.WorkSpaceEntityDao;
import com.facishare.webpage.customer.dao.entity.FileEntity;
import com.facishare.webpage.customer.dao.entity.WorkSpaceEntity;
import com.facishare.webpage.customer.service.CmsResourceService;
import com.facishare.webpage.customer.util.PaginationUtil;
import com.facishare.webpage.customer.util.ReferenceTargetType;
import com.facishare.webpage.customer.util.SearchValidationUtil;
import com.fxiaoke.paas.auth.factory.FuncClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.webpage.customer.core.service.impl.FileServiceImpl.getGeneralUrlKey;

/**
 * CMS资源管理服务实现类
 */
@Service
@Slf4j
public class CmsResourceServiceImpl implements CmsResourceService {

    @Resource
    private FileEntityDao fileEntityDao;

    @Resource
    private ReferenceEntityDao referenceEntityDao;

    @Resource
    private WorkSpaceEntityDao workSpaceEntityDao;

    @Resource
    private FileService fileService;
    @Resource
    FuncClient funcClient;

    private final String CMS_FUNCTION_CODE = "uipaascms";
    private final String SYSTEM = "facishare-system";//管理权限的appId

    private final String NODE_TYPE_FILE = "file";
    private final String NODE_TYPE_DIR = "dir";

    @Override
    public ResourceListResult getResourceListByWorkSpace(User userInfo, GetResourceListArg arg) {
        if (StringUtils.isAnyBlank(arg.getWorkSpaceApiName())) {
            throw ValidateException.fromI18N(ErrorMessageI18NKey.PARAMS_ERROR);
        }

        // 检查工作区是否存在
        WorkSpaceEntity workSpaceEntity = workSpaceEntityDao.findByApiName(userInfo.getTenantId(), arg.getWorkSpaceApiName());
        if (CmsStatus.ENABLE.equals(arg.getStatus())
                && (workSpaceEntity == null || CmsStatus.DISABLE.equals(workSpaceEntity.getStatus()))) {
            log.warn("Workspace not found, workSpaceApiName: {}", arg.getWorkSpaceApiName());
            throw ValidateException.fromI18N(ErrorMessageI18NKey.WORKSPACE_NOT_EXISTS);
        }
        // 验证搜索关键字，防止正则表达式特殊字符
        SearchValidationUtil.validateSearchKeyword(arg.getName());

        //判断当前人是否有cms的管理功能权限
        boolean hasPermission = hasPermission(userInfo);

        // 构建查询条件
        FileEntity condition = new FileEntity();
        condition.setTenantId(userInfo.getTenantId());

        if (StringUtils.isNotBlank(arg.getName())) {
            condition.setName(arg.getName());
        }
        if (StringUtils.isNotBlank(arg.getWorkSpaceApiName())) {
            condition.setWorkSpace(arg.getWorkSpaceApiName());
        }
        if (Objects.nonNull(arg.getStatus())) {
            condition.setStatus(arg.getStatus());
        }

        // 分页查询
        int offset = (arg.getPageNum() - 1) * arg.getPageSize();

        long count = fileEntityDao.findCountByCondition(condition);

        if (count == 0) {
            return ResourceListResult.builder().hasPermission(hasPermission).build();
        }

        // 查询工作区下的所有文件
        List<FileEntity> fileEntities = fileEntityDao.findByCondition(condition, offset, arg.getPageSize());

        if (CollectionUtils.isEmpty(fileEntities)) {
            return ResourceListResult.builder().hasPermission(hasPermission).build();
        }

        Map<String, String> fileUrl = getFileUrl(userInfo, fileEntities);
        // 转换为VO
        return ResourceListResult.builder()
                .resourceList(fileEntities.stream().map(x -> convertToResourceInfoVO(x, fileUrl)).collect(Collectors.toList()))
                .pageNum(arg.getPageNum())
                .hasPermission(hasPermission)
                .pageSize(arg.getPageSize())
                .totalNum(count)
                .build();
    }

    private boolean hasPermission(User userInfo) {
        AuthContext authContext = new AuthContext();
        authContext.setUserId(String.valueOf(userInfo.getUserId()));
        authContext.setTenantId(String.valueOf(userInfo.getTenantId()));
        if (Objects.nonNull(userInfo.getOutTenantId()) && Objects.nonNull(userInfo.getOutUserId())) {
            authContext.setOuterUserId(String.valueOf(userInfo.getOutUserId()));
            authContext.setOuterTenantId(String.valueOf(userInfo.getOutTenantId()));
        }
        authContext.setAppId(SYSTEM);
        Map<String, Boolean> map = funcClient.userFuncPermissionCheck(authContext, Sets.newHashSet(CMS_FUNCTION_CODE));
        boolean hasPermission = BooleanUtils.isTrue(map.get(CMS_FUNCTION_CODE));
        return hasPermission;
    }

    @Override
    public Map<String, String> getFileUrl(User user, List<FileEntity> fileEntities) {
        List<CalculateUrlArg> args = fileEntities.stream().map(x -> new CalculateUrlArg(x.getPath(), x.getName(), x.getSignature(), x.getExtra()))
                .collect(Collectors.toList());
        Map<String, String> urlMap = fileService.batchCalculateUrl(user, args);
        if (CollectionUtils.isEmpty(urlMap)) {
            return Maps.newHashMap();
        }
        Map<String, String> fileToUrl = Maps.newHashMap();
        fileEntities.forEach(x -> {
            String key = getGeneralUrlKey(x.getPath(), x.getSignature());
            String url = urlMap.get(key);
            if (StringUtils.isNotBlank(url)) {
                fileToUrl.put(x.getApiName(), url);
            }
        });

        return fileToUrl;

    }

    /**
     * @param args 文件信息列表
     * @return
     */
    @Override
    public void batchSaveFileInfo(User user, SaveFileInfoArg args) {
        log.info("Batch save file info, args: {}", args);

        List<SaveFileInfoArg.SaveFileInfo> saveFileInfoList = args.getSaveFileInfoList();
        if (CollectionUtils.isEmpty(saveFileInfoList)) {
            return;
        }

        Set<String> workSpaceApiNameList = saveFileInfoList.stream().map(SaveFileInfoArg.SaveFileInfo::getWorkSpaceApiName).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(workSpaceApiNameList)) {
            log.warn("upload file workSpaceApiName is null");
            throw ValidateException.fromI18N(ErrorMessageI18NKey.PARAMS_ERROR);
        }
        //校验工作区是否存在
        validateWorkSpace(user, workSpaceApiNameList);
        //校验参数并将其转换为文件对象
        List<FileEntity> fileEntityList = validateAndConvertFile(user, saveFileInfoList, workSpaceApiNameList);

        if (CollectionUtils.isEmpty(fileEntityList)) {
            return;
        }
        convertPathInfo(user, fileEntityList);
        fileEntityDao.batchSave(user, fileEntityList);
    }

    private void convertPathInfo(User user, List<FileEntity> fileEntityList) {
        Set<String> tNPathSet = com.facishare.webpage.customer.core.util.CollectionUtils.nullToEmpty(fileEntityList).stream()
                .map(FileEntity::getPath).filter(x -> x.startsWith("TN_")).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(tNPathSet)) {
            return;
        }
        //tNPath -> nPath
        Map<String, String> nPathMap = fileService.batchTnConvertNPath(user, tNPathSet);
        //nPath -> signature
        Map<String, String> signatureByNPath = fileService.batchGetSignatureByNPath(user, nPathMap.values());
        Set<String> failFileSet = Sets.newHashSet();
        for (FileEntity fileEntity : fileEntityList) {
            String tNPath = fileEntity.getPath();
            String nPath = nPathMap.get(tNPath);
            if (StringUtils.isEmpty(nPath)) {
                failFileSet.add(fileEntity.getApiName());
            }
            String signature = signatureByNPath.get(nPath);
            if (StringUtils.isEmpty(signature)) {
                failFileSet.add(fileEntity.getApiName());
            }
            fileEntity.setPath(nPath);
            fileEntity.setSignature(signature);
        }
        if (com.facishare.webpage.customer.core.util.CollectionUtils.notEmpty(failFileSet)) {
            throw ValidateException.fromI18N(ErrorMessageI18NKey.FILE_PATH_CONVERT_FAILED);
        }
    }

    private List<FileEntity> validateAndConvertFile(User user, List<SaveFileInfoArg.SaveFileInfo> args, Set<String> workSpaceApiNameList) {
        if (CollectionUtils.isEmpty(args) || CollectionUtils.isEmpty(workSpaceApiNameList)) {
            return Lists.newArrayList();
        }
        Map<String, List<FileEntity>> workSpaceMap = fileEntityDao.batchFindByWorkSpace(user.getTenantId(), workSpaceApiNameList);
        List<FileEntity> addFileEntities = Lists.newArrayList();
        for (SaveFileInfoArg.SaveFileInfo arg : args) {
            if (Objects.isNull(arg)) {
                continue;
            }
            List<FileEntity> fileEntityList = workSpaceMap.get(arg.getWorkSpaceApiName());
            Map<String, FileEntity> fileEntityMap = Maps.newHashMap();
            if (com.facishare.webpage.customer.core.util.CollectionUtils.notEmpty(fileEntityList)) {
                fileEntityMap.putAll(fileEntityList.stream().collect(Collectors.toMap(FileEntity::getApiName, Function.identity())));
            }
            // 检查父文件夹是否存在（如果有）
            if (StringUtils.isNotBlank(arg.getParentApiName())) {
                FileEntity parentFile = fileEntityMap.get(arg.getParentApiName());
                if (parentFile == null) {
                    log.warn("Parent folder not found, parentApiName: {}", arg.getParentApiName());
                    throw ValidateException.fromI18N(ErrorMessageI18NKey.PARENT_FOLDER_NOT_EXISTS);
                }
                if (!"folder".equals(parentFile.getType())) { //todo 在FileEntity中增加方法来判断
                    log.warn("Parent is not a folder, parentApiName: {}", arg.getParentApiName());
                    throw ValidateException.fromI18N(ErrorMessageI18NKey.PARENT_NOT_FOLDER);
                }
            }
            // 生成文件ApiName（如果没有）
            if (StringUtils.isBlank(arg.getApiName())) {
                log.warn("fileApiName not exists! fileInfo:{}", arg);
                throw ValidateException.fromI18N(ErrorMessageI18NKey.FILE_API_NAME_NOT_EXISTS);
            }
            // 检查文件是否已存在
            FileEntity existingFile = fileEntityMap.get(arg.getApiName());
            if (Objects.nonNull(existingFile)) {
                log.warn("File already exists,upload fileApiName repeat, fileApiName: {}", arg.getApiName());
                throw ValidateException.fromI18N(ErrorMessageI18NKey.FILE_API_NAME_REPEAT);
            }
            // 转换文件
            addFileEntities.add(convertToFileEntity(user, arg));
        }
        return addFileEntities;
    }

    private FileEntity convertToFileEntity(User user, SaveFileInfoArg.SaveFileInfo arg) {
        FileEntity fileEntity = new FileEntity();
        fileEntity.setApiName(arg.getApiName());
        fileEntity.setName(arg.getName());
        fileEntity.setType(arg.getType());
        fileEntity.setNodeType(arg.getNodeType());
        fileEntity.setPath(arg.getPath());
        fileEntity.setMimeType(arg.getMimeType());
        fileEntity.setWorkSpace(arg.getWorkSpaceApiName());
        fileEntity.setParentApiName(arg.getParentApiName());
        fileEntity.setStatus(CmsStatus.ENABLE); // 默认启用状态
        fileEntity.setTenantId(user.getTenantId());
        fileEntity.setCreatorId(String.valueOf(user.getUserId()));
        fileEntity.setCreateTime(System.currentTimeMillis());
        fileEntity.setUpdaterId(String.valueOf(user.getUserId()));
        fileEntity.setUpdateTime(System.currentTimeMillis());
        fileEntity.setExtra(arg.getExtra());
        return fileEntity;
    }

    /**
     * 校验库中不存在的工作区
     *
     * @param user
     * @param workSpaceApiNameList
     */
    private void validateWorkSpace(User user, Set<String> workSpaceApiNameList) {
        if (CollectionUtils.isEmpty(workSpaceApiNameList)) {
            return;
        }
        List<WorkSpaceEntity> workSpaceList = workSpaceEntityDao.findWorkSpaceList(user.getTenantId(), workSpaceApiNameList);
        Set<String> dbWorkSpaceList = workSpaceList.stream().map(WorkSpaceEntity::getApiName).collect(Collectors.toSet());
        Collection<String> subtractList = org.apache.commons.collections4.CollectionUtils.subtract(workSpaceApiNameList, dbWorkSpaceList);
        if (!CollectionUtils.isEmpty(subtractList)) {
            throw ValidateException.fromI18N(ErrorMessageI18NKey.WORKSPACE_NOT_EXISTS);
        }
    }

    @Override
    public List<OperationResult.OperationInfo> batchDeleteResource(User user, DeleteResourceArg args) {
        log.info("Batch delete resources, args: {}", args);

        List<DeleteResourceArg.DeleteResource> deleteResourceList = args.getResourceList();

        if (CollectionUtils.isEmpty(deleteResourceList)) {
            return Lists.newArrayList();
        }

        Set<String> workSpaceApiNameSets = deleteResourceList.stream().filter(x -> Objects.equals(x.getNodeType(), NODE_TYPE_DIR))
                .map(DeleteResourceArg.DeleteResource::getWorkSpaceApiName).collect(Collectors.toSet());

        Set<String> apiNameSet = deleteResourceList.stream().map(DeleteResourceArg.DeleteResource::getApiName).collect(Collectors.toSet());
        //todo 删除文件夹时，需要看下级文件是否可以删除，如果不能删，则上级文件夹也不能删除
        List<FileEntity> allFile = findCascadeByApiNames(user, apiNameSet, workSpaceApiNameSets);
        if (CollectionUtils.isEmpty(allFile)) {
            return Lists.newArrayList();
        }

        //文件未禁用导致失败
        List<OperationResult.OperationInfo> operationInfoList = Lists.newArrayList();
        List<FileEntity> enableFileEntityList = allFile.stream().filter(x -> CmsStatus.ENABLE.equals(x.getStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(enableFileEntityList)) {
            operationInfoList.addAll(enableFileEntityList.stream()
                    .map(x -> OperationResult.OperationInfo.of(x, "status", false))
                    .collect(Collectors.toList()));
            allFile.removeIf(x -> CmsStatus.ENABLE.equals(x.getStatus()));
        }

        //文件有引用关系导致失败
        Set<String> allFileApiNameSet = allFile.stream().map(FileEntity::getApiName).collect(Collectors.toSet());
        List<String> hasRelatedFileApiNameList = referenceEntityDao.filterHasRelatedTargetIds(String.valueOf(user.getTenantId()), ReferenceTargetType.FILE, allFileApiNameSet, null, null);

        if (!CollectionUtils.isEmpty(hasRelatedFileApiNameList)) {
            List<FileEntity> hasReferenceFileEntityList = allFile.stream().filter(x -> hasRelatedFileApiNameList.contains(x.getApiName())).collect(Collectors.toList());
            operationInfoList.addAll(hasReferenceFileEntityList.stream()
                    .map(x -> OperationResult.OperationInfo.of(x, "reference", false))
                    .collect(Collectors.toList()));
            allFile.removeIf(x -> hasRelatedFileApiNameList.contains(x.getApiName()));

        }

        //成功删除的文件
        fileEntityDao.batchDelete(user, allFile);
        operationInfoList.addAll(allFile.stream()
                .map(x -> OperationResult.OperationInfo.of(x, null, true))
                .collect(Collectors.toList()));

        return operationInfoList;
    }

    public List<FileEntity> findCascadeByApiNames(User user, Set<String> fileApiNames, Set<String> workSpaceApiNames) {
        if (Objects.isNull(user)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(workSpaceApiNames)) {
            List<FileEntity> fileEntityList = fileEntityDao.findByApiNames(user.getTenantId(), fileApiNames);
            workSpaceApiNames = fileEntityList.stream().map(FileEntity::getWorkSpace)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        }
        Map<String, List<FileEntity>> workSpaceToFile = fileEntityDao.batchFindByWorkSpace(user.getTenantId(), workSpaceApiNames);
        Map<String, FileEntity> fileEntityMap = Maps.newHashMap();
        Map<String, List<FileEntity>> parentEntityMap = Maps.newHashMap();

        for (List<FileEntity> fileEntityList : workSpaceToFile.values()) {
            fileEntityList.forEach(x -> fileEntityMap.put(x.getApiName(), x));
            fileEntityList.stream()
                    .filter(x -> StringUtils.isNotBlank(x.getParentApiName()))
                    .forEach(x -> parentEntityMap.computeIfAbsent(x.getParentApiName(), it -> Lists.newArrayList()).add(x));
        }
        List<FileEntity> allFile = Lists.newArrayList();
        for (String fileApiName : fileApiNames) {
            FileEntity fileEntity = fileEntityMap.get(fileApiName);
            if (Objects.isNull(fileEntity)) {
                continue;
            }
            if (NODE_TYPE_FILE.equals(fileEntity.getNodeType())) {
                allFile.add(fileEntity);
            }
            if (NODE_TYPE_DIR.equals(fileEntity.getNodeType())) {
                allFile.add(fileEntity);
                // 递归获取文件夹下的所有文件
                getAllFilesRecursively(fileEntity.getApiName(), parentEntityMap, allFile, 1);
            }
        }
        return allFile;
    }

    /**
     * 递归获取文件夹下所有文件
     *
     * @param parentApiName   父文件夹的ApiName
     * @param parentEntityMap 存储父子关系的映射表，key为父文件夹ApiName，value为子文件列表
     * @param allFiles        用于存储所有找到的文件的集合
     * @param currentLevel
     */
    private void getAllFilesRecursively(String parentApiName, Map<String, List<FileEntity>> parentEntityMap, List<FileEntity> allFiles, int currentLevel) {
        // 如果已经超过3层，则停止递归
        if (currentLevel > 3) {
            return;
        }
        // 获取当前父文件夹下的所有子文件
        List<FileEntity> children = parentEntityMap.get(parentApiName);
        // 如果父文件夹没有子文件，直接返回
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        // 遍历所有子文件
        for (FileEntity childEntity : children) {
            // 添加到结果列表中
            allFiles.add(childEntity);
            // 如果子文件是文件夹，则递归处理
            if (NODE_TYPE_DIR.equals(childEntity.getNodeType())) {
                getAllFilesRecursively(childEntity.getApiName(), parentEntityMap, allFiles, ++currentLevel);
            }
        }
    }

    @Override
    public void moveResource(User user, MoveResourceArg arg) {
        log.info("Move resources, arg: {}", arg);

        List<MoveResourceArg.MoveResourceItem> moveResourceList = arg.getMoveResourceList();
        if (CollectionUtils.isEmpty(moveResourceList)) {
            return;
        }

        // 收集所有文件ApiName和目标文件夹ApiName
        Set<String> fileApiNames = new HashSet<>();
        Set<String> targetFolderApiNames = new HashSet<>();

        for (MoveResourceArg.MoveResourceItem item : moveResourceList) {
            fileApiNames.add(item.getApiName());
            if (StringUtils.isNotBlank(item.getNewParentApiName())) {
                targetFolderApiNames.add(item.getNewParentApiName());
            }
        }

        // 批量查询所有需要移动的文件
        List<FileEntity> fileEntities = fileEntityDao.findByApiNames(user.getTenantId(), fileApiNames);

        // 建立文件ApiName到实体的映射
        Map<String, FileEntity> fileEntityMap = fileEntities.stream()
                .collect(Collectors.toMap(FileEntity::getApiName, entity -> entity));

        // 检查所有文件是否都存在
        for (MoveResourceArg.MoveResourceItem item : moveResourceList) {
            if (!fileEntityMap.containsKey(item.getApiName())) {
                log.warn("File not found, fileApiName: {}", item.getApiName());
                throw ValidateException.fromI18N(ErrorMessageI18NKey.FILE_NOT_EXISTS);
            }
        }
        // 如果有目标文件夹，批量查询
        Map<String, FileEntity> folderEntityMap;
        if (!targetFolderApiNames.isEmpty()) {
            List<FileEntity> folderEntities = fileEntityDao.findByApiNames(user.getTenantId(), targetFolderApiNames);
            folderEntityMap = folderEntities.stream()
                    .collect(Collectors.toMap(FileEntity::getApiName, entity -> entity));

            // 检查所有目标文件夹是否都存在且类型正确
            for (String folderApiName : targetFolderApiNames) {
                FileEntity folderEntity = folderEntityMap.get(folderApiName);
                if (folderEntity == null) {
                    log.warn("Target folder not found, targetParentApiName: {}", folderApiName);
                    throw ValidateException.fromI18N(ErrorMessageI18NKey.TARGET_FOLDER_NOT_EXISTS);
                }
                if (!NODE_TYPE_DIR.equals(folderEntity.getNodeType())) {
                    log.warn("Target is not a folder, targetParentApiName: {}", folderApiName);
                    throw ValidateException.fromI18N(ErrorMessageI18NKey.TARGET_NOT_FOLDER);
                }
            }
        }

        // 在内存中更新所有文件实体
        List<FileEntity> updatedEntities = new ArrayList<>();
        for (MoveResourceArg.MoveResourceItem item : moveResourceList) {
            FileEntity fileEntity = fileEntityMap.get(item.getApiName());
            fileEntity.setParentApiName(item.getNewParentApiName());
            fileEntity.setUpdaterId(String.valueOf(user.getUserId()));
            fileEntity.setUpdateTime(System.currentTimeMillis());
            updatedEntities.add(fileEntity);
        }
        // 批量更新到数据库
        fileEntityDao.batchUpdate(user, updatedEntities);

    }

    @Override
    public void updateResource(User user, UpdateResourceArg arg) {
        log.info("Update resource information, arg: {}", arg);

        List<UpdateResourceArg.UpdateResourceItem> updateResourceList = arg.getResourceList();
        if (CollectionUtils.isEmpty(updateResourceList)) {
            return;
        }
        List<String> fileApiNameList = updateResourceList.stream().map(UpdateResourceArg.UpdateResourceItem::getApiName).collect(Collectors.toList());
        List<FileEntity> fileEntities = fileEntityDao.findByApiNames(user.getTenantId(), fileApiNameList);
        if (CollectionUtils.isEmpty(fileEntities)) {
            throw ValidateException.fromI18N(ErrorMessageI18NKey.FILE_NOT_EXISTS);
        }
        Map<String, FileEntity> fileEntityMap = fileEntities.stream().collect(Collectors.toMap(FileEntity::getApiName, it -> it, (x1, x2) -> x1));
        List<FileEntity> fileEntityToUpdateList = Lists.newArrayList();
        for (UpdateResourceArg.UpdateResourceItem item : updateResourceList) {
            // 检查文件是否存在
            FileEntity fileEntity = fileEntityMap.get(item.getApiName());
            if (Objects.isNull(fileEntity)) {
                log.warn("File not found, fileApiName: {}", item.getApiName());
                throw ValidateException.fromI18N(ErrorMessageI18NKey.FILE_NOT_EXISTS);
            }
            fileEntityToUpdateList.add(convertUpdateResourceItemToFile(item, fileEntity, user));
        }
        convertPathInfo(user, fileEntityToUpdateList);
        fileEntityDao.batchUpdate(user, fileEntityToUpdateList);
    }

    /**
     * 将UpdateResourceItem转换为FileEntity
     *
     * @param item       更新项参数
     * @param fileEntity 需要更新的FileEntity
     * @param user       当前用户
     * @return 更新后的FileEntity
     */
    public FileEntity convertUpdateResourceItemToFile(UpdateResourceArg.UpdateResourceItem item, FileEntity fileEntity, User user) {
        if (item == null || fileEntity == null) {
            return fileEntity;
        }

        if (StringUtils.isNotBlank(item.getName())) {
            fileEntity.setName(item.getName());
        }

        if (item.getStatus() != null) {
            fileEntity.setStatus(item.getStatus());
        }

        if (item.getExtra() != null) {
            fileEntity.setExtra(item.getExtra());
        }

        if (StringUtils.isNotBlank(item.getDescription())) {
            fileEntity.setDescription(item.getDescription());
        }

        if (StringUtils.isNotBlank(item.getType())) {
            fileEntity.setType(item.getType());
        }

        if (StringUtils.isNotBlank(item.getMimeType())) {
            fileEntity.setMimeType(item.getMimeType());
        }

        if (StringUtils.isNotBlank(item.getNodeType())) {
            fileEntity.setNodeType(item.getNodeType());
        }

        if (StringUtils.isNotBlank(item.getPath())) {
            fileEntity.setPath(item.getPath());
        }

        if (StringUtils.isNotBlank(item.getParentApiName())) {
            fileEntity.setParentApiName(item.getParentApiName());
        }

        // 如果有用户信息，设置更新者信息
        if (user != null) {
            fileEntity.setUpdaterId(String.valueOf(user.getUserId()));
            fileEntity.setUpdateTime(System.currentTimeMillis());
        }

        return fileEntity;
    }

    @Override
    public void enableResource(User user, EnableResourceArg arg) {
        List<EnableResourceArg.EnableResourceItem> enableResourceList = arg.getResourceList();
        if (CollectionUtils.isEmpty(enableResourceList)) {
            return;
        }

        // 收集所有需要处理的文件ApiName
        Set<String> fileApiNames = enableResourceList.stream()
                .map(EnableResourceArg.EnableResourceItem::getApiName)
                .collect(Collectors.toSet());

        fileEntityDao.batchUpdateStatus(user, fileApiNames, CmsStatus.ENABLE);
    }

    @Override
    public void disableResource(User user, DisableResourceArg arg) {
        List<DisableResourceArg.DisableResourceItem> disableResourceList = arg.getResourceList();
        if (CollectionUtils.isEmpty(disableResourceList)) {
            return;
        }

        // 收集所有需要处理的文件ApiName
        Set<String> fileApiNames = disableResourceList.stream()
                .map(DisableResourceArg.DisableResourceItem::getApiName)
                .collect(Collectors.toSet());

        fileEntityDao.batchUpdateStatus(user, fileApiNames, CmsStatus.DISABLE);
    }

    @Override
    public List<FileEntity> findFileEntityByApiNames(User user, List<String> fileApiNameList) {
        return CollectionUtils.isEmpty(fileApiNameList) ? Lists.newArrayList() : fileEntityDao.findByApiNames(user.getTenantId(), fileApiNameList);
    }

    @Override
    public List<FileEntity> findByApiNamesIncludesDelete(User user, List<String> fileApiNameList) {
        return CollectionUtils.isEmpty(fileApiNameList) ? Lists.newArrayList() : fileEntityDao.findByApiNamesIncludesDelete(user.getTenantId(), fileApiNameList);
    }

    @Override
    public List<ValidateFileStatusResult.ValidateFileStatusInfo> validateFileStatus(User user, OuterUserInfo outerUserInfo, List<String> apiNameList) {
        //查询文件信息
        List<FileEntity> fileEntities = fileEntityDao.findByApiNamesIncludesDelete(user.getTenantId(), apiNameList);
        Map<String, FileEntity> fileEntityMap = fileEntities.stream().collect(Collectors.toMap(FileEntity::getApiName, it -> it, (x1, x2) -> x1));

        //查询工作区信息
        Set<String> workSpaceApiNames = fileEntities.stream().map(FileEntity::getWorkSpace).collect(Collectors.toSet());
        List<WorkSpaceEntity> workSpaceFileEntities = workSpaceEntityDao.findWorkSpaceIncludesDeleted(user.getTenantId(), workSpaceApiNames);
        Map<String, WorkSpaceEntity> workSpaceEntityMap = workSpaceFileEntities.stream()
                .collect(Collectors.toMap(WorkSpaceEntity::getApiName, it -> it, (x1, x2) -> x1));

        List<ValidateFileStatusResult.ValidateFileStatusInfo> validateList = Lists.newArrayList();
        for (String apiName : apiNameList) {
            FileEntity fileEntity = fileEntityMap.get(apiName);

            ValidateFileStatusResult.ValidateFileStatusInfo statusInfo = ValidateFileStatusResult.ValidateFileStatusInfo
                    .builder()
                    .apiName(apiName)
                    .status(true)
                    .build();
            //校验文件
            if (Objects.isNull(fileEntity) || !CmsStatus.ENABLE.equals(fileEntity.getStatus())) {
                statusInfo.setStatus(false);
                validateList.add(statusInfo);
                continue;
            }
            //校验工作区
            WorkSpaceEntity workSpaceEntity = workSpaceEntityMap.get(fileEntity.getWorkSpace());
            if (Objects.isNull(workSpaceEntity) || !CmsStatus.ENABLE.equals(workSpaceEntity.getStatus())) {
                statusInfo.setStatus(false);
                validateList.add(statusInfo);
                continue;
            }
            validateList.add(statusInfo);
        }
        return validateList;
    }

    @Override
    public List<ConvertResourceResult.ConvertResourceInfo> convertResourceByApiName(User user, OuterUserInfo outerUserInfo, List<String> apiNameList) {
        if (CollectionUtils.isEmpty(apiNameList)) {
            log.warn("ApiName cannot be empty");
            throw ValidateException.fromI18N(ErrorMessageI18NKey.PARAMS_ERROR);
        }

        // 根据apiName查找文件实体
        List<FileEntity> fileEntityList = fileEntityDao.batchFindFileByApiNames(user.getTenantId(), apiNameList);
        if (Objects.isNull(fileEntityList) || CollectionUtils.isEmpty(fileEntityList)) {
            return Lists.newArrayList();
        }
        // 获取文件URL
        Map<String, String> fileUrlMap = getFileUrl(user, fileEntityList);

        List<ConvertResourceResult.ConvertResourceInfo> result = Lists.newArrayList();
        for (FileEntity fileEntity : fileEntityList) {
            String apiName = fileEntity.getApiName();
            String url = fileUrlMap.get(apiName);
            ConvertResourceResult.ConvertResourceInfo resourceInfo = ConvertResourceResult.ConvertResourceInfo.builder()
                    .name(fileEntity.getName())
                    .apiName(apiName)
                    .workSpaceApiName(fileEntity.getWorkSpace())
                    .url(url)
                    .build();
            result.add(resourceInfo);
        }
        return result;
    }

    /**
     * 将文件实体转换为VO
     */
    private ResourceInfoVO convertToResourceInfoVO(FileEntity entity, Map<String, String> fileUrl) {
        if (entity == null) {
            return null;
        }
        ResourceInfoVO vo = new ResourceInfoVO();
        BeanUtils.copyProperties(entity, vo);
        String url = fileUrl.get(entity.getApiName());
        if (StringUtils.isNotBlank(url)) {
            vo.setUrl(url);
        }
        return vo;
    }

}
