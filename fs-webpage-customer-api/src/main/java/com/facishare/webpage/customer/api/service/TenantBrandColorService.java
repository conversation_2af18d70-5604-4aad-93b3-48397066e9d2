package com.facishare.webpage.customer.api.service;

import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.result.GetTenantBrandColorResult;

@RestResource(
        value = "WebPageResource",
        desc = "主题色",//ignoreI18n
        contentType = "application/json"
)
public interface TenantBrandColorService {
    @POST(value = "/mainChannelManager/rest/getTenantBrandColor",desc = "获取主题色信息" )//ignoreI18n
    GetTenantBrandColorResult getTenantBrandColor(@HeaderParam("x-fs-ei") String tenantId);
}
