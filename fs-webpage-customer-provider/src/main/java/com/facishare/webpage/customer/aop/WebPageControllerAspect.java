package com.facishare.webpage.customer.aop;

import com.alibaba.fastjson.JSON;
import com.facishare.webpage.customer.api.exception.WebPageException;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by zhangyu on 2019/9/9
 */
@Aspect
public class WebPageControllerAspect {

    private static Logger logger = LoggerFactory.getLogger(WebPageControllerAspect.class);

    @Before(value = "execution(* com.facishare.webpage.customer.controller..*.*(..))")
    public void beforeCall(JoinPoint joinPoint) {
        logger.info("before WebPageController method:{} Args:{}", joinPoint.getSignature().toShortString(), JSON.toJSONString(joinPoint.getArgs()));
    }

    @AfterReturning(value = "execution(* com.facishare.webpage.customer.controller..*.*(..))", returning = "ret")
    public void afterReturn(JoinPoint joinPoint, Object ret) {
        logger.info("afterReturn, WebPageController:{} arg:{} Return:-",
                joinPoint.getSignature().toShortString(), joinPoint.getArgs());
    }

    @AfterThrowing(value = "execution(* com.facishare.webpage.customer.controller..*.*(..))", throwing = "e")
    public void afterThrowing(JoinPoint joinPoint, Throwable e) throws Throwable {
        if (e instanceof WebPageException) {
            logger.warn("afterThrowing, WebPageController:{} ", joinPoint.getSignature().toShortString(), e);
        } else {
            logger.error("afterThrowing, WebPageController:{} ", joinPoint.getSignature().toShortString(), e);
            throw e;
        }
    }

}
