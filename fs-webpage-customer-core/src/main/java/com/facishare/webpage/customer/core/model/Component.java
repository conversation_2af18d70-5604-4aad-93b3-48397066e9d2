package com.facishare.webpage.customer.core.model;

import com.facishare.webpage.customer.api.model.core.TenantPrivilege;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 19/12/10.
 */
@Data
public class Component {

    private String id;

    private String parentId;       //当前组件父级Id，如果为空，则标识它就第一级组件

    private int componentType;               //component类型，1 分组；2 MenuCollection；3 WidgetCollection；4 Widget；

    private String title;          //显示标题，仅当  componentType 为 1 时，该字段才有意义；其他情况直接使用 collection 的名称；

    private String titleI18nKey;          //标题的 i18n-key

    private String collectionId;    //数据CollectionId，当 componentType 为 2 时标识 menuCollection 的Id；当为 3 时，标识 widgetCollection 的Id

    private int widgetSourceType;

    private  TenantPrivilege tenantPrivilege;
    /**
     * 灰度eiList
     */
    private List<Integer> grayEiList;

}
