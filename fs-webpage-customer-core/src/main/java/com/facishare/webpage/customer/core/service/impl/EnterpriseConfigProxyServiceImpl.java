package com.facishare.webpage.customer.core.service.impl;

import com.facishare.organization.adapter.api.config.model.GetConfigDto;
import com.facishare.organization.adapter.api.config.model.SetConfigDto;
import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService;
import com.facishare.webpage.customer.core.service.EnterpriseConfigProxyService;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("enterpriseConfigProxyService")
public class EnterpriseConfigProxyServiceImpl implements EnterpriseConfigProxyService {

    private static final String GO_NEW_CRM_KEY = "goNewCRM";
    private static final String NEW_CRM_OPEN_STATUS = "1";
    private static final String NEW_CRM_CLOSE_STATUS = "0";
    private static final int SYSTEM_USER_ID = -10000;

    @Autowired
    private EnterpriseConfigService enterpriseConfigService;

    private final Cache<String, Boolean> cache = Caffeine.newBuilder()
            .expireAfterAccess(5, TimeUnit.MINUTES)
            .maximumSize(50000)
            .build();

    @Override
    public boolean isOpenNewCrm(int enterpriseId) {
        Boolean ret = cache.get(String.valueOf(enterpriseId), key -> {
            GetConfigDto.Argument argument = createGetConfigArgument(enterpriseId);
            try {
                GetConfigDto.Result result = enterpriseConfigService.getConfig(argument);
                log.info("isOpenNewCrmSwitch from EnterpriseConfig rpc,enterpriseId:{},result:{}", enterpriseId, result);
                return Objects.isNull(result) || !NEW_CRM_CLOSE_STATUS.equals(result.getValue());
            } catch (Exception e) {
                log.error("isOpenNewCrmSwitch from EnterpriseConfig error,enterpriseId:{}", enterpriseId, e);
                throw e;
            }
        });
        return Boolean.TRUE.equals(ret);
    }

    @Override
    public void openNewCrm(int enterpriseId) {
        setNewCrmStatus(enterpriseId, NEW_CRM_OPEN_STATUS);
    }

    @Override
    public void closeNewCrm(int enterpriseId) {
        setNewCrmStatus(enterpriseId, NEW_CRM_CLOSE_STATUS);
    }

    private GetConfigDto.Argument createGetConfigArgument(int enterpriseId) {
        GetConfigDto.Argument argument = new GetConfigDto.Argument();
        argument.setCurrentEmployeeId(SYSTEM_USER_ID);
        argument.setEmployeeId(SYSTEM_USER_ID);
        argument.setEnterpriseId(enterpriseId);
        argument.setKey(GO_NEW_CRM_KEY);
        return argument;
    }

    private void setNewCrmStatus(int enterpriseId, String status) {
        SetConfigDto.Argument argument = new SetConfigDto.Argument();
        argument.setCurrentEmployeeId(SYSTEM_USER_ID);
        argument.setEmployeeId(SYSTEM_USER_ID);
        argument.setEnterpriseId(enterpriseId);
        argument.setKey(GO_NEW_CRM_KEY);
        argument.setValue(status);
        enterpriseConfigService.setConfig(argument);
    }
}
