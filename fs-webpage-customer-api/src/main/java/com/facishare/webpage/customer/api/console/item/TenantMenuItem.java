package com.facishare.webpage.customer.api.console.item;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TenantMenuItem implements Serializable {
        private String id;

        private Integer tenantId;

        private int appType;

        private String appId;

        private String appTemplateId;

        private List<MenuDataItem> menuDataEntities;

        private List<String> scopes;

        private String name;

        private String description;

        private Integer creatorId;

        private Long createTime;

        private Integer updaterId;

        private Long updateTime;

        private Boolean isChange;

        /**
         * -1: 临时状态
         * 0: 禁用
         * 1：启用
         * 2: 删除
         */
        private Integer status;

        private String sourceType;

        private String sourceId;
}
