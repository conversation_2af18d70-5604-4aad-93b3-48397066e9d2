package com.facishare.webpage.customer.processor.siteDiffProcessor;

import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.dao.entity.SiteEntity;
import com.facishare.webpage.customer.processor.siteDiffProcessor.result.EntityDiffResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractEntityDiffProcessor<T> {

    public final EntityDiffResult<T> processDiff(User user, SiteEntity siteEntity,
                                                 List<T> newEntities, String clientType, String diffType, Boolean isPublish) {
        try {
            // 1. 查询旧数据
            List<T> oldEntities = queryOldEntities(user, siteEntity, clientType, diffType, isPublish);

            // 2. 计算diff
            EntityDiffResult<T> diffResult = calculateDiff(newEntities, oldEntities);

            // 3. 验证新增数据
            validateNewEntities(user, diffResult.getToCreate());

            return diffResult;

        } catch (Exception e) {
            log.error("processDiff error! type:{}", getEntityType(), e);
            throw e;
        }
    }

    private EntityDiffResult<T> calculateDiff(List<T> newEntities, List<T> oldEntities) {
        List<T> toCreate;
        List<String> toDelete = Lists.newArrayList();
        List<T> toUpdate = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(oldEntities)) {
            Map<String, String> oldApiName2IdMap = oldEntities.stream()
                    .collect(Collectors.toMap(this::getEntityApiName, this::getEntityId));

            Set<String> newApiNameSet = newEntities.stream()
                    .map(this::getEntityApiName).collect(Collectors.toSet());

            // 计算要删除的
            toDelete = oldApiName2IdMap.keySet().stream()
                    .filter(apiName -> !newApiNameSet.contains(apiName))
                    .map(oldApiName2IdMap::get)
                    .collect(Collectors.toList());

            // 计算要更新的
            toUpdate = newEntities.stream()
                    .filter(entity -> oldApiName2IdMap.containsKey(getEntityApiName(entity)))
                    .peek(entity -> setEntityId(entity, oldApiName2IdMap.get(getEntityApiName(entity))))
                    .collect(Collectors.toList());

            // 计算要新增的
            toCreate = newEntities.stream()
                    .filter(entity -> !oldApiName2IdMap.containsKey(getEntityApiName(entity)))
                    .collect(Collectors.toList());
        } else {
            toCreate = newEntities;
        }

        return EntityDiffResult.<T>builder()
                .toCreate(toCreate)
                .toUpdate(toUpdate)
                .toDelete(toDelete)
                .build();
    }


    protected abstract List<T> queryOldEntities(User user, SiteEntity siteEntity, String clientType, String diffType, Boolean isPublish);

    protected abstract String getEntityApiName(T entity);

    protected abstract String getEntityId(T entity);

    protected abstract void setEntityId(T entity, String id);

    protected abstract void validateNewEntities(User user, List<T> newEntities);

    protected abstract String getEntityType();


}
