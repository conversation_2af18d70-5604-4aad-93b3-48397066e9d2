package com.facishare.webpage.customer.api.service;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;

@RestResource(
        value = "WebPageResource",
        desc = "应用页面服务",//ignoreI18n
        contentType = "application/json"
)
public interface TenantMenuRestService {
    @POST(value = "/webPage/tenantMenuRestService/getMenuById",desc = "获取菜单模板详情" )//ignoreI18n
    GetMenuByIdRestResult getMenuById(@HeaderParam("x-fs-ei") String tenantId,@Body GetMenuByIdRestArg arg);

    @POST(value = "/webPage/tenantMenuRestService/getCrossMenuGroupById",desc = "获取互联应用菜单分组" )//ignoreI18n
    GetMenuByIdRestResult getCrossMenuGroupById(@HeaderParam("x-fs-ei") String tenantId, @Body GetMenuByIdRestArg arg);

    @POST(value = "/webPage/tenantMenuRestService/createOrUpdateMenu",desc = "新增/编辑菜单模板" )//ignoreI18n
    CreateMenuRestResult createOrUpdateMenu(@HeaderParam("x-fs-ei") String tenantId,@Body CreateMenuRestArg arg);

    @POST(value = "/webPage/tenantMenuRestService/getUserMenuById",desc = "获取菜单模板详情" )//ignoreI18n
    GetUserMenuByIdRestResult getUserMenuById(@HeaderParam("x-fs-ei") String tenantId,@Body GetUserMenuByIdRestArg arg);
    //针对老的预置模板新加了菜单配置这种情况，给历史使用这个模板且编辑过的的租户添加菜单数据
    @POST(value = "/webPage/tenantMenuRestService/addPresetWebMenuForOldTenant",desc = "获取菜单模板详情" )//ignoreI18n
    void addPresetWebMenuForOldTenant(@HeaderParam("x-fs-ei") String tenantId,@Body AddPresetWebMenuForOldTenantArg arg);

    @POST(value = "/webPage/tenantMenuRestService/queryCommonlyUsedMenu",desc = "查询老版crm常用菜单" )//ignoreI18n
    CommonlyUsedMenuResult queryCommonlyUsedMenu(@HeaderParam("x-fs-ei") String tenantId, @Body QueryCommonlyUsedMenuRestArg arg);

    @POST(value = "/webPage/tenantMenuRestService/queryLicense",desc = "查询老版crm常用菜单" )//ignoreI18n
    QueryLicenseRestResult queryLicense(@HeaderParam("x-fs-ei") String tenantId, @Body QueryLicenseRestArg arg);

    @POST(value = "/webPage/tenantMenuRestService/getAllCrmMenuList",desc = "查询老版crm菜单列表" )//ignoreI18n
    MenuListRestResult getAllCrmMenuList(@HeaderParam("x-fs-ei") String tenantId, @Body MenuListRestArg arg);

    @POST(value = "/webPage/tenantMenuRestService/getOldCrmMenuNames",desc = "查询老版crm菜单列表名称, 仅用于翻译工作台" )//ignoreI18n
    CrmMenuName.Result getAllCrmMenuList(@HeaderParam("x-fs-ei") String tenantId, @Body CrmMenuName.Arg arg);
    }
