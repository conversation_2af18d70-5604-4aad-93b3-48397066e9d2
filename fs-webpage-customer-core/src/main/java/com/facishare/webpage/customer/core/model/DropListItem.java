package com.facishare.webpage.customer.core.model;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by zhangyu on 2020/6/16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DropListItem implements Serializable {

    /**
     * 菜单名称
     */
    private String name;
    /**
     * 菜单Id
     */
    private String id;
    /**
     * pid
     */
    private String parentId;
    /**
     * 菜单类型："group"：菜单；"component"：组件
     */
    private String type;
    /**
     * 菜单icon
     */
    private String icon;
    /**
     * 菜单FxIcon
     */
    private String fxIcon;
    /**
     * 菜单分组："menuGroup"
     */
    private String groupType;
    /**
     * 组件信息
     */
    private JSONObject component;
}
