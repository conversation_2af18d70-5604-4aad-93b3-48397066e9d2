package com.facishare.webpage.customer.api.model;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2020/11/12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdatePaasAppScopeListVO implements Serializable {

    private Integer tenantId;
    private String appId;
    private List<String> scopeList;
    private List<ScopeForCross> scopeListForCross;
}
