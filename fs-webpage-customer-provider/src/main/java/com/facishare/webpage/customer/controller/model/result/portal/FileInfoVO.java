package com.facishare.webpage.customer.controller.model.result.portal;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 文件信息VO，用于返回站点页面中引用的文件资源
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileInfoVO {

    /**
     * 文件apiName
     */
    private String apiName;

    /**
     * 引用者ID，如组件ID
     */
    private String sourceId;

    /**
     * 引用者类型，如"component"
     */
    private String sourceType;

    /**
     * 上级apiName，如引用者的父级组件ID
     */
    private String superApiName;

    /**
     * 所属工作区apiName
     */
    private String workSpaceApiName;

    /**
     * 访问URL
     */
    private String url;
}
