package com.facishare.webpage.customer.api.exception;

import com.facishare.webpage.customer.api.constant.TranslateI18nUtils;
import com.facishare.webpage.customer.api.constant.WebPageErrorCode;

/**
 * Created by zhouwr on 2024/12/30.
 */
public class ValidateException extends WebPageException {
    public ValidateException(String failureMessage) {
        super(failureMessage, WebPageErrorCode.VALIDATION_ERROR.getCode());
    }

    public ValidateException(String failureMessage, Integer errorCode) {
        super(failureMessage, errorCode);
    }


    public static ValidateException fromI18N(String messageI18NKey) {
        String message = TranslateI18nUtils.text(messageI18NKey);
        return new ValidateException(message);
    }

    public static ValidateException fromI18N(String messageI18NKey, Object... placeHolder) {
        String message = TranslateI18nUtils.text(messageI18NKey, placeHolder);
        return new ValidateException(message);
    }

    public static ValidateException fromI18N(String messageI18NKey, Integer errorCode) {
        String message = TranslateI18nUtils.text(messageI18NKey);
        return new ValidateException(message, errorCode);
    }
}
