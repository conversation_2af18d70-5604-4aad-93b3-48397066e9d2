package com.facishare.webpage.customer.brush.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/23 2:59 下午
 */
public interface PaasPageBrush {

    @Data
    class Arg implements Serializable {
        private List<Integer> tenantIds;
    }

    @Data
    class Result implements Serializable {
        private List<PaasPageBrush.ResultData> resultDataList;
    }

    @Data
    class ResultData implements Serializable {
        private Integer tenantId;
        private Integer count;
    }

}
