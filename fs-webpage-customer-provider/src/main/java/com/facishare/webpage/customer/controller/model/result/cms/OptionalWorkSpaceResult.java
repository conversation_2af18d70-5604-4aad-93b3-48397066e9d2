package com.facishare.webpage.customer.controller.model.result.cms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 可选工作区列表结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OptionalWorkSpaceResult {

    private List<OptionalItem> linkApp;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptionalItem {
        String apiName;
        String name;
    }
}
