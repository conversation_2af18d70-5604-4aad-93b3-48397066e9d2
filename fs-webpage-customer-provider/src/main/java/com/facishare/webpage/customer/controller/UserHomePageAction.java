package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.homepage.*;
import com.facishare.webpage.customer.controller.model.result.homepage.*;

/**
 * Created by <PERSON>hangyu on 2019/9/10
 */
public interface UserHomePageAction {

    /**
     * 根据id获取首页（互联场景使用）
     *
     * @param userInfo
     * @param outerUserInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetUserHomePageLayoutResult getUserHomePageLayoutByLayoutId(UserInfo userInfo, OuterUserInfo outerUserInfo, ClientInfo clientInfo, GetUserHomePageLayoutArg arg);

    /**
     * 渠道门户使用
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    @Deprecated
    GetVendorUserHomePageResult getVendorUserHomePageById(UserInfo userInfo, ClientInfo clientInfo, GetVendorUserHomePageArg arg);

    /**
     * 企业内根据id获取首页
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetInnerUserHomePageLayoutResult getInnerUserHomePageLayoutById(UserInfo userInfo, ClientInfo clientInfo, GetInnerUserHomePageLayoutArg arg);

    /**
     * 根据apiName获取首页数据
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetHomePageByApiNameResult getHomePageByApiName(UserInfo userInfo, ClientInfo clientInfo, GetHomePageByApiNameArg arg);

    /**
     * 获取新版布局数据
     *
     * @param userInfo
     * @param outerUserInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetUserCustomerLayoutResult getUserCustomerLayout(UserInfo userInfo,
                                                      OuterUserInfo outerUserInfo,
                                                      ClientInfo clientInfo,
                                                      GetUserCustomerLayoutArg arg);

    /**
     * 个人级页面列表
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetUserHomePageLayoutListResult getUserHomePageLayoutList(UserInfo userInfo, ClientInfo clientInfo, GetUserHomePageLayoutListArg arg);

    /**
     * 设置当前默认首页
     *
     * @param userInfo
     * @param arg
     * @return
     */
    SetPaaSCurrentHomePageResult setPaaSCurrentHomePage(UserInfo userInfo, SetPaaSCurrentHomePageArg arg);

    /**
     * save user homePage
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    SaveUserHomePageResult saveUserHomePage(UserInfo userInfo, ClientInfo clientInfo, SaveUserHomePageArg arg);
}
