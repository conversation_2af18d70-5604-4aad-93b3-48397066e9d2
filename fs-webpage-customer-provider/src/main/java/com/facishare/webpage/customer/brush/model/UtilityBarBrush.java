package com.facishare.webpage.customer.brush.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface UtilityBarBrush {

    @Data
    class Arg implements Serializable {
        private String appId;
        private List<Integer> tenantIds;
    }

    @Data
    class Result implements Serializable {
        private List<ResultData> resultDataList;
    }

    @Data
    class ResultData implements Serializable {
        private Integer tenantId;
        private Integer count;
    }

}
