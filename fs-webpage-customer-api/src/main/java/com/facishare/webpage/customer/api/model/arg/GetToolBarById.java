package com.facishare.webpage.customer.api.model.arg;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;

import java.util.List;

public interface GetToolBarById {


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private String pageTemplateId;
        private String appId;
        private Integer tenantId;
        private int bizType;
        private Integer employeeId;
        private String lang;

        public void valid() {
            if (StringUtils.isAnyEmpty(appId, pageTemplateId)) {
                throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
            }
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        @Builder.Default
        List<ToolBarComponent> components = Lists.newArrayList();
        String id;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ToolBarComponent {
        String newHeader;
        String header;
        String apiName;
        String key;
        private List<String> preKeyList;
    }
}
