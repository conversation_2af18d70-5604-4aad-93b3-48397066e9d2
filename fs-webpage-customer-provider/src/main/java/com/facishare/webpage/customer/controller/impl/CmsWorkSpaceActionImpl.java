package com.facishare.webpage.customer.controller.impl;

import com.facishare.cep.plugin.annotation.FSClientInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.controller.CmsWorkSpaceAction;
import com.facishare.webpage.customer.controller.model.arg.cms.*;
import com.facishare.webpage.customer.controller.model.result.cms.OperationResult;
import com.facishare.webpage.customer.controller.model.result.cms.OptionalWorkSpaceResult;
import com.facishare.webpage.customer.controller.model.result.cms.RelatedWorkSpaceResult;
import com.facishare.webpage.customer.controller.model.result.cms.WorkSpaceInfoResult;
import com.facishare.webpage.customer.controller.model.result.cms.WorkSpaceListResult;
import com.facishare.webpage.customer.controller.model.vo.cms.WorkSpaceVO;
import com.facishare.webpage.customer.service.CmsWorkSpaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * CMS工作区管理接口实现
 */
@Controller
@Slf4j
@RequestMapping("/cms/workSpace")
public class CmsWorkSpaceActionImpl implements CmsWorkSpaceAction {

    @Autowired
    private CmsWorkSpaceService cmsWorkSpaceService;

    @RequestMapping(value = "list", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public WorkSpaceListResult list(@FSUserInfo UserInfo userInfo,
                                    @FSClientInfo ClientInfo clientInfo,
                                    @RequestBody WorkSpaceListArg arg) {
        User user = RequestContextManager.getUser();
        return cmsWorkSpaceService.findWorkSpaceList(user, arg);
    }

    @RequestMapping(value = "findInfoByApiName", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public WorkSpaceInfoResult findInfoByApiName(@FSUserInfo UserInfo userInfo,
                                                 @FSClientInfo ClientInfo clientInfo,
                                                 @RequestBody FindWorkSpaceInfoArg arg) {
        WorkSpaceVO workspaceInfo = cmsWorkSpaceService.findWorkSpaceInfoByApiName(RequestContextManager.getUser(), arg.getApiName());
        return WorkSpaceInfoResult.builder().workSpaceInfo(workspaceInfo).build();
    }

    @RequestMapping(value = "findRelatedWorkSpace", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public RelatedWorkSpaceResult findRelatedWorkSpace(@FSUserInfo UserInfo userInfo,
                                                       @FSClientInfo ClientInfo clientInfo,
                                                       @RequestBody FindRelatedWorkSpaceArg arg) {
        User user = RequestContextManager.getUser();
        return cmsWorkSpaceService.findRelatedWorkSpace(user, arg);
    }

    @RequestMapping(value = "optionalItemList", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public OptionalWorkSpaceResult optionalItemList(@FSUserInfo UserInfo userInfo,
                                                    @FSClientInfo ClientInfo clientInfo,
                                                    @RequestBody OptionalWorkSpaceArg arg) {
        User user = RequestContextManager.getUser();
        return cmsWorkSpaceService.findOptionalWorkSpaceList(user, arg);
    }

    @RequestMapping(value = "create", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public OperationResult create(@FSUserInfo UserInfo userInfo,
                                  @FSClientInfo ClientInfo clientInfo,
                                  @RequestBody CreateWorkSpaceArg arg) {
        User user = RequestContextManager.getUser();
        cmsWorkSpaceService.createWorkSpace(user, arg);
        return OperationResult.success();
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public OperationResult update(@FSUserInfo UserInfo userInfo,
                                  @FSClientInfo ClientInfo clientInfo,
                                  @RequestBody UpdateWorkSpaceArg arg) {
        User user = RequestContextManager.getUser();
        cmsWorkSpaceService.updateWorkSpace(user, arg);
        return OperationResult.success();
    }

    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public OperationResult delete(@FSUserInfo UserInfo userInfo,
                                  @FSClientInfo ClientInfo clientInfo,
                                  @RequestBody DeleteWorkSpaceArg arg) {
        User user = RequestContextManager.getUser();

        cmsWorkSpaceService.deleteWorkSpace(user, arg.getApiName());
        return OperationResult.success();
    }

    @RequestMapping(value = "enable", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public OperationResult enable(@FSUserInfo UserInfo userInfo,
                                  @FSClientInfo ClientInfo clientInfo,
                                  @RequestBody EnableWorkSpaceArg arg) {
        User user = RequestContextManager.getUser();
        cmsWorkSpaceService.enableWorkSpace(user, arg.getApiName());
        return OperationResult.success();
    }

    @RequestMapping(value = "disable", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public OperationResult disable(@FSUserInfo UserInfo userInfo,
                                   @FSClientInfo ClientInfo clientInfo,
                                   @RequestBody DisableWorkSpaceArg arg) {
        User user = RequestContextManager.getUser();
        cmsWorkSpaceService.disableWorkSpace(user, arg.getApiName());
        return OperationResult.success();
    }
}
