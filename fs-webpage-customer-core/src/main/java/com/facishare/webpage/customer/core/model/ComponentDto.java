package com.facishare.webpage.customer.core.model;

import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.api.model.core.TenantPrivilege;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 19/12/11.
 */
@Data
public class ComponentDto {

    private String id;

    private String parentId;

    private int componentType;

    private String title;

    private String titleI18nKey;

    private TenantPrivilege tenantPrivilege;    // type=4(widget)是定义在应用drop里面的权限 type=3(collection)是定义在connection里面的权限

    private int menuSourceType;

    private List<Menu> menus;

    private int widgetSourceType;   //1、表示自定义组件

    private Widget widget;
    /**
     * @see com.facishare.webpage.customer.core.model.MenuCollectionType
     */
    private String type;
}
