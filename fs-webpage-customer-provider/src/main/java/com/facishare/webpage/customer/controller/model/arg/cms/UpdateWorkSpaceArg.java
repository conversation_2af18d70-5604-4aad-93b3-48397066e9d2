package com.facishare.webpage.customer.controller.model.arg.cms;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 更新工作区参数
 */
@Data
public class UpdateWorkSpaceArg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工作区ApiName
     */
    private String apiName;

    /**
     * 工作区名称
     */
    private String name;

    /**
     * 工作区描述
     */
    private String description;

    /**
     * 状态
     */
    private int status;

    /**
     * 关联的频道列表
     */
    private List<String> channelList;
}
