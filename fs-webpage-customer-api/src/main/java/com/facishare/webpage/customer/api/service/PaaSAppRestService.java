package com.facishare.webpage.customer.api.service;

import com.facishare.rest.core.annotation.*;
import com.facishare.webpage.customer.api.model.PaaSAppEntityVO;
import com.facishare.webpage.customer.api.model.UpdatePaasAppScopeListVO;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;

import java.util.Map;

@RestResource(
        value = "WebPageResource",
        desc = "应用页面服务",//ignoreI18n
        contentType = "application/json"
)
public interface PaaSAppRestService {
    @POST(value = "/webPage/PaaSAppRestService/getPaaSAppInfo",desc = "获取企业内应用页面模板id" )//ignoreI18n
    GetPaaSAppInfoResult getPaaSAppInfo(@HeaderMap Map<String, String> headers, @Body GetPaaSAppInfoArg arg);

    @POST(value = "/webPage/PaaSAppRestService/getAppList",desc = "获取PaaSApp列表" )//ignoreI18n
    QueryPaasAppResult getAppList(@HeaderParam("x-fs-ei") String tenantId, @Body QueryPaasAppArg arg);

    @POST(value = "/webPage/PaaSAppRestService/getUserPaaSAppList",desc = "获取用户PaaSApp列表" )//ignoreI18n
    QueryPaasAppResult getUserPaaSAppList(@HeaderParam("x-fs-ei") String tenantId, @Body QueryPaasAppArg arg);

    @POST(value = "/webPage/PaaSAppRestService/saveTenantPaaSAppInfo",desc = "保存预设应用相关信息")//ignoreI18n
    SavePaasAppResult saveTenantPaaSAppInfo(@HeaderParam("x-fs-ei") String tenantId, @Body SavePaasAppArg arg);

    @POST(value = "/webPage/PaaSAppRestService/getUserPerAppList",desc = "获取用户预制应用appIdList")//ignoreI18n
    GetUserPerAppResult getUserPerAppList(@HeaderParam("x-fs-ei") String tenantId, @Body GetUserPerAppArg arg);

    @POST(value = "/webPage/PaaSAppRestService/getTenantPerAppList",desc = "获取企业预制应用appIdList")//ignoreI18n
    GetTenantPerAppResult getTenantPerAppList(@HeaderParam("x-fs-ei") String tenantId, @Body GetTenantPerAppArg arg);

    @POST(value = "/webPage/PaaSAppRestService/clearTenantCache",desc = "清除企业缓存")//ignoreI18n
    ClearTenantCacheResult clearTenantCache(@HeaderParam("x-fs-ei") String tenantId, @Body ClearTenantCacheArg arg);

    @POST(value = "/webPage/PaaSAppRestService/getPaaSAppByAppIds",desc = "获取PaaSApp列表" )//ignoreI18n
    QueryPaasAppResult getPaaSAppByAppIds(@HeaderParam("x-fs-ei") String tenantId, @Body QueryPaasAppListByIdsArg arg);

    @POST(value = "/webPage/PaaSAppRestService/getTenantPaaSAppStatusByAppId",desc = "获取当前应用是否可见" )//ignoreI18n
    GetTenantAppStatusResult getTenantPaaSAppStatusByAppId(@HeaderParam("x-fs-ei") String tenantId, @Body GetTenantAppStatusArg arg);

    @POST(value = "/webPage/PaaSAppRestService/savePaasAppEntity",desc = "获取PaaSApp列表" )//ignoreI18n
    SavePaasAppEntityResult savePaasAppEntity(@HeaderParam("x-fs-ei") String tenantId, @Body PaaSAppEntityVO arg);

    @POST(value = "/webPage/PaaSAppRestService/updatePaasAppScopeList",desc = "获取PaaSApp列表" )//ignoreI18n
    UpdatePaaSAppScopeListResult updatePaasAppScopeList(@HeaderParam("x-fs-ei") String tenantId, @Body UpdatePaasAppScopeListVO arg);

    @POST(value = "/webPage/PaaSAppRestService/getAppListForTransLate",desc = "获取PaaSApp列表" )// // ignoreI18n
    QueryPaasAppResult getAppListForTransLate(@HeaderParam("x-fs-ei") String tenantId, @Body QueryPaasAppArg arg);
    }
