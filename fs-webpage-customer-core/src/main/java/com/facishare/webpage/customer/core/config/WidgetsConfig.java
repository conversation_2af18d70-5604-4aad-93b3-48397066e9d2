package com.facishare.webpage.customer.core.config;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.core.model.Widget;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by she<PERSON> on 19/12/11.
 */
@Slf4j
public class WidgetsConfig {

    private Map<String, Widget> widgets;
    private Map<String, Widget> cardIdWidgets;

    public void init() {
        ConfigFactory.getInstance().getConfig("fs-webpage-customer-widget-all", iConfig -> loadAllWidgetConfig(iConfig));
    }

    public void loadAllWidgetConfig(IConfig iConfig) {
        if (Strings.isNullOrEmpty(iConfig.getString())) {
            return;
        }
        Map<String, String> configMap = JSONObject.parseObject(iConfig.getString(), Map.class);
        Map<String, Widget> allWidgets = new HashMap<>();
        Map<String, Widget> allCardIdWidgets = new HashMap<>();
        configMap.entrySet().forEach(entry -> {
            ConfigFactory.getInstance().getConfig(entry.getValue(), iConfig2 -> {
                if (!Strings.isNullOrEmpty(iConfig2.getString())) {
                    List<Widget> widgets = JSONObject.parseArray(iConfig2.getString(), Widget.class);
                    widgets.stream().forEach(widget -> {
                        allWidgets.put(widget.getId(), widget);
                        allCardIdWidgets.put(widget.getCardId(), widget);
                    });
                }
            });
        });
        widgets = allWidgets;
        cardIdWidgets = allCardIdWidgets;
    }

    public Widget getWidget(String id) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(objectMapper.writeValueAsString(widgets.get(id)), Widget.class);
        } catch (Exception e) {
            log.error("getWidget error by id:{}", id, e);
            return null;
        }
    }

    public List<Widget> getWidgetsByIds(List<String> widgetIds) {
        return widgetIds.stream().map(x -> {
            Widget widget = widgets.get(x);
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                return objectMapper.readValue(objectMapper.writeValueAsString(widget), Widget.class);
            } catch (Exception e) {
                log.error("getWidgetsByIds error by x:{}", x, e);
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public List<Widget> getWidgetsByCardIds(List<String> cardIds) {
        return cardIds.stream().map(x -> {
            Widget widget = cardIdWidgets.get(x);
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                return objectMapper.readValue(objectMapper.writeValueAsString(widget), Widget.class);
            } catch (Exception e) {
                log.error("getWidgetsByCardIds error by x:{}", x, e);
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public Widget getWidgetByCardIdOrId(String id, String cardId) {
        if (StringUtils.isNotEmpty(id)) {
            Widget widget = getWidget(id);
            if (widget != null) {
                return widget;
            }
        }
        if (StringUtils.isEmpty(cardId)){
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(objectMapper.writeValueAsString(cardIdWidgets.get(cardId)), Widget.class);
        } catch (Exception e) {
            log.error("getWidget error by cardId:{}", cardId, e);
            return null;
        }
    }

    public List<Widget> getAllWidgetsNoCopy() {
        return Lists.newArrayList(widgets.values());
    }

}
