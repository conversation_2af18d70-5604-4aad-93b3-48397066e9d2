<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="webPageRestServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-qixin-objgroup-rest-proxy-config" init-method="init"/>

    <bean id="biReportResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.core.resource.BiReportResource">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

</beans>
