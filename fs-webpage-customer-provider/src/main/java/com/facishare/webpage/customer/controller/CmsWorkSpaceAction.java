package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.cms.*;
import com.facishare.webpage.customer.controller.model.result.cms.OperationResult;
import com.facishare.webpage.customer.controller.model.result.cms.OptionalWorkSpaceResult;
import com.facishare.webpage.customer.controller.model.result.cms.RelatedWorkSpaceResult;
import com.facishare.webpage.customer.controller.model.result.cms.WorkSpaceInfoResult;
import com.facishare.webpage.customer.controller.model.result.cms.WorkSpaceListResult;
import com.facishare.webpage.customer.controller.model.vo.cms.WorkSpaceVO;

import java.util.List;

/**
 * CMS工作区管理接口 Created by cursor.
 */
public interface CmsWorkSpaceAction {

    /**
     * 工作区查询列表接口
     */
    WorkSpaceListResult list(UserInfo userInfo, ClientInfo clientInfo, WorkSpaceListArg arg);

    /**
     * 工作区详细信息接口
     */
    WorkSpaceInfoResult findInfoByApiName(UserInfo userInfo, ClientInfo clientInfo, FindWorkSpaceInfoArg arg);

    /**
     * 查询关联工作区接口
     */
    RelatedWorkSpaceResult findRelatedWorkSpace(UserInfo userInfo, ClientInfo clientInfo, FindRelatedWorkSpaceArg arg);

    /**
     * 查询可选工作区列表接口
     */
    OptionalWorkSpaceResult optionalItemList(UserInfo userInfo, ClientInfo clientInfo, OptionalWorkSpaceArg arg);

    /**
     * 创建工作区接口
     */
    OperationResult create(UserInfo userInfo, ClientInfo clientInfo, CreateWorkSpaceArg arg);

    /**
     * 编辑工作区接口
     */
    OperationResult update(UserInfo userInfo, ClientInfo clientInfo, UpdateWorkSpaceArg arg);

    /**
     * 删除工作区接口
     */
    OperationResult delete(UserInfo userInfo, ClientInfo clientInfo, DeleteWorkSpaceArg arg);

    /**
     * 启用工作区接口
     */
    OperationResult enable(UserInfo userInfo, ClientInfo clientInfo, EnableWorkSpaceArg arg);

    /**
     * 禁用工作区接口
     */
    OperationResult disable(UserInfo userInfo, ClientInfo clientInfo, DisableWorkSpaceArg arg);
}
