package com.facishare.webpage.customer.api.model;

import com.fxiaoke.paasauthrestapi.common.data.PageData;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON>hangyu on 2019/9/19
 */
@Data
public class AppPageTemplateVO implements Serializable {

    private String pageTemplateId;
    private Integer enterpriseId;

    /**
     * 页面模板名称
     */
    private String name;

    /**
     * 普通图标(自定义页面,下拉框 使用)
     */
    private String icon;
    /**
     * 未选中图标
     */
    private String background;
    /**
     * 选中图标
     */
    private String selectIcon;
    /**
     * 数据可操作权限
     *
     * @see
     */
    private Integer operationAuth;

    private Integer createId;
    private Long createTime;
    private Integer updateEmployeeId;
    private Long updateTime;

    private String sourceType;

    private String sourceId;

    private int iconType;

    private String layout;
    /**
     * 布局的版本
     */
    private Integer layoutVersion;

    /**
     * 布局类型
     */
    private Integer layoutType;
    /**
     * 底导航数据存储
     */
    private String layoutExp;

    /**
     * 顶导航开启 1开启  0未开启
     */
    private int pageMultiType;

    /**
     * 默认标签页index
     */
    private int defaultLabelIndex;

    /**
     * 所有标签页数据信息，layoutList"，此项考虑和customerLayout冗余，customerLayout作为往前兼容的冗余数据
     */
    private List<String> layoutList;
    /**
     * 自定义页面备注
     */
    private String description;
    private String appId;


    /**
     * 布局样式  0方角   1圆角   默认0方角
     */
    private int layoutStyleType;

    private Boolean fromOldCrmHomePage = false;

}
