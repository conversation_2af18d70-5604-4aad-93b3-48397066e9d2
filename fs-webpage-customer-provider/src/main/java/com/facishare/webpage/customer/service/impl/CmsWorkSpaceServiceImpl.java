package com.facishare.webpage.customer.service.impl;

import com.facishare.paas.I18N;
import com.facishare.webpage.customer.api.constant.CmsStatus;
import com.facishare.webpage.customer.api.constant.ErrorMessageI18NKey;
import com.facishare.webpage.customer.api.exception.ValidateException;
import com.facishare.webpage.customer.api.model.PaaSAppVO;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.api.model.arg.GetlinkAppListArg;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.controller.model.arg.cms.*;
import com.facishare.webpage.customer.controller.model.result.cms.OptionalWorkSpaceResult;
import com.facishare.webpage.customer.controller.model.result.cms.RelatedWorkSpaceResult;
import com.facishare.webpage.customer.controller.model.result.cms.WorkSpaceListResult;
import com.facishare.webpage.customer.controller.model.vo.cms.WorkSpaceVO;
import com.facishare.webpage.customer.dao.FileEntityDao;
import com.facishare.webpage.customer.dao.ReferenceEntityDao;
import com.facishare.webpage.customer.dao.WorkSpaceEntityDao;
import com.facishare.webpage.customer.dao.entity.FileEntity;
import com.facishare.webpage.customer.dao.entity.WorkSpaceEntity;
import com.facishare.webpage.customer.service.CmsWorkSpaceService;
import com.facishare.webpage.customer.service.PaaSAppService;
import com.facishare.webpage.customer.util.ReferenceTargetType;
import com.facishare.webpage.customer.util.SearchValidationUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * CMS工作区管理服务实现类
 */
@Service
@Slf4j
public class CmsWorkSpaceServiceImpl implements CmsWorkSpaceService {

    @Resource
    private WorkSpaceEntityDao workSpaceEntityDao;
    @Resource
    private FileEntityDao fileEntityDao;
    @Resource
    private ReferenceEntityDao referenceEntityDao;
    @Autowired
    private PaaSAppService paaSAppService;

    @Override
    public WorkSpaceListResult findWorkSpaceList(User user, WorkSpaceListArg arg) {
        // 验证搜索关键字，防止正则表达式特殊字符
        SearchValidationUtil.validateSearchKeyword(arg.getName());

        // 构建查询条件
        WorkSpaceEntity condition = new WorkSpaceEntity();
        condition.setTenantId(user.getTenantId());

        if (StringUtils.isNotBlank(arg.getName())) {
            condition.setName(arg.getName());
        }

        if (arg.getStatus() != null) {
            condition.setStatus(arg.getStatus());
        }

        // 分页查询
        int offset = (arg.getPageNum() - 1) * arg.getPageSize();

        long count = workSpaceEntityDao.getCountByCondition(condition);

        if (count == 0) {
            return WorkSpaceListResult.builder().build();
        }
        List<WorkSpaceEntity> workSpaceEntities = workSpaceEntityDao.findByCondition(condition, offset, arg.getPageSize());

        if (CollectionUtils.isEmpty(workSpaceEntities)) {
            return WorkSpaceListResult.builder().build();
        }
        // 转换为VO
        return WorkSpaceListResult.builder()
                .workspaceList(workSpaceEntities.stream()
                        .map(this::convertToVO)
                        .collect(Collectors.toList()))
                .pageNum(arg.getPageNum())
                .pageSize(arg.getPageSize())
                .totalNum(count)
                .build();
    }

    @Override
    public WorkSpaceVO findWorkSpaceInfoByApiName(User user, String apiName) {
        // 根据ApiName查询工作区详情
        WorkSpaceEntity workSpaceEntity = workSpaceEntityDao.findByApiName(user.getTenantId(), apiName);
        if (workSpaceEntity == null) {
            log.warn("Workspace not found, apiName: {}", apiName);
            return null;
        }

        return convertToVO(workSpaceEntity);
    }

    @Override
    public RelatedWorkSpaceResult findRelatedWorkSpace(User user, FindRelatedWorkSpaceArg arg) {
        // 验证搜索关键字，防止正则表达式特殊字符
        SearchValidationUtil.validateSearchKeyword(arg.getName());

        WorkSpaceEntity condition = new WorkSpaceEntity();
        condition.setTenantId(user.getTenantId());
        condition.setChannelList(Lists.newArrayList(getChannelValue(arg.getType(), arg.getApiName())));
        condition.setName(arg.getName());
        if (Objects.nonNull(arg.getStatus())) {
            condition.setStatus(arg.getStatus());
        }

        long count = workSpaceEntityDao.getCountByCondition(condition);
        if (count == 0) {
            return RelatedWorkSpaceResult.builder().build();
        }
        // 分页查询
        int offset = (arg.getPageNum() - 1) * arg.getPageSize();
        // 查询已关联的工作区
        List<WorkSpaceEntity> relatedWorkSpaces = workSpaceEntityDao.findByCondition(condition, offset, arg.getPageSize());

        if (CollectionUtils.isEmpty(relatedWorkSpaces)) {
            return RelatedWorkSpaceResult.builder().build();
        }

        List<WorkSpaceVO> workSpaceVOList = relatedWorkSpaces.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        return RelatedWorkSpaceResult.builder().totalNum(count).pageNum(arg.getPageNum())
                .pageSize(arg.getPageSize()).workspaceList(workSpaceVOList).build();
    }

    private String getChannelValue(String type, String apiName) {
        return type + "/" + apiName;
    }


    /**
     * 960只支持互联应用，其余类型以后需要时在扩展，如果多种类型的话，建议起线程并行查询
     *
     * @param arg 查询参数
     * @return
     */
    @Override
    public OptionalWorkSpaceResult findOptionalWorkSpaceList(User user, OptionalWorkSpaceArg arg) {
        log.info("Query optional workspace list, arg: {}", arg);
        GetlinkAppListArg getlinkAppListArg = new GetlinkAppListArg();
        getlinkAppListArg.setTenantId(user.getTenantId());

        List<PaaSAppVO> linkAppVOList = paaSAppService.getLinkAppVOList(getlinkAppListArg, Locale.forLanguageTag(I18N.getContext().getLanguage()));
        return OptionalWorkSpaceResult.builder()
                .linkApp(convertToOptionalItem(linkAppVOList))
                .build();
    }

    private List<OptionalWorkSpaceResult.OptionalItem> convertToOptionalItem(List<PaaSAppVO> appList) {
        return appList.stream().map(x -> OptionalWorkSpaceResult.OptionalItem.builder()
                .name(x.getName())
                .apiName(x.getAppId())
                .build()).collect(Collectors.toList());
    }

    @Override
    public void createWorkSpace(User user, CreateWorkSpaceArg arg) {
        log.info("Create workspace, arg: {}", arg);
        if (StringUtils.isEmpty(arg.getApiName()) || CollectionUtils.isEmpty(arg.getChannelList())) {
            throw ValidateException.fromI18N(ErrorMessageI18NKey.PARAMS_ERROR);
        }
        // 检查是否已存在同名工作区
        WorkSpaceEntity existingWorkSpace = workSpaceEntityDao.findByApiName(user.getTenantId(), arg.getApiName());
        if (existingWorkSpace != null) {
            log.warn("Workspace already exists, apiName: {}", arg.getApiName());
            throw ValidateException.fromI18N(ErrorMessageI18NKey.WORKSPACE_ALREADY_EXISTS);
        }

        // 创建工作区
        WorkSpaceEntity workSpaceEntity = new WorkSpaceEntity();
        workSpaceEntity.setName(arg.getName());
        workSpaceEntity.setApiName(arg.getApiName());
        workSpaceEntity.setDescription(arg.getDescription());
        workSpaceEntity.setStatus(arg.getStatus());
        workSpaceEntity.setChannelList(arg.getChannelList()); // 设置关联频道列表
        workSpaceEntity.setTenantId(user.getTenantId());
        workSpaceEntity.setCreatorId(String.valueOf(user.getUserId()));
        workSpaceEntity.setCreateTime(System.currentTimeMillis());
        workSpaceEntity.setUpdaterId(String.valueOf(user.getUserId()));
        workSpaceEntity.setUpdateTime(System.currentTimeMillis());

        workSpaceEntityDao.save(workSpaceEntity);
        log.info("Workspace created successfully, apiName: {}", arg.getApiName());
    }

    @Override
    public void updateWorkSpace(User user, UpdateWorkSpaceArg arg) {
        log.info("Update workspace, arg: {}", arg);
        // 检查工作区是否存在
        WorkSpaceEntity existingWorkSpace = workSpaceEntityDao.findByApiName(user.getTenantId(), arg.getApiName());
        if (existingWorkSpace == null) {
            log.warn("Workspace not found, apiName: {}", arg.getApiName());
            throw ValidateException.fromI18N(ErrorMessageI18NKey.WORKSPACE_NOT_EXISTS);
        }

        // 更新工作区
        existingWorkSpace.setName(arg.getName());
        existingWorkSpace.setStatus(arg.getStatus());
        existingWorkSpace.setDescription(arg.getDescription());
        existingWorkSpace.setChannelList(arg.getChannelList()); // 设置关联频道列表
        existingWorkSpace.setUpdaterId(String.valueOf(user.getUserId()));
        existingWorkSpace.setUpdateTime(System.currentTimeMillis());
        existingWorkSpace.setCreateTime(existingWorkSpace.getCreateTime());
        existingWorkSpace.setCreatorId(existingWorkSpace.getCreatorId());

        workSpaceEntityDao.update(existingWorkSpace);
        log.info("Workspace updated successfully, apiName: {}", arg.getApiName());
    }

    @Override
    public void deleteWorkSpace(User user, String apiName) {
        log.info("Delete workspace, apiName: {}", apiName);

        // 检查工作区是否存在
        WorkSpaceEntity existingWorkSpace = workSpaceEntityDao.findByApiName(user.getTenantId(), apiName);
        if (existingWorkSpace == null) {
            log.warn("Workspace not found, apiName: {}", apiName);
            throw ValidateException.fromI18N(ErrorMessageI18NKey.WORKSPACE_NOT_EXISTS);
        }

        if (Objects.equals(existingWorkSpace.getStatus(), CmsStatus.ENABLE)) {
            throw ValidateException.fromI18N(ErrorMessageI18NKey.WORKSPACE_NOT_DISABLE);
        }

        // 检查工作区是否有关联资源，如果有关联资源则不允许删除
        validateReference(user.getTenantId(), apiName);
        //先删文件在删工作区
        fileEntityDao.deleteByWorkSpace(user, apiName);
        // 删除工作区
        workSpaceEntityDao.delete(user, apiName);
        log.info("Workspace deleted successfully, apiName: {}", apiName);
    }

    private void validateReference(int tenantId, String apiName) {
        List<FileEntity> fileEntityList = fileEntityDao.findByWorkSpace(tenantId, apiName);
        if (CollectionUtils.isEmpty(fileEntityList)) {
            return;
        }
        Set<String> fileIds = fileEntityList.stream().map(FileEntity::getApiName).collect(Collectors.toSet());
        boolean hasRelatedResources = referenceEntityDao.hasRelatedResources(String.valueOf(tenantId), ReferenceTargetType.FILE,
                fileIds, null, null);
        if (hasRelatedResources) {
            log.warn("Workspace has related resources, cannot delete, apiName: {}", apiName);
            throw ValidateException.fromI18N(ErrorMessageI18NKey.WORKSPACE_HAS_RELATED_RESOURCES);
        }
    }

    @Override
    public void enableWorkSpace(User user, String apiName) {
        log.info("Enable workspace, apiName: {}", apiName);

        // 检查工作区是否存在
        WorkSpaceEntity existingWorkSpace = workSpaceEntityDao.findByApiName(user.getTenantId(), apiName);
        if (existingWorkSpace == null) {
            log.warn("workSpace is not exists, apiName: {}", apiName);
            throw ValidateException.fromI18N(ErrorMessageI18NKey.WORKSPACE_NOT_EXISTS);
        }

        // 如果已经是启用状态，则直接返回
        if (Objects.equals(existingWorkSpace.getStatus(), CmsStatus.ENABLE)) {
            log.info("Workspace is already enabled, apiName: {}", apiName);
            return;
        }

        // 更新状态为启用
        existingWorkSpace.setStatus(CmsStatus.ENABLE);
        existingWorkSpace.setUpdaterId(String.valueOf(user.getUserId()));
        existingWorkSpace.setUpdateTime(System.currentTimeMillis());

        workSpaceEntityDao.update(existingWorkSpace);
        log.info("Workspace enabled successfully, apiName: {}", apiName);
    }

    @Override
    public void disableWorkSpace(User user, String apiName) {
        log.info("Disable workspace, apiName: {}", apiName);
        // 检查工作区是否存在
        WorkSpaceEntity existingWorkSpace = workSpaceEntityDao.findByApiName(user.getTenantId(), apiName);
        if (existingWorkSpace == null) {
            log.warn("workSpace is not exists, apiName: {}", apiName);
            throw ValidateException.fromI18N(ErrorMessageI18NKey.WORKSPACE_NOT_EXISTS);
        }

        // 如果已经是禁用状态，则直接返回
        if (Objects.equals(existingWorkSpace.getStatus(), CmsStatus.DISABLE)) {
            log.info("Workspace is already disabled, apiName: {}", apiName);
            return;
        }

        // 更新状态为禁用
        existingWorkSpace.setStatus(CmsStatus.DISABLE);
        existingWorkSpace.setUpdaterId(String.valueOf(user.getUserId()));
        existingWorkSpace.setUpdateTime(System.currentTimeMillis());

        workSpaceEntityDao.update(existingWorkSpace);
        log.info("Workspace disabled successfully, apiName: {}", apiName);
    }

    /**
     * 将工作区实体转换为VO
     */
    private WorkSpaceVO convertToVO(WorkSpaceEntity entity) {
        if (entity == null) {
            return null;
        }

        WorkSpaceVO vo = new WorkSpaceVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
}
