package com.facishare.webpage.customer.api.model.result;

import com.facishare.webpage.customer.api.model.PageWidget;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/19 11:56 上午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryHomePageWidgetsResult implements Serializable {
    private List<PageWidget> homePageWidgets;



}
