<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
    <!-- ch.qos.logback.core.ConsoleAppender 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.facishare.webpage" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="com.github.trace" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="com.fxiaoke" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="com.github.autoconf" level="ERROR" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <root level="WARN">
        <!-- appender referenced after it is defined -->
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
