package com.facishare.webpage.customer.api.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2019/9/10
 */
@Data
public class HomePageLayoutTool implements Serializable {
    @JSONField(name = "M5")
    @SerializedName("IsShow")
    private boolean isShow;
    @JSONField(name = "M1")
    @SerializedName("ToolID")
    private String toolID;
    @JSONField(name = "M2")
    @SerializedName("ToolName")
    private String toolName;
    @JSONField(name = "M4")
    @SerializedName("ToolType")
    private int toolType;
    @JSONField(name = "M3")
    @SerializedName("URL")
    private String url;

}
