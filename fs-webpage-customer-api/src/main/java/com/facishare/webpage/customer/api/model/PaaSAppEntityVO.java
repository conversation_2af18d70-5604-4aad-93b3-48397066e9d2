package com.facishare.webpage.customer.api.model;

import com.facishare.webpage.customer.api.constant.SourceType;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;

import java.io.Serializable;
import java.util.List;

/**
 * Created by z<PERSON><PERSON> on 2020/11/12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaaSAppEntityVO implements Serializable {

    private ObjectId id;
    private int tenantId;
    private String appId;
    private List<String> scopeList = Lists.newArrayList();

    private List<ScopeForCross> scopeListForCross = Lists.newArrayList();
    private String name;
    private String description;
    private String iconIndex;
    private String icon;
    private int status;
    private String sourceType;
    private int creatorId;
    private long createTime;
    private int updaterId;
    private long updateTime;
    /**
     * 是否允许该应用 使用个人级应用视图
     */
    private boolean useUserPageTempleFlag = false;
    /**
     * 上传图片类型
     */
    private String uploadImgType;

    /**
     * 应用类型
     */
    private int appType;
    /**
     * 适用终端
     */
    private String accessType;

    /**
     * 应用web端跳转地址
     */
    private String weburl;

    /**
     * 应用web端跳转地址
     */
    private String appurl;
    /**
     * 父应用ID
     */
    private String parentAppId;
}
