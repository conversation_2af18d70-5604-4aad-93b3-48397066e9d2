package com.facishare.webpage.customer.api.service;

import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.MainChannelMenu;
import com.facishare.webpage.customer.api.model.arg.QueryMainChannelMenuArg;

import java.util.List;

/**
 * Created by zhangyu on 2020/11/26
 */
@RestResource(
        value = "WebPageResource",
        desc = "主导航相关rest接口",//ignoreI18n
        contentType = "application/json"
)
public interface MainChannelRestService {

    @POST(value = "/webPage/mainChannelRestService/queryMainChannelMenu", desc = "")
    List<MainChannelMenu> queryMainChannelMenu(@HeaderParam("x-fs-ei") String tenantId, QueryMainChannelMenuArg arg);


}
