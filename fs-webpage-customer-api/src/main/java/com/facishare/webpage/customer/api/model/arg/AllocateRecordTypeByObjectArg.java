package com.facishare.webpage.customer.api.model.arg;

import com.facishare.er.api.model.vo.AddRoleRecordTypeVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class AllocateRecordTypeByObjectArg implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 角色布局分配关系
     */
    List<AddRoleRecordTypeVO> roleList;
    /**
     * 应用Id
     */
    private String appId;
    /**
     * 对象apiName
     */
    private String objectApiName;

    private boolean isManual = false;
}
