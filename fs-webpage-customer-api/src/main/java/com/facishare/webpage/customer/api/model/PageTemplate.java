package com.facishare.webpage.customer.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PageTemplate implements Serializable {

    private String templeId;

    private int tenantId;

    private String name;

    private String appId;
    private String sourceType;

    private String type;

    private String webMenuId;

    private String webPageId;

    private String appPageId;

    private String description;

    private List<Scope> scopeList;

    private List<String> scopeNames;

    private String createName;

    private int creatorId;

    private Long createTime;

    private String updaterName;

    private int updaterId;

    private Long updateTime;

    private int status;

    private boolean system;

    private int priorityLevel = 0;
    /**
     * 模板类型
     * 1：个人
     * 2、租户
     * 3、系统级
     */
    private int pageTemplateType;
    /**
     * 是否支持员工编辑
     */
    private boolean canCustom;
    /**
     * 是否恢复到租户配置
     */
    private boolean resetTenantConfig = true;
    /**
     * 是否已经同步至app端的标识
     */
    private boolean hasBeenSynToApp = false;
    /**
     * app模板对应的web模板的id
     */
    private String fromWebPagetemplateId;

    /**
     * web端模板复制移动端后  对应的app模板id
     */
    private String appTempleId;
    private String translateKey;
    private String defaultTranslateKey;
    private List<String> preTranslateKeys;
}
