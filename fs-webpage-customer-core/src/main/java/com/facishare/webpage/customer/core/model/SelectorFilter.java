package com.facishare.webpage.customer.core.model;

import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON>hangyu on 2021/2/22
 */
public class SelectorFilter {

    private List<EmpsAndDep> empsAndDeps;
    private boolean isAll;
    private String responsibleDepName;
    private boolean canEdit = true;

    public List<EmpsAndDep> getEmpsAndDeps() {
        return empsAndDeps;
    }

    public void setEmpsAndDeps(List<EmpsAndDep> empsAndDeps) {
        this.empsAndDeps = empsAndDeps;
    }

    public boolean getIsAll() {
        return isAll;
    }

    public void setAll(boolean all) {
        isAll = all;
    }

    public String getResponsibleDepName() {
        return responsibleDepName;
    }

    public void setResponsibleDepName(String responsibleDepName) {
        this.responsibleDepName = responsibleDepName;
    }

    public boolean isCanEdit() {
        return canEdit;
    }

    public void setCanEdit(boolean canEdit) {
        this.canEdit = canEdit;
    }

    @Override
    public String toString() {
        return "SelectorFilter{" +
                "empsAndDeps=" + empsAndDeps +
                ", isAll=" + isAll +
                ", responsibleDepName='" + responsibleDepName + '\'' +
                ", canEdit=" + canEdit +
                '}';
    }

    @Data
    public static class EmpsAndDep {
        private String id;
        private int type;
    }
}
