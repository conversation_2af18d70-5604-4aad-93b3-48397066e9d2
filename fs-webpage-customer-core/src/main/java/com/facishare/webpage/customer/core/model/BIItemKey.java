package com.facishare.webpage.customer.core.model;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface BIItem<PERSON>ey {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Arg implements Serializable {
        private List<QueryBiCardI18nKey> getI18nKeysInfos;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class QueryBiCardI18n<PERSON><PERSON> implements Serializable {
        Set<String> dataIds = Sets.newHashSet();
        String tenantId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result implements Serializable {
        List<I18nKey> getI18nKeysInfos = Lists.newArrayList();
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    class I18nKey implements Serializable {
        private String tenantId;
        private Map<String, List<String>> dataId2I18nKeysMap;
    }


}
