package com.facishare.webpage.customer.aop;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.rest.InnerHeaders;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.facishare.webpage.customer.util.CEPXHeader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Locale;
import java.util.Objects;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2024/10/10
 * @Description : 设置I18n context
 */
@Slf4j
public class RequestFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        // 参见 com.facishare.cep.plugin.resolver.RequestAnnotationResolver.resolveArgument
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String xFsHeader = httpRequest.getHeader("x-fs-locale");
        String acceptLanguage = httpRequest.getHeader("accept-language");
        String language = StringUtils.isBlank(acceptLanguage) ? "" : httpRequest.getLocale().getLanguage();
        if ("".equals(language)) {
            language = StringUtils.isBlank(xFsHeader) ? Locale.CHINA.getLanguage() : xFsHeader;
        }
        String cepEi = StringUtils.stripToNull(httpRequest.getHeader("X-fs-Enterprise-Id"));
        String innerEi = StringUtils.stripToNull(httpRequest.getHeader("x-fs-ei"));
        String ei = Objects.isNull(cepEi) ? innerEi : cepEi;
        if (Objects.nonNull(ei)) {
            I18N.setContext(ei, language);
        }
        initRequestContext(cepEi, innerEi, httpRequest);
        // 继续过滤链执行，传递请求到下一个过滤器或目标资源
        try {
            chain.doFilter(request, response);
        } finally {
            // 请求结束后，清除 I18N 上下文
            I18N.clearContext();
            RequestContextManager.removeContext();
        }
    }

    private void initRequestContext(String cepEi, String innerEi, HttpServletRequest httpRequest) {
        if (StringUtils.isNotEmpty(cepEi)) {
            RequestContextManager.initContextWithUser(getCepContextUser(httpRequest));
        } else if (StringUtils.isNotEmpty(innerEi)) {
            RequestContextManager.initContextWithUser(getRestContextUser(httpRequest));
        } else {
            RequestContextManager.initContext();
        }
        if (WebPageGraySwitch.isAllowForEi(WebPageGraySwitch.ON_TIME_OPTIMIZE_EI, StringUtils.firstNonBlank(cepEi, innerEi, ""))) {
            RequestContextManager.initContextForIsFromManage(isFromManage(cepEi, innerEi, httpRequest));
        }
    }

    private boolean isFromManage(String cepEi, String innerEi, HttpServletRequest httpRequest) {
        String postId = "";
        if (StringUtils.isNotBlank(cepEi)) {
            postId = httpRequest.getHeader(CEPXHeader.POST_ID.key());
        } else if (StringUtils.isNotBlank(innerEi)) {
            postId = httpRequest.getHeader(InnerHeaders.POST_ID);
        }
        boolean isFromManage = StringUtils.isNotBlank(postId) && postId.endsWith(".manage");
        log.info("requestFilter info! postId: {}, isFromManage: {}", postId, isFromManage);
        return isFromManage;
    }

    private User getCepContextUser(HttpServletRequest httpRequest) {
        // 获取请求头，使用CEPXHeader的key()方法获取实际的请求头名称
        String tenantId = httpRequest.getHeader(CEPXHeader.TENANT_ID.key());
        String employeeId = httpRequest.getHeader(CEPXHeader.USER_ID.key());
        String outTenantId = httpRequest.getHeader(CEPXHeader.OUT_TENANT_ID.key());
        String outUserId = httpRequest.getHeader(CEPXHeader.OUT_USER_ID.key());
        String appId = httpRequest.getHeader(CEPXHeader.APP_ID.key());
        String upstreamOwnerId = httpRequest.getHeader(CEPXHeader.UPSTREAM_OWNER_ID.key());
        String outIdentityType = httpRequest.getHeader(CEPXHeader.OUT_IDENTITY_TYPE.key());

        return createUserFromHeaders(tenantId, employeeId, outTenantId, outUserId, appId, upstreamOwnerId, outIdentityType);
    }

    private User getRestContextUser(HttpServletRequest request) {
        // 获取请求头
        String tenantId = request.getHeader(InnerHeaders.TENANT_ID);
        String employeeId = request.getHeader(InnerHeaders.USER_ID);
        String outTenantId = request.getHeader(InnerHeaders.OUT_TENANT_ID);
        String outUserId = request.getHeader(InnerHeaders.OUT_USER_ID);
        String appId = request.getHeader(InnerHeaders.APP_ID);
        String upstreamOwnerId = request.getHeader(InnerHeaders.UPSTREAM_OWNER_ID);
        String outIdentityType = request.getHeader(InnerHeaders.OUT_IDENTITY_TYPE);

        return createUserFromHeaders(tenantId, employeeId, outTenantId, outUserId, appId, upstreamOwnerId, outIdentityType);
    }

    /**
     * 根据请求头信息创建User对象
     *
     * @param tenantId        租户ID
     * @param employeeId      员工ID
     * @param outTenantId     外部租户ID
     * @param outUserId       外部用户ID
     * @param appId           应用ID
     * @param upstreamOwnerId 上游拥有者ID
     * @param outIdentityType 外部身份类型
     * @return 创建的User对象
     */
    private User createUserFromHeaders(String tenantId, String employeeId, String outTenantId,
                                       String outUserId, String appId, String upstreamOwnerId,
                                       String outIdentityType) {
        // 创建User对象
        User user = new User();

        // 设置appId
        if (StringUtils.isNotBlank(appId)) {
            user.setAppId(appId);
        }

        // 安全处理tenantId
        if (StringUtils.isNotBlank(tenantId)) {
            try {
                user.setTenantId(Integer.parseInt(tenantId));
            } catch (NumberFormatException e) {
                log.warn("Invalid tenantId format: {}", tenantId);
            }
        }

        // 安全处理employeeId
        if (StringUtils.isNotBlank(employeeId)) {
            try {
                user.setUserId(Integer.parseInt(employeeId));
            } catch (NumberFormatException e) {
                log.warn("Invalid employeeId format: {}", employeeId);
            }
        }

        // 安全处理outTenantId
        if (StringUtils.isNotBlank(outTenantId)) {
            try {
                user.setOutTenantId(Long.valueOf(outTenantId));
            } catch (NumberFormatException e) {
                log.warn("Invalid outTenantId format: {}", outTenantId);
            }
        }

        // 安全处理outUserId
        if (StringUtils.isNotBlank(outUserId)) {
            try {
                user.setOutUserId(Long.valueOf(outUserId));
            } catch (NumberFormatException e) {
                log.warn("Invalid outUserId format: {}", outUserId);
            }
        }

        // 安全处理upstreamOwnerId
        if (StringUtils.isNotBlank(upstreamOwnerId)) {
            try {
                user.setUpstreamOwnerId(Integer.valueOf(upstreamOwnerId));
            } catch (NumberFormatException e) {
                log.warn("Invalid upstreamOwnerId format: {}", upstreamOwnerId);
            }
        }

        // 设置outIdentityType
        if (StringUtils.isNotBlank(outIdentityType)) {
            user.setOutIdentityType(outIdentityType);
        }

        return user;
    }

    @Override
    public void destroy() {

    }
}
