package com.facishare.webpage.customer.common.model;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.core.util.CollectionUtils;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
public class TenantUiInfoExt {
    private JSONObject map;

    private static final String NAME_I18N_KEY = "nameI18nKey";
    private static final String TITLE = "title";
    private static final String CLIENT = "client";

    public static TenantUiInfoExt of(JSONObject jsonObject) {
        return new TenantUiInfoExt(jsonObject);
    }

    public JSONObject getMap() {
        return map;
    }

    public String getTitle() {
        return map.getString(TITLE);
    }

    public String getNameI18nKey() {
        return map.getString(NAME_I18N_KEY);
    }

    public List<String> getSupportClient() {
        if (CollectionUtils.empty(map.getJSONArray(CLIENT))) {
            return Lists.newArrayList();
        }
        return map.getJSONArray(CLIENT).toJavaList(String.class);
    }

    public void setTitle(String title) {
        map.put(TITLE, title);
    }
}
