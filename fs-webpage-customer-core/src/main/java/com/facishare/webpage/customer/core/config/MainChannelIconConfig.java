package com.facishare.webpage.customer.core.config;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.core.Icon;
import com.facishare.webpage.customer.api.model.core.IndexIcon;
import com.github.autoconf.ConfigFactory;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class MainChannelIconConfig {

    private Map<Integer, IndexIcon> indexIconMap=new HashMap<>();

    public void init() {
        ConfigFactory.getInstance().getConfig("fs-webpage-customer-mainIndexIcon", iConfig -> {
            List<IndexIcon> preObjects = JSONObject.parseArray(iConfig.getString(), IndexIcon.class);
            if (CollectionUtils.isNotEmpty(preObjects)) {
                indexIconMap = preObjects.stream().collect(Collectors.toMap(IndexIcon::getIndex, Function.identity(), (key1, key2) -> key2));
            }
        });
    }

    public Icon getIconByIndex(Integer index) {
        IndexIcon indexIcon = indexIconMap.get(index);
        if (indexIcon == null || indexIcon.getIcon() == null) {
            return null;
        }
        Icon icon = indexIcon.getIcon();
        return icon;
    }
}
