package com.facishare.webpage.customer.api.constant;

import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public interface UIPaaSI18NKey {

    //菜单分组国际化前缀 + 分组名
    //菜单分组国际化前缀 + 分组ID
    String GROUP_PREFIX = "group_";
    String PaaS_App_Name = "UIPaaS.%s.name"; //PaaS应用id，PaaS应用名称国际化词条
    String PaaS_App_Des = "UIPaaS.%s.des"; //PaaS应用id，PaaS应用描述国际化词条
    String PaaS_App_Template_Name = "UIPaaS.Template.%s.name"; //PaaS应用视图模板id（不包含企业id），PaaS应用视图模板名国际化词条
    String System_Default_PREFIX = "Default_"; // 系统预置的词条，需要再国际化平台架构预置词条，用来判断用户是否变更过

    String USER_DEFAULT_SYSTEM = "qx_srv.common.default.system";

    static String getUIPaasI18NValue(Map<String, String> i18nKeyInfoMap, String translateKey, String key, String value, String lang, Integer tenantId) {
        if (i18nKeyInfoMap == null || i18nKeyInfoMap.isEmpty()) {
            return value;
        }
        String translateName = i18nKeyInfoMap.get(translateKey);
        if (StringUtils.isNotBlank(translateName)) {
            return translateName;
        }

        Localization order = I18nClient.getInstance().getByOrder(Lists.newArrayList(key, System_Default_PREFIX + key), tenantId);

        if (Objects.nonNull(order) && Objects.nonNull(order.getData()) && order.getData().containsValue(value)) {
            return order.get(lang, value);
        } else {
            return value;
        }
    }

    static String getUIPaasI18NValue(Map<String, String> i18nKeyInfoMap, String translateKey, String key, String value) {
        // 之前取名称多语逻辑有问题, 现在已经切换到重载方法上, 这个只用于描述
        if (i18nKeyInfoMap == null || i18nKeyInfoMap.isEmpty()) {
            return value;
        }
        String translateName = i18nKeyInfoMap.get(translateKey);
        if (StringUtils.isNotBlank(translateName)) {
            return translateName;
        }
        if (!i18nKeyInfoMap.containsKey(key)) {
            return value;
        }

        Localization defaultLocalization = TranslateI18nUtils.getAllTransValueByOrder(Lists.newArrayList(System_Default_PREFIX + key), 0);

        if (CollectionUtils.isEmpty(defaultLocalization.getData().values())) {
            return i18nKeyInfoMap.get(key);
        }

        if (defaultLocalization.getData().containsValue(value)) { // 如果用户变更了默认词条，就用用户的词条
            return i18nKeyInfoMap.get(key);
        } else {
            return value;
        }
    }


    static String getLinkAppI18nValue(Map<String, String> i18nKeyInfoMap, String translateKey, String preKey, String value) {
        if (StringUtils.isBlank(value)) {
            value = "";
        }
        if (null == i18nKeyInfoMap || i18nKeyInfoMap.isEmpty()) {
            return value;
        }
        String transValue = i18nKeyInfoMap.get(translateKey);
        if (StringUtils.isNotEmpty(transValue)) {
            return transValue;
        }
        String preValue = i18nKeyInfoMap.get(preKey);
        return StringUtils.isNotEmpty(preValue) ? preValue : value;
    }


    @Data
    @Builder
    class PreAndDefaultTranslateKey {
        private String defaultTranslateKey;
        private List<String> preTranslateKeys;
    }

}
