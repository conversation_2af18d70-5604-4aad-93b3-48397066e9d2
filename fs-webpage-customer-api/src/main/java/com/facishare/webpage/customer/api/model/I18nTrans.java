package com.facishare.webpage.customer.api.model;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public interface I18nTrans {


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    class TransArg {
        private String name;
        private String customKey;
        private String originalCustomKey;
        @Builder.Default
        private List<String> preKeyList = Lists.newArrayList();
        @Builder.Default
        private List<String> oldKeyList = Lists.newArrayList();

        public String getOriginalOrCustomKey() {
            return StringUtils.defaultString(originalCustomKey, customKey);
        }
    }
}
