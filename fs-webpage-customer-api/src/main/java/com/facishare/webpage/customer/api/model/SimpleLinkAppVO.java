package com.facishare.webpage.customer.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SimpleLinkAppVO {
    private String id;
    private String name;
    private String nameI18nKey;
    private String icon;
    private String appIcon;
    private String managerIcon;
    private String introduction;
    private String introductionI18nKey;
    /**
     * 互联管理后台web端的url,在paas侧没用
     */
    private String webManagerUrl;
    /**
     * 互联管理后台app端的url,在paas侧没用
     */
    private String appManagerUrl;
    /**
     * web端主导航显示的url
     */
    private String webUrl;
    /**
     * 移动端展示的url
     */
    private String appUrl;
    private String enablePrompt;
    private String disablePrompt;
    private Long createTime;
    private Long updateTime;
    private Boolean unVisibleInManager;
    /**
     * 是否支持扩展对象
     */
    private Boolean supportExtendObject;
    /**
     * 是否支持关联CRM对象
     */
    private Boolean associationCrmObject;
    /**
     * 应用开启回调业务的url
     */
    private String openValidatePostUrl;
    /**
     * 是否展示paas入口
     */
    private Integer showPaasPage;
    /**
     * 企业应用状态  1-启用，2-禁用
     */
    private Integer status;
    private Integer type;
    private List<String> scopeList;
    private List<ScopeForCross> scopeListForCross;
    //是否默认启用
    private Integer enabledByDefault;

    public void copyProperties(SimpleLinkAppVO source) {
        if (source.getName() != null) {
            this.name = source.getName();
        }
        if (source.getNameI18nKey() != null) {
            this.nameI18nKey = source.getNameI18nKey();
        }
        if (source.getIcon() != null) {
            this.icon = source.getIcon();
        }
        if (source.getAppIcon() != null) {
            this.appIcon = source.getAppIcon();
        }
        if (source.getManagerIcon() != null) {
            this.managerIcon = source.getManagerIcon();
        }
        if (source.getIntroduction() != null) {
            this.introduction = source.getIntroduction();
        }
        if (source.getIntroductionI18nKey() != null) {
            this.introductionI18nKey = source.getIntroductionI18nKey();
        }
        if (source.getWebManagerUrl() != null) {
            this.webManagerUrl = source.getWebManagerUrl();
        }
        if (source.getAppManagerUrl() != null) {
            this.appManagerUrl = source.getAppManagerUrl();
        }
        if (source.getWebUrl() != null) {
            this.webUrl = source.getWebUrl();
        }
        if (source.getAppUrl() != null) {
            this.appUrl = source.getAppUrl();
        }
        if (source.getEnablePrompt() != null) {
            this.enablePrompt = source.getEnablePrompt();
        }
        if (source.getDisablePrompt() != null) {
            this.disablePrompt = source.getDisablePrompt();
        }
        if (source.getCreateTime() != null) {
            this.createTime = source.getCreateTime();
        }
        if (source.getUpdateTime() != null) {
            this.updateTime = source.getUpdateTime();
        }
        if (source.getUnVisibleInManager() != null) {
            this.unVisibleInManager = source.getUnVisibleInManager();
        }
        if (source.getSupportExtendObject() != null) {
            this.supportExtendObject = source.getSupportExtendObject();
        }
        if (source.getAssociationCrmObject() != null) {
            this.associationCrmObject = source.getAssociationCrmObject();
        }
        if (source.getOpenValidatePostUrl() != null) {
            this.openValidatePostUrl = source.getOpenValidatePostUrl();
        }
        if (source.getShowPaasPage() != null) {
            this.showPaasPage = source.getShowPaasPage();
        }
        if (source.getStatus() != null) {
            this.status = source.getStatus();
        }
        if (source.getType() != null) {
            this.type = source.getType();
        }
        if (source.getEnabledByDefault() != null) {
            this.enabledByDefault = source.getEnabledByDefault();
        }
    }

}
