package com.facishare.webpage.customer.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LinkAppVO {
    private String id;
    private String name;
    private String nameI18nKey;
    private String icon;
    private String appIcon;
    private String appSessionIcon;
    private String managerIcon;
    private String introduction;
    private String introductionI18nKey;
    private Integer type;
    private String webManagerUrl;
    private String appManagerUrl;
    private String webUrl;
    private String appUrl;
    private Boolean supportSetManager;
    private String unableSetManagerText;
    private String wAppId;
    private Integer status;
    private String internalVisibleRangePrompt;
    private String enablePrompt;
    private String disablePrompt;
    private Integer enabledByDefault;
    private String addManagerTipsUrl;
    private String downstreamMenuJson;
    private Boolean isDefaultSelect;
    private Integer isNew;
    private Long createTime;
    private Long updateTime;
    private String sessionId;
    private Boolean unVisibleInManager;
    private Integer authType;
    private Boolean authByEa = false;
    private Boolean supportRoleExt;
    /** @deprecated */
    @Deprecated
    private Set<Integer> enterpriseTypes;
    private Integer dataAuthType;
    private Boolean supportExtendObject;
    private Boolean associationCrmObject;
    private String openValidatePostUrl;
    private Boolean supportNoFsAccount;
    private Boolean appVisibility;
    private Integer availableTime;
    private Integer showPaasPage;
    private String supportIdentityTypes;
    /** @deprecated */
    @Deprecated
    private Boolean supportGuestor;
    private Set<String> supportMapperApiNames;

    private Integer authedRoleNum;
    private Boolean hasLinkAppAdmin = true;
    private Boolean isExpired;


}
