package com.facishare.webpage.customer.api.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetSynNewViewStatusResult implements Serializable {
    private String appId;
    private int tenantId;
    //1：待迁移-》2：迁移中-》3：迁移完成待确认-》4：迁移结束
    private int status;
    //1-100 四舍五入
    private int process;
}