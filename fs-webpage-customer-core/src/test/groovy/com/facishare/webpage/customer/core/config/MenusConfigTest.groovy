import com.alibaba.fastjson.JSONObject
import com.facishare.webpage.customer.api.model.core.Menu
import com.facishare.webpage.customer.core.config.MenusConfig
import spock.lang.Specification

import java.util.function.Function
import java.util.stream.Collectors

class MenusConfigTest extends Specification {

    def menusConfig = new MenusConfig()
    def menuCollections = [:]

    def fs_webpage_customer_menus

    def setup() {
        fs_webpage_customer_menus = '''[{"deviceTypes":["WEB","Desktop_Mac","Desktop_Windows"],"tenantPrivilege":{"licenseModuleCodes":["isv_dev_kit_app"]},"personPrivilege":{},"name":"ISV应用","icon":{},"iconIndex":392,"nameI18nKey":"fs.uipaas.isv.software.package","id":"isv_software_package","url":{"webGoJumpUrl":"isv_software_package","webUrl":"isv_software_package","useServerUrl":true}},{"tenantPrivilege":{"bizConfKeys":["eservice_ds_knowledge_base_list"],"licenseModuleCodes":["knowledgemanagement_app"]},"name":"知识库列表","icon":{"icon_2":"https://a9.fspage.com/FSR/link/eservice/fonts/knowledgeFace.png","icon_1":"https://a9.fspage.com/FSR/link/eservice/fonts/knowledgeLine.svg"},"nameI18nKey":"eservice_menu_knowledge_list","id":"eservice_dlt_knowledge_base_list","iconIndex":174,"url":{"useServerUrl":true,"webGoJumpUrl":"thirdapp/=/portal-knowledge-page-list","androidUrl":"https://${variables_endpoint.http_domain}/proj/page/doc?ea=${fs_upstream_ea}&isEM6=1&fsAppId=FSAID_11490f80#/list","iOSUrl":"https://${variables_endpoint.http_domain}/proj/page/doc?ea=${fs_upstream_ea}&isEM6=1&fsAppId=FSAID_11490f80#/list"}},{"deviceTypes":["iOS","Android","WEB","Desktop_Mac","Desktop_Windows"],"tenantPrivilege":{"licenseProductCodes":["key_accounts_management_industry"]},"personPrivilege":{},"name":"客户关系规划目标","icon":{"icon_2":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/facial/fx-icon-obj-app194.svg","icon_1":"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/fx-icon-obj-app194.svg"},"nameI18nKey":"CustomerRetentionObjectivesObj.attribute.self.display_name","id":"CustomerRetentionObjectivesObj","url":{"useServerUrl":false}}]'''
        def menus = JSONObject.parseArray(fs_webpage_customer_menus, Menu)
        menuCollections = menus.stream()
                .collect(Collectors.toMap({ menu -> menu.getId() }, Function.identity(), { t1, t2 -> t2 }))

    }

    def "test getMenusByIds with valid ids"() {
        given:
        menusConfig.menuCollections = menuCollections
        def id1 = "eservice_dlt_knowledge_base_list"
        def id2 = "isv_software_package"

        def name1 = "知识库列表"
        def name2 = "ISV应用"

        def oldMenu1 = menuCollections.get(id1)
        def oldMenu2 = menuCollections.get(id2)

        when:
        def result = menusConfig.getMenusByIds([id1, id2])

        then:
        result.size() == 2
        result*.id == [id1, id2]
        result*.name == [name1, name2]
        result.every { !it.is(oldMenu1) && !it.is(oldMenu2) } // 确保是深拷贝

    }

    def "test getMenusByIds with invalid ids"() {
        given:
        menusConfig.menuCollections = menuCollections

        when:
        def result = menusConfig.getMenusByIds(["2", "3"])

        then:
        result.isEmpty()
    }

    def "test getMenusByIds with empty list"() {
        given:
        menusConfig.menuCollections = menuCollections
        when:
        def result = menusConfig.getMenusByIds([])

        then:
        result.isEmpty()
    }

    def "test getMenusByIds with nested object"() {
        given:
        menusConfig.menuCollections = menuCollections

        def id = "CustomerRetentionObjectivesObj"
        def name = "客户关系规划目标"

        def oldMenu = menuCollections.get(id)

        when:
        def result = menusConfig.getMenusByIds([id])
        result[0].deviceTypes = []

        then:
        result.size() == 1
        result[0].id == id
        result[0].name == name
        !result[0].is(oldMenu) // 确保是深拷贝
        oldMenu.deviceTypes.size() == 5
        result[0].deviceTypes.size() == 0
    }
} 