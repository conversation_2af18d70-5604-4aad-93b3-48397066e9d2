package com.facishare.webpage.customer.controller.model.result.homepage;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.webpage.customer.core.config.WorkTableConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 客服工作台 适用应用/渠道分类 的可用数据
 * <AUTHOR> ZhenHui
 * @Date : 2025/1/7
 */
@Data
public class GetWorkTableConfigResult implements Serializable {

    private WorkTableConfigDataVO workTableConfig;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WorkTableConfigDataVO implements Serializable {
        @JSONField(ordinal = 1)
        private List<WorkTableConfig.App> app = Collections.emptyList();
        @JSONField(ordinal = 2)
        private List<WorkTableConfig.Channel> channel = Collections.emptyList();
    }

} 