package com.facishare.webpage.customer.core.config;

import com.facishare.webpage.customer.core.model.Widget;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * WidgetsConfig单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class WidgetsConfigTest {

    @InjectMocks
    private WidgetsConfig widgetsConfig;

    private Map<String, Widget> mockWidgets;
    private Map<String, Widget> mockCardIdWidgets;

    @Before
    public void setUp() throws Exception {
        // 初始化测试数据
        mockWidgets = new HashMap<>();
        mockCardIdWidgets = new HashMap<>();

        // 创建测试Widget
        Widget widget1 = createTestWidget("widget1", "card1", "Test Widget 1");
        Widget widget2 = createTestWidget("widget2", "card2", "Test Widget 2");
        Widget widget3 = createTestWidget("widget3", null, "Test Widget 3");

        mockWidgets.put("widget1", widget1);
        mockWidgets.put("widget2", widget2);
        mockWidgets.put("widget3", widget3);

        mockCardIdWidgets.put("card1", widget1);
        mockCardIdWidgets.put("card2", widget2);

        // 使用反射设置私有字段
        setPrivateField(widgetsConfig, "widgets", mockWidgets);
        setPrivateField(widgetsConfig, "cardIdWidgets", mockCardIdWidgets);
    }

    /**
     * 测试通过id获取Widget成功的情况
     */
    @Test
    public void testGetWidgetByCardIdOrId_WithValidId_ShouldReturnWidget() {
        // Given
        String id = "widget1";
        String cardId = null;

        // When
        Widget result = widgetsConfig.getWidgetByCardIdOrId(id, cardId);

        // Then
        assertNotNull("应该返回Widget对象", result);
        assertEquals("Widget ID应该匹配", "widget1", result.getId());
        assertEquals("Widget名称应该匹配", "Test Widget 1", result.getName());
        assertEquals("Widget cardId应该匹配", "card1", result.getCardId());
    }

    /**
     * 测试通过cardId获取Widget成功的情况
     */
    @Test
    public void testGetWidgetByCardIdOrId_WithValidCardId_ShouldReturnWidget() {
        // Given
        String id = null;
        String cardId = "card2";

        // When
        Widget result = widgetsConfig.getWidgetByCardIdOrId(id, cardId);

        // Then
        assertNotNull("应该返回Widget对象", result);
        assertEquals("Widget ID应该匹配", "widget2", result.getId());
        assertEquals("Widget名称应该匹配", "Test Widget 2", result.getName());
        assertEquals("Widget cardId应该匹配", "card2", result.getCardId());
    }

    /**
     * 测试id优先于cardId的情况
     */
    @Test
    public void testGetWidgetByCardIdOrId_WithBothIdAndCardId_ShouldPrioritizeId() {
        // Given
        String id = "widget1";
        String cardId = "card2";

        // When
        Widget result = widgetsConfig.getWidgetByCardIdOrId(id, cardId);

        // Then
        assertNotNull("应该返回Widget对象", result);
        assertEquals("应该返回id对应的Widget", "widget1", result.getId());
        assertEquals("Widget名称应该匹配", "Test Widget 1", result.getName());
    }

    /**
     * 测试id和cardId都为空的情况
     */
    @Test
    public void testGetWidgetByCardIdOrId_WithBothNull_ShouldReturnNull() {
        // Given
        String id = null;
        String cardId = null;

        // When
        Widget result = widgetsConfig.getWidgetByCardIdOrId(id, cardId);

        // Then
        assertNull("应该返回null", result);
    }

    /**
     * 测试id和cardId都为空字符串的情况
     */
    @Test
    public void testGetWidgetByCardIdOrId_WithBothBlank_ShouldReturnNull() {
        // Given
        String id = "";
        String cardId = "   ";

        // When
        Widget result = widgetsConfig.getWidgetByCardIdOrId(id, cardId);

        // Then
        assertNull("应该返回null", result);
    }

    /**
     * 测试id不存在但cardId存在的情况
     */
    @Test
    public void testGetWidgetByCardIdOrId_WithInvalidIdButValidCardId_ShouldReturnWidgetByCardId() {
        // Given
        String id = "nonexistent";
        String cardId = "card1";

        // When
        Widget result = widgetsConfig.getWidgetByCardIdOrId(id, cardId);

        // Then
        assertNotNull("应该返回Widget对象", result);
        assertEquals("Widget ID应该匹配", "widget1", result.getId());
        assertEquals("Widget cardId应该匹配", "card1", result.getCardId());
    }

    /**
     * 测试id和cardId都不存在的情况
     */
    @Test
    public void testGetWidgetByCardIdOrId_WithBothInvalid_ShouldReturnNull() {
        // Given
        String id = "nonexistent";
        String cardId = "nonexistent_card";

        // When
        Widget result = widgetsConfig.getWidgetByCardIdOrId(id, cardId);

        // Then
        assertNull("应该返回null", result);
    }

    /**
     * 测试JSON序列化异常的情况
     */
    @Test
    public void testGetWidgetByCardIdOrId_WithSerializationError_ShouldReturnNull() throws Exception {
        // Given
        String id = null;
        String cardId = "card1";
        
        // 创建一个会导致序列化异常的Widget
        Widget problematicWidget = mock(Widget.class);
        when(problematicWidget.getId()).thenThrow(new RuntimeException("Serialization error"));
        
        Map<String, Widget> problematicCardIdWidgets = new HashMap<>();
        problematicCardIdWidgets.put("card1", problematicWidget);
        setPrivateField(widgetsConfig, "cardIdWidgets", problematicCardIdWidgets);

        // When
        Widget result = widgetsConfig.getWidgetByCardIdOrId(id, cardId);

        // Then
        assertNull("序列化异常时应该返回null", result);
    }

    /**
     * 测试widgets为null的情况
     */
    @Test
    public void testGetWidgetByCardIdOrId_WithNullWidgets_ShouldReturnNull() throws Exception {
        // Given
        setPrivateField(widgetsConfig, "widgets", null);
        String id = "widget1";
        String cardId = null;

        // When
        Widget result = widgetsConfig.getWidgetByCardIdOrId(id, cardId);

        // Then
        assertNull("widgets为null时应该返回null", result);
    }

    /**
     * 测试cardIdWidgets为null的情况
     */
    @Test
    public void testGetWidgetByCardIdOrId_WithNullCardIdWidgets_ShouldReturnNull() throws Exception {
        // Given
        setPrivateField(widgetsConfig, "cardIdWidgets", null);
        String id = "nonexistent";
        String cardId = "card1";

        // When
        Widget result = widgetsConfig.getWidgetByCardIdOrId(id, cardId);

        // Then
        assertNull("cardIdWidgets为null时应该返回null", result);
    }

    /**
     * 创建测试用的Widget对象
     */
    private Widget createTestWidget(String id, String cardId, String name) {
        Widget widget = new Widget();
        widget.setId(id);
        widget.setCardId(cardId);
        widget.setName(name);
        widget.setWidgetType(1);
        widget.setHeight(200);
        widget.setLimit(1);
        widget.setGrayLimit(1);
        return widget;
    }

    /**
     * 使用反射设置私有字段
     */
    private void setPrivateField(Object target, String fieldName, Object value) throws Exception {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }
}
