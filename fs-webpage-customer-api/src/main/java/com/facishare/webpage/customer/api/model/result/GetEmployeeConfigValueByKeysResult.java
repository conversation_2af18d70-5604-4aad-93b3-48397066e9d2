package com.facishare.webpage.customer.api.model.result;

import com.facishare.webpage.customer.api.model.EmployeeConfig;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2019/10/28
 */
@Data
public class GetEmployeeConfigValueByKeysResult implements Serializable {
    @SerializedName("ConfigInfoList")
    private List<EmployeeConfig> configInfoList;

}
