package com.facishare.webpage.customer.api.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by zhangyu on 2020/11/12
 */
@Data
@Builder
public class AppItemVO {

    private String appId;

    private int appType;

    private String appTypeName;

    private int payType;

    private Integer payStatus;

    private String icon;

    private int status;

    private String name;

    private String description;

    private String createName;

    private String sourceType;
    /**
     * 上传图标的类型
     * 没有上传图标就不下发这个值
     */
    private String uploadType;

    private List<String> namePreKeyList;
    private List<String> descPreKeyList;

}
