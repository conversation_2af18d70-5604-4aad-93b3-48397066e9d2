package com.facishare.webpage.customer.api.service;

import com.facishare.er.api.model.vo.AllocatedRecordTypeVO;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.SimpleLinkAppVO;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;

import java.util.Map;

@RestResource(
        value = "WebPageResource",
        desc = "应用页面服务",//ignoreI18n
        contentType = "application/json"
)
public interface LinkAppRestService {
    //-----------------------------------互联应用关联对象相关操作接口开始-----------------------------------
    @POST(value = "/webPage/LinkAppRestService/insertLinkAppObjectAssociationList", desc = "批量新增互联应用关联对象")//ignoreI18n
    void insertLinkAppObjectAssociationList(@HeaderMap Map<String, String> headers, @Body InsertLinkAppAssociationObjectListArg arg);

    @POST(value = "/webPage/LinkAppRestService/getLinkAppObjectAssociationList", desc = "批量查询互联应用关联对象")//ignoreI18n
    GetLinkAppAssociationObjectListResult getLinkAppObjectAssociationList(@HeaderMap Map<String, String> headers, @Body GetLinkAppAssociationObjectListArg arg);

    @POST(value = "/webPage/LinkAppRestService/deleteLinkAppObjectAssociationList", desc = "批量删除互联应用关联对象")//ignoreI18n
    void deleteLinkAppObjectAssociationList(@HeaderMap Map<String, String> headers, @Body DeleteLinkAppAssociationObjectListArg arg);

    @POST(value = "/webPage/LinkAppRestService/batchUpdateStatusByTypeAndUpstreamAndApiNames", desc = "批量更新互联应用关联对象")//ignoreI18n
    void batchUpdateStatusByTypeAndUpstreamAndApiNames(@HeaderMap Map<String, String> headers, @Body UpdateLinkAppAssociationObjectListArg arg);


    @POST(value = "/webPage/LinkAppRestService/batchAddAssociationObjects", desc = "批量新增互联应用关联对象")// // ignoreI18n
    BatchAddAssociationObjectsResult batchAddAssociationObjects(@HeaderMap Map<String, String> headers, @Body BatchAddAssociationObjectsRestArg arg);

    @POST(value = "/webPage/LinkAppRestService/listAllocatedRecordTypeByObject", desc = "查询业务类型")// // ignoreI18n
    AllocatedRecordTypeVO listAllocatedRecordTypeByObject(@HeaderMap Map<String, String> headers, @Body ListAllocatedRecordTypeByObjectRestArg arg);

    @POST(value = "/webPage/LinkAppRestService/allocateRecordTypeByObject", desc = "分配业务类型")// // ignoreI18n
    void allocateRecordTypeByObject(@HeaderMap Map<String, String> headers, @Body AllocateRecordTypeByObjectRestArg arg);

    //-----------------------------------互联应用关联对象相关操作接口结束-----------------------------------


    //--------------------------------------------互联应用相关操作接口开始--------------------------------------------
    @POST(value = "/webPage/LinkAppRestService/getPresetlinkAppVOList", desc = "查询某个企业下的所有的预置互联应用")// // ignoreI18n
    GetPresetLinkAppListResult getPresetlinkAppVOList(@HeaderMap Map<String, String> headers, @Body GetlinkAppListArg arg);

    @POST(value = "/webPage/LinkAppRestService/getPresetlinkAppVOListWithEi", desc = "查询某个企业下的互联应用的信息, arg必须包含tenantId")
    GetPresetLinkAppListResult getPresetlinkAppVOListWithEi(@HeaderMap Map<String, String> headers, @Body GetlinkAppListArg arg);

    @POST(value = "/webPage/LinkAppRestService/getlinkAppVOByLinkAppId", desc = "查询单个预置互联应用")// // ignoreI18n
    SimpleLinkAppVO getlinkAppVOByLinkAppId(@HeaderMap Map<String, String> headers, @Body GetLinkAppByAppIdArg arg);

    @POST(value = "/webPage/LinkAppRestService/getlinkAppVOListByRoleId", desc = "通过角色id查询预置互联应用列表")// // ignoreI18n
    GetPresetLinkAppListByRoleIdResult getlinkAppVOListByRoleId(@HeaderMap Map<String, String> headers, @Body GetlinkAppListArg arg);


    @POST(value = "/webPage/LinkAppRestService/getLinkAppVOList", desc = "查询互联应用列表")// // ignoreI18n
    GetSimpleLinkAppListResult getLinkAppVOList(@HeaderMap Map<String, String> headers, @Body GetlinkAppListArg arg);

    @POST(value = "/webPage/LinkAppRestService/createOrUpdatePresetlinkApp", desc = "创建/修改预置互联应用")// // ignoreI18n
    void createOrUpdatePresetlinkApp(@HeaderMap Map<String, String> headers, @Body SimpleLinkAppVO arg);

    @POST(value = "/webPage/LinkAppRestService/batchAddLinkApp", desc = "批量企业 开通预置互联应用")// // ignoreI18n
    void batchAddLinkApp(@HeaderMap Map<String, String> headers, @Body BatchAddlinkAppListArg arg);

    @POST(value = "/webPage/LinkAppRestService/batchUpdateStatus", desc = "批量企业 更新 互联应用的状态")// // ignoreI18n
    void batchUpdateStatus(@HeaderMap Map<String, String> headers, @Body BatchUpdatelinkAppStatusArg arg);

    @POST(value = "/webPage/LinkAppRestService/deletePresetlinkApp", desc = "删除预置互联应用")// // ignoreI18n
    void deletePresetlinkApp(@HeaderMap Map<String, String> headers, @Body DeletelinkAppListArg arg);

    @POST(value = "/webPage/LinkAppRestService/deleteTenantlinkAppRelation", desc = "删除应用和企业的关联关系")// // ignoreI18n
    void deleteTenantlinkAppRelation(@HeaderMap Map<String, String> headers, @Body DeletelinkAppListArg arg);


    @POST(value = "/webPage/LinkAppRestService/getFirstUpstreamEaByApp", desc = "查询第一个开通某个互联应用的企业信息")// // ignoreI18n
    Integer getFirstUpstreamEaByApp(@HeaderMap Map<String, String> headers, @Body GetLinkAppByAppIdArg arg);

    @POST(value = "/webPage/LinkAppRestService/listByStatus", desc = "查询互联应用某个状态下的所有企业id")// // ignoreI18n
    GetEiListByLinkAppStatusResult listByStatus(@HeaderMap Map<String, String> headers, @Body GetTenantIdListForLinkAppByStatusArg arg);

    //--------------------------------------------互联应用相关操作接口结束--------------------------------------------


    //----------------------------------互联应用url相关接口----------------------------------------------
    @POST(value = "/webPage/LinkAppRestService/createOrUpdateLinkAppUrl", desc = "新建或者更新互联企业的url")// // ignoreI18n
    void createOrUpdateLinkAppUrl(@HeaderMap Map<String, String> headers, @Body CreateOrUpdateLinkAppUrlArg arg);

    @POST(value = "/webPage/LinkAppRestService/deleteLinkAppUrl", desc = "删除互联企业的url")// // ignoreI18n
    void deleteLinkAppUrl(@HeaderMap Map<String, String> headers, @Body DeleteLinkAppUrlArg arg);
    //----------------------------------互联应用url相关接口结束----------------------------------------------


    //--------------------------------------------互联应用关联角色相关操作接口开始--------------------------------------------
    @POST(value = "/webPage/LinkAppRestService/bathCreateOrUpdatelinkAppRole", desc = "互联应用批量添加角色")// // ignoreI18n
    void bathCreateOrUpdatelinkAppRole(@HeaderMap Map<String, String> headers, @Body BathCreateOrUpdatelinkAppRoleArg arg);

    @POST(value = "/webPage/LinkAppRestService/getLinkAppRoleList", desc = "预置互联应用批量添加角色")// // ignoreI18n
    GetLinkAppRoleListResult getLinkAppRoleList(@HeaderMap Map<String, String> headers, @Body GetlinkAppRoleListArg arg);

    @POST(value = "/webPage/LinkAppRestService/deleteLinkAppRole", desc = "互联应用删除角色")// // ignoreI18n
    void deleteLinkAppRole(@HeaderMap Map<String, String> headers, @Body DeleteLinkAppRoleAra arg);
    //--------------------------------------------互联应用关联角色相关操作接口结束--------------------------------------------


}
