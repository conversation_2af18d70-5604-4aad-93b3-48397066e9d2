package com.facishare.webpage.customer.api.utils;

import com.facishare.webpage.customer.api.model.User;

import java.util.Objects;

public class RequestContextManager {

    private static final ThreadLocal<RequestContext> THREAD_LOCAL = new ThreadLocal<>();

    public static void initContextForIsFromManage(boolean isFromManage) {
        RequestContext context = getContext();
        if (Objects.isNull(context)) {
            context = new RequestContext();
        }
        context.setFromManage(isFromManage);
        setContext(context);
    }

    public static void initContext(boolean isFromManage, boolean isFromRest) {
        RequestContext context = getContext();
        if (Objects.isNull(context)) {
            context = new RequestContext();
        }
        context.setFromManage(isFromManage);
        context.setFromRest(isFromRest);
        setContext(context);
    }

    public static void initContextWithUser(User user) {
        if (Objects.isNull(user)) {
            user = new User();
        }
        RequestContext context = getContext();
        if (Objects.isNull(context)) {
            context = new RequestContext();
        }
        context.setUser(user);
        setContext(context);
    }

    public static void initContext() {
        RequestContext context = getContext();
        if (Objects.isNull(context)) {
            setContext(new RequestContext());
        }
    }

    public static void initContextForIsFromRest(boolean isFromRest) {
        RequestContext context = getContext();
        if (Objects.isNull(context)) {
            context = new RequestContext();
        }
        context.setFromRest(isFromRest);
        setContext(context);
    }

    public static void initContextForIsIncomplete(boolean isIncomplete) {
        RequestContext context = getContext();
        if (Objects.isNull(context)) {
            context = new RequestContext();
        }
        context.setIncomplete(isIncomplete);
        setContext(context);
    }

    public static boolean isFromManager() {
        return Objects.nonNull(THREAD_LOCAL.get()) && THREAD_LOCAL.get().isFromManage();
    }

    public static boolean isFromRest() {
        return Objects.nonNull(THREAD_LOCAL.get()) && THREAD_LOCAL.get().isFromRest();
    }

    public static boolean isIncomplete() {
        return Objects.nonNull(THREAD_LOCAL.get()) && THREAD_LOCAL.get().isIncomplete();
    }

    public static User getUser() {
        if (Objects.isNull(getContext())) {
            return new User();
        }
        return getContext().getUser();
    }

    public static void setContext(RequestContext requestContext) {
        if (requestContext == null) {
            return;
        }
        THREAD_LOCAL.set(requestContext);
    }

    public static RequestContext getContext() {
        return THREAD_LOCAL.get();
    }


    public static void removeContext() {
        THREAD_LOCAL.remove();
    }

}
