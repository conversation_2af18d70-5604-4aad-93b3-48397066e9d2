package com.facishare.webpage.customer.dao.entity;


import lombok.Data;
import org.mongodb.morphia.annotations.*;

/**
 * 文件实体类
 */
@Data
@Indexes({
        @Index(fields = {@Field("tenantId"), @Field("apiName")}
                , options = @IndexOptions(unique = true, dropDups = true, background = true)),
        @Index(fields = {@Field("tenantId"), @Field("status")},
                options = @IndexOptions(dropDups = true, background = true)),
        @Index(fields = {@Field("tenantId"), @Field("workSpace")},
                options = @IndexOptions(dropDups = true, background = true)),
})
public class FileEntity {

    /**
     * 主键ID
     */
    @Id
    private String id;

    /**
     * 租户ID
     */
    @Property("tenantId")
    private Integer tenantId;

    /**
     * 文件ApiName，唯一标识
     */
    @Property("apiName")
    private String apiName;

    /**
     * 文件类型：file-文件，folder-文件夹
     */
    @Property("type")
    private String type;

    /**
     * 文件名称
     */
    @Property("name")
    private String name;

    /**
     * 文件路径(NPath)
     */
    @Property("path")
    private String path;

    /**
     * 文件签名
     */
    @Property("signature")
    private String signature;

    @Property("extra")
    private String extra;

    /**
     * MIME类型
     */
    @Property("mimeType")
    private String mimeType;

    /**
     * 所属工作区ApiName
     */
    @Property("workSpace")
    private String workSpace;

    /**
     * 上级文件夹ApiName，为空表示根目录
     */
    @Property("parentApiName")
    private String parentApiName;

    @Property("description")
    private String description;

    /**
     * 状态：1-启用，0-禁用
     */
    @Property("status")
    private Integer status;


    /**
     * 创建人ID
     */
    @Property("creatorId")
    private String creatorId;

    /**
     * 创建时间
     */
    @Property("createTime")
    private Long createTime;

    /**
     * 节点类型
     */
    @Property("nodeType")
    private String nodeType;

    /**
     * 更新人ID
     */
    @Property("updaterId")
    private String updaterId;

    /**
     * 更新时间
     */
    @Property("updateTime")
    private Long updateTime;
}
