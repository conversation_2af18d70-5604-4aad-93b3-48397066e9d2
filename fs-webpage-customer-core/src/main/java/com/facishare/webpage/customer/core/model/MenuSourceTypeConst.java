package com.facishare.webpage.customer.core.model;

/**
 * Created by <PERSON><PERSON> on 19/12/12.
 */
public interface MenuSourceTypeConst {
    /**
     * 来源配置中心
     */
    int CONFIG_MENU_TYPE = 1;
    /**
     * 来源CRM对象
     */
    int CRM_MENU_TYPE = 2;
    /**
     * 来源互联CRM对象
     */
    int CROSS_CRM_MENU_TYPE = 3;
    /**
     * 来源页面模板数据, 包括web和app
     */
    int PAGE_MENU_TYPE = 4;
    /**
     * 来源互联应用
     */
    int CROSS_APP_MENU_TYPE = 5;
    /**
     * 自定义菜单项
     */
    int CUSTOMER_MENU_TYPE = 6;
    /**
     * 企业内应用
     */
    int INNER_APP_MENU_TYPE = 7;
    /**
     * 来源于H5页面
     */
    int H5_PAGE = 8;
    /**
     * 新建对象
     */
    int NEW_CREATE_OBJECT = 9;
    /**
     * 互联部分应用
     */
    int SECTION_CROSS_APP_MENU_TYPE = 10;

    /**
     * 严格自定义页面模板（不包含H5页面）
     */
    int CUSTOMER_PAGE_MENU_TYPE = 12;

    /**
     * PaaS应用
     */
    int PAAS_APP_MENU_TYPE = 13;

    /**
     * 自定义互联应用 关联的 对象
     */
    int CUSTOMER_LINK_APP_OBJECT_TYPE = 14;

    /**
     * 自定义待办
     */
    int CUSTOMER_TODO_TYPE = 15;
    /**
     * 快消的自定义页面
     */
    int  KUAIXIAO_CUSTOMER_PAGE_MENU_TYPE = 16;
}
