package com.facishare.webpage.customer.service.impl;

import com.facishare.webpage.customer.api.constant.ErrorMessageI18NKey;
import com.facishare.webpage.customer.api.exception.ValidateException;

import com.facishare.webpage.customer.controller.model.I18nInfoDTO;
import com.facishare.webpage.customer.api.model.SiteLangDTO;
import com.facishare.webpage.customer.core.service.I18nService;
import com.facishare.webpage.customer.dao.ReferenceEntityDao;
import com.facishare.webpage.customer.dao.entity.ReferenceEntity;
import com.facishare.webpage.customer.service.SiteI18nQueryService;
import com.facishare.webpage.customer.util.ReferenceTargetType;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 站点多语服务实现
 */
@Service
@Slf4j
public class SiteI18nQueryServiceImpl implements SiteI18nQueryService {

    @Autowired
    private ReferenceEntityDao referenceEntityDao;

    @Autowired
    private I18nService i18nService;

    @Override
    public List<I18nInfoDTO> queryI18nInfoForSite(Integer tenantId, String siteApiName, List<SiteLangDTO> langList) {
        if (tenantId == null || StringUtils.isBlank(siteApiName)) {
            log.warn("Invalid parameters for querying i18n info. tenantId: {}, siteApiName: {}", tenantId, siteApiName);
            return Collections.emptyList();
        }

        // 检查站点是否支持多语功能
        if (!isSiteMultilingualEnabled(langList)) {
            log.debug("Site {} does not support multilingual features, returning empty list", siteApiName);
            return Collections.emptyList();
        }


        try {
            List<ReferenceEntity> references = referenceEntityDao.findBySiteApiName(tenantId, siteApiName);
            List<ReferenceEntity> i18nReferenceList = references.stream()
                    .filter(x -> ReferenceTargetType.I18N.getCode().equals(x.getTargetType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(i18nReferenceList)) {
                return Collections.emptyList();
            }
            //暂时不考虑同一个页面中的两个组件使用同一个i18nKey的情况
            Map<String, ReferenceEntity> referenceEntityMap = i18nReferenceList.stream()
                    .collect(Collectors.toMap(ReferenceEntity::getTargetId, x -> x, (x1, x2) -> x1));
            List<String> i18nKeys = i18nReferenceList.stream().map(ReferenceEntity::getTargetId).collect(Collectors.toList());

            // 获取站点关联的多语key列表
            if (CollectionUtils.isEmpty(i18nKeys)) {
                log.debug("No i18n keys found for site: {}", siteApiName);
                return Collections.emptyList();
            }

            // 从翻译工作台查询多语翻译
            Map<String, Localization> localizations = i18nService.getTransValue(tenantId, i18nKeys);
            if (MapUtils.isEmpty(localizations)) {
                log.warn("No translations found for site: {} with {} keys", siteApiName, i18nKeys.size());
                return Collections.emptyList();
            }

            // 转换为I18nInfoDTO列表
            return convertLocalizationsToI18nInfo(localizations, langList, referenceEntityMap);

        } catch (Exception e) {
            log.error("Failed to query i18n info for site: {}", siteApiName, e);
            throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_I18N_QUERY_FAILED, siteApiName);
        }
    }

    @Override
    public boolean isSiteMultilingualEnabled(List<SiteLangDTO> langList) {
        return CollectionUtils.isNotEmpty(langList);
    }

    @Override
    public List<String> getSupportedLanguageCodes(List<SiteLangDTO> langList) {
        if (CollectionUtils.isEmpty(langList)) {
            return Collections.emptyList();
        }

        return langList.stream()
                .map(SiteLangDTO::getLang)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 将Localization转换为I18nInfoDTO列表
     *
     * @param localizations      本地化信息映射
     * @param langList           站点语言配置列表
     * @param referenceEntityMap
     * @return I18nInfoDTO列表
     */
    private List<I18nInfoDTO> convertLocalizationsToI18nInfo(Map<String, Localization> localizations,
                                                             List<SiteLangDTO> langList,
                                                             Map<String, ReferenceEntity> referenceEntityMap) {
        if (MapUtils.isEmpty(localizations) || CollectionUtils.isEmpty(langList) || com.facishare.webpage.customer.core.util.CollectionUtils.empty(referenceEntityMap)) {
            return Collections.emptyList();
        }

        List<String> supportedLangs = getSupportedLanguageCodes(langList);
        List<I18nInfoDTO> result = new ArrayList<>();

        for (Map.Entry<String, Localization> entry : localizations.entrySet()) {
            String key = entry.getKey();
            Localization localization = entry.getValue();

            if (localization == null) {
                log.warn("Localization is null for key: {}", key);
                continue;
            }
            ReferenceEntity referenceEntity = referenceEntityMap.get(key);
            if (Objects.isNull(referenceEntity)) {
                log.warn("referenceEntity is null for key: {}", key);
                continue;
            }

            // 提取支持语言的翻译信息
            Map<String, String> languageInfo = new HashMap<>();
            for (String lang : supportedLangs) {
                String translation = localization.get(lang, null);
                if (StringUtils.isNotBlank(translation)) {
                    languageInfo.put(lang, translation);
                }
            }

            if (MapUtils.isEmpty(languageInfo)) {
                log.debug("No translations found for key: {} in supported languages", key);
                continue;
            }

            I18nInfoDTO i18nInfo = I18nInfoDTO.builder()
                    .key(key)
                    .sourceType(referenceEntity.getSourceType())
                    .sourceId(referenceEntity.getSourceId())
                    .dimension(getDimension(referenceEntity))
                    .superApiName(getSuperApiNameByDimension(getDimension(referenceEntity), referenceEntity))
                    .languageInfo(languageInfo)
                    // 注意：dimension、sourceId、sourceType、superApiName等信息无法从Localization中获取
                    // 这些信息需要从其他地方获取或重新构建
                    .build();

            result.add(i18nInfo);
        }

        return result;
    }

    private String getSuperApiNameByDimension(String dimension, ReferenceEntity referenceEntity) {
        if (StringUtils.isBlank(dimension)) {
            return "";
        }
        String sourceId = referenceEntity.getSourceId();
        if (StringUtils.isBlank(sourceId)) {
            return "";
        }
        String[] cascadeList = sourceId.split("/");
        if (CollectionUtils.isEmpty(Arrays.asList(cascadeList))) {
            return "";
        }
        Map<String, String> result = Maps.newHashMap();
        for (String cascade : cascadeList) {
            if (StringUtils.isEmpty(cascade)) {
                continue;
            }
            String[] split = cascade.split(":");
            if (CollectionUtils.isEmpty(Arrays.asList(split)) || split.length < 2) {
                continue;
            }
            result.put(split[0], split[1]);
        }

        return StringUtils.defaultString(result.get(dimension), "");
    }

    private String getDimension(ReferenceEntity referenceEntity) {
        String sourceId = referenceEntity.getSourceType();
        if (StringUtils.isBlank(sourceId)) {
            return "";
        }
        String[] cascadeTypeList = sourceId.split("/");
        if (CollectionUtils.isEmpty(Arrays.asList(cascadeTypeList))) {
            return "";
        }
        if (cascadeTypeList.length < 3) {
            return "";
        }
        return cascadeTypeList[1];
    }

    @Override
    public void syncI18nInfoForSite(Integer tenantId, String siteApiName, List<I18nInfoDTO> i18nInfoList) {
        if (tenantId == null) {
            log.warn("Invalid parameters for syncing i18n info. tenantId: {}", tenantId);
            return;
        }

        if (CollectionUtils.isEmpty(i18nInfoList)) {
            log.debug("No i18n info to sync");
            return;
        }

        try {

            // 2. 转换为TransArg格式并同步到翻译工作台
            syncToTranslationWorkbench(tenantId, i18nInfoList);
        } catch (Exception e) {
            log.error("Failed to sync i18n info to translation workbench", e);
            throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_I18N_SYNC_FAILED, "translation workbench");
        }
    }

    /**
     * 验证多语信息列表
     *
     * @param i18nInfoList 多语信息列表
     */
    private void validateI18nInfoList(List<I18nInfoDTO> i18nInfoList) {
        for (I18nInfoDTO i18nInfo : i18nInfoList) {
            try {
                i18nInfo.validate();
            } catch (IllegalArgumentException e) {
                throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_I18N_VALIDATION_FAILED, e.getMessage());
            }
        }
    }

    /**
     * 同步到翻译工作台
     *
     * @param tenantId     租户ID
     * @param i18nInfoList 多语信息列表
     */
    private void syncToTranslationWorkbench(Integer tenantId, List<I18nInfoDTO> i18nInfoList) {
        // 转换为TransArg格式
        List<Localization> localizations = convertToLocalization(i18nInfoList, tenantId);
        if (CollectionUtils.isNotEmpty(localizations)) {
            // 调用I18nService同步到翻译工作台
            i18nService.syncTransValue(tenantId, localizations);
            log.debug("Successfully synced {} trans args to translation workbench", localizations.size());
        }
    }

    private List<Localization> convertToLocalization(List<I18nInfoDTO> i18nInfoList, Integer tenantId) {
        return i18nInfoList.stream().map(x -> {
            Localization localization = new Localization();
            localization.setKey(x.getKey());
            localization.setTags(Lists.newArrayList("server"));
            x.getLanguageInfo().forEach(localization::set);
            localization.setTenantId(tenantId);
            return localization;
        }).collect(Collectors.toList());
    }
}
