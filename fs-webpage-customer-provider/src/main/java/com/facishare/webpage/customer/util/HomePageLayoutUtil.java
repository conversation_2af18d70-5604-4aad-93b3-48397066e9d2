package com.facishare.webpage.customer.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.I18N;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.constant.CustomerLayoutField;
import com.facishare.webpage.customer.api.constant.SourceType;
import com.facishare.webpage.customer.api.constant.Status;
import com.facishare.webpage.customer.api.model.HomePageLayoutCard;
import com.facishare.webpage.customer.api.model.HomePageLayoutFilter;
import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.facishare.webpage.customer.api.model.HomePageLayoutTool;
import com.facishare.webpage.customer.api.utils.I18NKey;
import com.facishare.webpage.customer.constant.DataVersion;
import com.facishare.webpage.customer.core.util.ScopesUtil;
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity;
import com.facishare.webpage.customer.model.PageData;
import com.facishare.webpage.customer.model.UserHomePageLayoutAO;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2019/9/14
 */
public class HomePageLayoutUtil {

    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();

    private static final List<String> defaultScope = Lists.newArrayList("D-999999");

    public static final String searchPriceToolID = "SFA_Tool_SearchPrice";
    public static final String PAGE_LAYOUT_TYPE = "pageLayoutType";
    public static final int PAGE_LAYOUT_TYPE_INT = 0;


    public static HomePageLayoutTO buildHomePageLayoutItem(HomePageLayoutEntity homePageLayoutEntity) {
        if (homePageLayoutEntity == null) {
            return null;
        }
        List<HomePageLayoutCard> homePageLayoutCards = Lists.newArrayList();
        Set<String> homePageCards = new HashSet<>(homePageLayoutEntity.getHomePageCardEntityList());
        homePageCards.forEach(homePageCardEntity -> {
            HomePageLayoutCard homePageLayoutCard = gson.fromJson(homePageCardEntity, HomePageLayoutCard.class);
            if (CollectionUtils.isNotEmpty(homePageLayoutCard.getHomePageLayoutFilters())) {
                Set<HomePageLayoutFilter> homePageLayoutFilters = new HashSet<>(homePageLayoutCard.getHomePageLayoutFilters());
                homePageLayoutCard.setHomePageLayoutFilters(new ArrayList<>(homePageLayoutFilters));
            }
            if (CollectionUtils.isNotEmpty(homePageLayoutCard.getHomePageLayoutTools())) {
                Set<HomePageLayoutTool> homePageLayoutTools = new HashSet<>(homePageLayoutCard.getHomePageLayoutTools());
                homePageLayoutCard.setHomePageLayoutTools(addHomePageLayoutTool(new ArrayList<>(homePageLayoutTools)));
            }
            homePageLayoutCard.setApiName(homePageLayoutCard.getCardId());
            homePageLayoutCards.add(homePageLayoutCard);
        });
        List<HomePageLayoutCard> pageLayoutCards = homePageLayoutCards.stream().sorted(Comparator.comparing(HomePageLayoutCard::getOrder)).collect(Collectors.toList());
        HomePageLayoutTO homePageLayoutTO = new HomePageLayoutTO();
        homePageLayoutTO.setLayoutId(homePageLayoutEntity.getLayoutId());
        homePageLayoutTO.setLayoutApiName(homePageLayoutEntity.getApiName());
        homePageLayoutTO.setAppId(homePageLayoutEntity.getAppId());
        homePageLayoutTO.setAppType(homePageLayoutEntity.getAppType());
        homePageLayoutTO.setTempleId(homePageLayoutEntity.getAppTemplateId());
        homePageLayoutTO.setLayoutType(homePageLayoutEntity.getLayoutType());
        homePageLayoutTO.setName(homePageLayoutEntity.getName());
        homePageLayoutTO.setDescription(homePageLayoutEntity.getDescription());
        if (CollectionUtils.isNotEmpty(homePageLayoutEntity.getScopes())) {
            Set<String> scopes = new HashSet<>(homePageLayoutEntity.getScopes());
            homePageLayoutTO.setScopes(ScopesUtil.resoleStringScope(new ArrayList<>(scopes)));
        }
        homePageLayoutTO.setStatus(homePageLayoutEntity.getStatus());
        if (SourceType.SYSTEM.equals(homePageLayoutEntity.getSourceType())) {
            homePageLayoutTO.setSystem(true);
            if (BizType.CRM.getType() == homePageLayoutEntity.getAppType() && !homePageLayoutEntity.getScopes().equals(defaultScope)) {
                homePageLayoutTO.setSystem(false);
            }
        }
        if (BizType.BI.getType() == homePageLayoutEntity.getAppType()) {
            homePageLayoutTO.setApiName(homePageLayoutEntity.getAppId());
        }

        if (homePageLayoutEntity.getDataVersion() == DataVersion.dataVersion_200) {
            homePageLayoutTO.setCustomerLayout(JSONObject.parseObject(homePageLayoutEntity.getCustomerLayout()));
        }
        homePageLayoutTO.setHomePageLayouts(pageLayoutCards);

        homePageLayoutTO.setPageLayoutType(homePageLayoutEntity.getPageLayoutType());
        homePageLayoutTO.setDataVersion(homePageLayoutEntity.getDataVersion());
        if (BizType.CUSTOMER.getType() == homePageLayoutEntity.getAppType()) {
            homePageLayoutTO.setCreateTime(homePageLayoutEntity.getCreateTime().getTime());
            homePageLayoutTO.setUpdateTime(homePageLayoutEntity.getUpdateTime().getTime());
            homePageLayoutTO.setLayoutApiName(homePageLayoutEntity.getApiName());
        }
        if (homePageLayoutEntity.getPageLayoutType() == 0) {
            homePageLayoutTO.setPageLayoutType(3);
        }
        homePageLayoutTO.setPriorityLevel(homePageLayoutEntity.getPriorityLevel());
        List<JSONObject> customerLayoutList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(homePageLayoutEntity.getCustomerLayoutList())) {
            homePageLayoutEntity.getCustomerLayoutList().forEach(item -> {
                JSONObject jsonObject = JSONObject.parseObject(item);
                if (null != jsonObject) {
                    if (CustomerLayoutField.LABEL_INDEX_STRING.equals(jsonObject.getString(CustomerLayoutField.LABEL_INDEX))) {
                        jsonObject.put(PAGE_LAYOUT_TYPE, null != jsonObject.getInteger(PAGE_LAYOUT_TYPE) ? jsonObject.getInteger(PAGE_LAYOUT_TYPE) : PAGE_LAYOUT_TYPE_INT);
                    }
                    customerLayoutList.add(jsonObject);
                }
            });
        } else {
            JSONObject jsonObject = JSON.parseObject(homePageLayoutEntity.getCustomerLayout());
            if (null != jsonObject) {
                if (StringUtils.isBlank(jsonObject.getString(CustomerLayoutField.LABEL_PAGE_NAME))) {
                    jsonObject.put(CustomerLayoutField.LABEL_PAGE_NAME, I18N.text(I18NKey.CUSTOMIZE_PAGE));
                }
                jsonObject.put(PAGE_LAYOUT_TYPE, homePageLayoutEntity.getPageLayoutType());
                jsonObject.put(CustomerLayoutField.LABEL_INDEX, CustomerLayoutField.LABEL_INDEX_STRING);
                customerLayoutList.add(jsonObject);
            }
        }
        homePageLayoutTO.setCustomerLayoutList(customerLayoutList);
        homePageLayoutTO.setDefaultLabelIndex(homePageLayoutEntity.getDefaultLabelIndex());
        homePageLayoutTO.setPageMultiType(homePageLayoutEntity.getPageMultiType());
        if (homePageLayoutEntity.getIconIndex() == null) {
            homePageLayoutTO.setIconIndex(1);
        } else {
            homePageLayoutTO.setIconIndex(homePageLayoutEntity.getIconIndex());
        }
        homePageLayoutTO.setFromOldCrmHomePage(homePageLayoutEntity.getFromOldCrmHomePage());
        homePageLayoutTO.setSourceType(homePageLayoutEntity.getSourceType());
        return homePageLayoutTO;
    }

    public static List<HomePageLayoutTool> addHomePageLayoutTool(List<HomePageLayoutTool> homePageLayoutTools) {

        Map<String, HomePageLayoutTool> homePageLayoutToolMap = homePageLayoutTools.stream().
                collect(Collectors.toMap(HomePageLayoutTool::getToolID, x -> x, (key1, key2) -> key1));
        if (homePageLayoutToolMap.get(searchPriceToolID) == null) {
            homePageLayoutTools.add(buildSearchPrice());
        }
        return homePageLayoutTools;
    }

    private static HomePageLayoutTool buildSearchPrice() {
        HomePageLayoutTool homePageLayoutTool = new HomePageLayoutTool();
        {
            homePageLayoutTool.setToolID(searchPriceToolID);
            homePageLayoutTool.setToolName(I18N.text(I18NKey.INQUIRY_TOOL));
            homePageLayoutTool.setToolType(3);
            homePageLayoutTool.setShow(true);
            homePageLayoutTool.setUrl("");
        }
        return homePageLayoutTool;
    }

    public static HomePageLayoutEntity buildHomePageLayoutEntity(int appType, String appId, int employeeId, Integer applyType, HomePageLayoutTO homePageLayoutTO) {
        HomePageLayoutEntity homePageLayoutEntity = new HomePageLayoutEntity();
        {
            Date date = new Date();
            homePageLayoutEntity.setLayoutId(homePageLayoutTO.getLayoutId());
            homePageLayoutEntity.setApiName(StringUtils.isEmpty(homePageLayoutTO.getLayoutApiName()) ? homePageLayoutTO.getLayoutId() : homePageLayoutTO.getLayoutApiName());
            homePageLayoutEntity.setAppType(appType);
            homePageLayoutEntity.setAppId(appId);
            homePageLayoutEntity.setLayoutType(homePageLayoutTO.getLayoutType());
            homePageLayoutEntity.setAppTemplateId(homePageLayoutTO.getTempleId());
            homePageLayoutEntity.setApplyType(applyType);
            homePageLayoutEntity.setChange(true);
            homePageLayoutEntity.setScopes(ScopesUtil.buildScopesToString(homePageLayoutTO.getScopes()));
            homePageLayoutEntity.setName(homePageLayoutTO.getName());
            homePageLayoutEntity.setDescription(homePageLayoutTO.getDescription());
            if (homePageLayoutTO.isSystem()) {
                homePageLayoutEntity.setSourceType(SourceType.SYSTEM);
            } else {
                homePageLayoutEntity.setSourceType(SourceType.CUSTOMER);
            }
            homePageLayoutEntity.setStatus(homePageLayoutTO.getStatus());
            homePageLayoutEntity.setCreatorId(employeeId);
            homePageLayoutEntity.setCreateTime(date);
            homePageLayoutEntity.setUpdaterId(employeeId);
            homePageLayoutEntity.setUpdateTime(date);
            if (null == homePageLayoutTO.getIconIndex()) {
                homePageLayoutEntity.setIconIndex(1);
            } else {
                homePageLayoutEntity.setIconIndex(homePageLayoutTO.getIconIndex());
            }
            if (homePageLayoutTO.getDataVersion() == DataVersion.dataVersion_100) {
                homePageLayoutEntity.setHomePageCardEntityList(buildCards(homePageLayoutTO.getHomePageLayouts()));
            } else if (homePageLayoutTO.getDataVersion() == DataVersion.dataVersion_200) {
                homePageLayoutEntity.setCustomerLayout(JSONObject.toJSONString(homePageLayoutTO.getCustomerLayout()));
            }
            homePageLayoutEntity.setPageLayoutType(homePageLayoutTO.getPageLayoutType());
            homePageLayoutEntity.setDataVersion(homePageLayoutTO.getDataVersion());
            homePageLayoutEntity.setPriorityLevel(homePageLayoutTO.getPriorityLevel());
            if (applyType != null) {
                homePageLayoutEntity.setApplyType(applyType);
            }
            List<JSONObject> customerLayoutList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(homePageLayoutTO.getCustomerLayoutList())) {
                customerLayoutList.addAll(homePageLayoutTO.getCustomerLayoutList());
            } else {
                customerLayoutList.add(homePageLayoutTO.getCustomerLayout());
            }
            homePageLayoutEntity.setCustomerLayoutList(customerLayoutList.stream().map(item -> JSONObject.toJSONString(item)).collect(Collectors.toList()));
            homePageLayoutEntity.setPageMultiType(homePageLayoutTO.getPageMultiType());
            homePageLayoutEntity.setDefaultLabelIndex(homePageLayoutTO.getDefaultLabelIndex());
        }
        return homePageLayoutEntity;
    }

    private static List<String> buildCards(List<HomePageLayoutCard> homePageLayoutCards) {
        if (CollectionUtils.isEmpty(homePageLayoutCards)) {
            return Lists.newArrayList();
        }
        List<String> homePageCards = homePageLayoutCards.stream().map(homePageLayoutCard -> gson.toJson(homePageLayoutCard)).collect(Collectors.toList());
        return homePageCards;
    }

    public static List<PageData> buildCusMenuItems(List<HomePageLayoutEntity> homePageLayoutEntityList) {

        if (CollectionUtils.isEmpty(homePageLayoutEntityList)) {
            return Lists.newArrayList();
        }
        List<PageData> pageDataList = Lists.newArrayList();
        homePageLayoutEntityList.stream().filter(x -> {
            if (Status.TEMPORARY != x.getStatus()) {
                return true;
            } else {
                return false;
            }
        }).forEach(homePageLayoutEntity -> {
            PageData pageData = new PageData();
            pageData.setLayoutApiName(homePageLayoutEntity.getApiName());
            pageData.setDisplayName(homePageLayoutEntity.getName());
            pageData.setActive(homePageLayoutEntity.getStatus() == Status.DISABLE ? false : true);
            if (null == homePageLayoutEntity.getIconIndex()) {
                //返回默认图标
                pageData.setIconIndex(1);
            } else {
                pageData.setIconIndex(homePageLayoutEntity.getIconIndex());
            }
            pageDataList.add(pageData);
        });
        return pageDataList;
    }

    public static UserHomePageLayoutAO covert2UserHomePageLayoutAO(HomePageLayoutEntity homePageLayoutEntity) {
        if (homePageLayoutEntity == null) {
            return null;
        }
        return UserHomePageLayoutAO.builder().
                layoutId(homePageLayoutEntity.getLayoutId()).
                appType(homePageLayoutEntity.getAppType()).
                appId(homePageLayoutEntity.getAppId()).
                layoutType(homePageLayoutEntity.getLayoutType()).
                status(homePageLayoutEntity.getStatus()).
                templeId(homePageLayoutEntity.getAppTemplateId()).
                customerLayout(JSON.parseObject(homePageLayoutEntity.getCustomerLayout())).
                customerLayoutList(homePageLayoutEntity.getCustomerLayoutList().stream().map(item -> JSON.parseObject(item)).collect(Collectors.toList())).
                dataVersion(homePageLayoutEntity.getDataVersion()).
                pageLayoutType(homePageLayoutEntity.getPageLayoutType()).
                sourceType(homePageLayoutEntity.getSourceType()).
                sourceId(homePageLayoutEntity.getSourceId()).
                pageMultiType(homePageLayoutEntity.getPageMultiType()).
                build();
    }

    public static HomePageLayoutTO covert2HomePageLayoutTO(int enterpriseId, UserHomePageLayoutAO userHomePageLayoutAO) {
        if (userHomePageLayoutAO == null) {
            return null;
        }
        HomePageLayoutTO homePageLayoutTO = new HomePageLayoutTO();
        homePageLayoutTO.setLayoutId(StringUtils.isEmpty(userHomePageLayoutAO.getLayoutId()) ? TempleIdUtil.buildId(enterpriseId) : userHomePageLayoutAO.getLayoutId());
        homePageLayoutTO.setStatus(userHomePageLayoutAO.getStatus());
        homePageLayoutTO.setLayoutType(userHomePageLayoutAO.getLayoutType());
        homePageLayoutTO.setTempleId(userHomePageLayoutAO.getTempleId());
        homePageLayoutTO.setCustomerLayout(userHomePageLayoutAO.getCustomerLayout());
        homePageLayoutTO.setDataVersion(userHomePageLayoutAO.getDataVersion());
        homePageLayoutTO.setPageLayoutType(userHomePageLayoutAO.getPageLayoutType());
        homePageLayoutTO.setCreateTime(System.currentTimeMillis());
        homePageLayoutTO.setUpdateTime(System.currentTimeMillis());
        homePageLayoutTO.setAppId(userHomePageLayoutAO.getAppId());
        homePageLayoutTO.setAppType(userHomePageLayoutAO.getAppType());
        homePageLayoutTO.setPageMultiType(userHomePageLayoutAO.getPageMultiType());
        homePageLayoutTO.setCustomerLayoutList(userHomePageLayoutAO.getCustomerLayoutList());

        return homePageLayoutTO;
    }

}
