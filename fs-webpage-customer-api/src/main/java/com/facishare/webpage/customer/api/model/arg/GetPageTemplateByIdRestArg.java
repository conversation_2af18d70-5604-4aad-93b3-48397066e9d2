package com.facishare.webpage.customer.api.model.arg;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * Created by zhangyu on 2019/9/19
 */
@Data
public class GetPageTemplateByIdRestArg implements Serializable {

    private String templeId;
    private Integer enterpriseId;
    private String enterpriseAccount;
    private Integer employeeId;

    public void valid() throws WebPageException {
        if (StringUtils.isEmpty(templeId)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }

}
