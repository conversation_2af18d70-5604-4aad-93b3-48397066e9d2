package com.facishare.webpage.customer.core.language;

import com.facishare.webpage.customer.core.model.ComponentDto;

import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ComponentLanguageService {

    /**
     * 获取组件的多语
     *
     * @param tenantId
     * @param componentDtoList
     * @param locale
     * @return
     */
    Map<String, String> queryComponentLanguage(int tenantId, List<ComponentDto> componentDtoList, Locale locale);

}
