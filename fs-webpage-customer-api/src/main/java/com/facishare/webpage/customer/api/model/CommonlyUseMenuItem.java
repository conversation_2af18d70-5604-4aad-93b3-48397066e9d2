package com.facishare.webpage.customer.api.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/14.
 */
@Data
public class CommonlyUseMenuItem implements Serializable {
    @JSONField(name = "M1")
    private String displayName;

    @JSONField(name = "M2")
    private String referenceApiname;

    @JSONField(name = "M3")
    private String iconPathHome;

    @JSONField(name = "M4")
    private List<String> privilegeAction = Lists.newArrayList();

    @JSONField(name = "M5")
    private Integer number;

    @J<PERSON><PERSON>ield(name = "M6")
    private MobileConfig mobileConfig;

    @JSONField(name = "M7")
    private List<String> i18nKeyList = Lists.newArrayList();

}
