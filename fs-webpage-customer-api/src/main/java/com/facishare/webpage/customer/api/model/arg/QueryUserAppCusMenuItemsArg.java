package com.facishare.webpage.customer.api.model.arg;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2022/2/14 6:04 下午
 */
@Data
@Builder
public class QueryUserAppCusMenuItemsArg implements Serializable {

    private int enterpriseId;
    private int oldEnterpriseId;
    private Integer employeeId;
    private Long outTenantId;
    private Long outUid;
    private Integer identityType;
    private String appId;
    private List<String> menuApiNames;
    private Locale locale;
    private String menuApiName;

}
