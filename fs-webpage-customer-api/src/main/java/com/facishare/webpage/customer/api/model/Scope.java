package com.facishare.webpage.customer.api.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2019/10/18
 */
@Data
public class Scope  implements Serializable {
    @JSONField(name = "M1")
    @SerializedName("DataID")
    private String dataId;
    @JSONField(name = "M2")
    @SerializedName("DataType")
    private int dataType;

}
