package com.facishare.webpage.customer.api.service;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/11/26
 */
@RestResource(
        value = "WebPageResource",
        desc = "首页/web自定义页面相关rest接口",//ignoreI18n
        contentType = "application/json"
)
public interface HomePageRestService {

    @POST(value = "/webPage/homePageRestService/makeHomePageFormal", desc = "")
    MakeHomePageFormalResult makeHomePageFormal(@HeaderParam("x-fs-ei") String tenantId,@Body MakeHomePageFormalArg arg);

    @POST(value = "/webPage/homePageRestService/modifyHomePageLayout", desc = "")
    ModifyHomePageLayoutResult modifyHomePageLayout(@HeaderParam("x-fs-ei") String tenantId,@Body ModifyHomePageLayoutArg arg);

    @POST(value = "/webPage/homePageRestService/getHomePageLayoutTransList")
    HomePageLayoutTransListResult getHomePageLayoutTransList(@HeaderParam("x-fs-ei") String tenantId,    // 总线路由会用到参数
                                                             @Body HomePageLayoutTransListArg arg);

    @POST(value = "/webPage/homePageRestService/getHomePageLayoutById", desc = "")
    GetHomePageLayoutByIdResult getHomePageLayoutById(@HeaderParam("x-fs-ei") String tenantId,@Body GetHomePageLayoutByIdArg arg);

    @POST(value = "/webPage/homePageRestService/setHomePageLayoutStatus", desc = "")
    SetHomePageLayoutStatusResult setHomePageLayoutStatus(@HeaderParam("x-fs-ei") String tenantId,@Body SetHomePageLayoutStatusArg arg);

    @POST(value = "/webPage/homePageRestService/getHomePageWidgetsById", desc = "")
    GetHomePageWidgetsByIdResult getHomePageWidgetsById(@HeaderParam("x-fs-ei") String tenantId, @Body GetHomePageWidgetsByIdArg arg);

    @POST(value = "/webPage/homePageRestService/getHomePageLabelNameById", desc = "顶导航翻译")
    GetHomePageLabelNameByIdResult getHomePageLabelNameById(@HeaderParam("x-fs-ei") String tenantId, @Body GetHomePageWidgetsByIdArg arg);

}
