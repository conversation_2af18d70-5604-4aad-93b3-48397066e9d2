package com.facishare.webpage.customer.controller.model.vo.cms;

import lombok.Data;

/**
 * 资源信息VO
 */
@Data
public class ResourceInfoVO {

    /**
     * 文件ApiName
     */
    private String apiName;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 文件类型：file-文件，folder-文件夹
     */
    private String type;

    /**
     * MIME类型
     */
    private String mimeType;

    /**
     * 所属工作区ApiName
     */
    private String workSpace;

    /**
     * 节点类型 dir：文件夹 file：文件
     */
    private String nodeType;
    /**
     * 上级文件夹ApiName，为空表示根目录
     */
    private String parentApiName;

    private String url;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新人ID
     */
    private String updaterId;

    /**
     * 更新时间
     */
    private Long updateTime;
}
