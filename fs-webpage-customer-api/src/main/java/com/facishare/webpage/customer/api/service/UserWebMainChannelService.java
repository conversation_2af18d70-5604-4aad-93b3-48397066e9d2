package com.facishare.webpage.customer.api.service;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.arg.QueryUserWebMainChannelArg;
import com.facishare.webpage.customer.api.model.result.QueryUserWebMainChannelResult;

/**
 * Created by zhangyu on 2020/11/19
 */
@RestResource(
        value = "WebMainChannelResource",
        desc = "web主频道",    //ignoreI18n
        contentType = "application/json"
)
public interface UserWebMainChannelService {

    @POST(value = "/userWebMainChannel/queryUserWebMainChannelVO", desc = "获取web主频道")//ignoreI18n
    QueryUserWebMainChannelResult queryUserWebMainChannelVO(@HeaderParam("x-fs-ei") String tenantId,
                                                            @HeaderParam("x-fs-locale") String language,
                                                            @Body QueryUserWebMainChannelArg arg);

}
