package com.facishare.webpage.customer.api.service;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.arg.ListObjectByPageArg;
import com.facishare.webpage.customer.api.model.arg.ListUptreamLinkAppByPageArg;

import java.util.Map;

@RestResource(
        value = "WebPageResource",
        desc = "应用页面服务",//ignoreI18n
        contentType = "application/json"
)
public interface SynLinkDataRestService {


    @POST(value = "/webPage/LinkAppRestService/listUpstreamObjectByPage", desc = "批量删除互联应用关联对象")//ignoreI18n
    void listUpstreamObjectByPage(@HeaderMap Map<String, String> headers, @Body ListObjectByPageArg arg);

    @POST(value = "/webPage/LinkAppRestService/listObjectByPage", desc = "批量更新互联应用关联对象")//ignoreI18n
    void listObjectByPage(@HeaderMap Map<String, String> headers, @Body ListObjectByPageArg arg);

    @POST(value = "/webPage/LinkAppRestService/removeAllObject", desc = "批量更新互联应用关联对象")//ignoreI18n
    void removeAllObject(@HeaderMap Map<String, String> headers);

    @POST(value = "/webPage/LinkAppRestService/removeNullObject", desc = "批量更新互联应用关联对象")//ignoreI18n
    void removeNullObject(@HeaderMap Map<String, String> headers);

    @POST(value = "/webPage/LinkAppRestService/listPresetLinkApp", desc = "全量同步预置互联应用")//ignoreI18n
    void listPresetLinkApp(@HeaderMap Map<String, String> headers);

    @POST(value = "/webPage/LinkAppRestService/listUptreamLinkAppByPage", desc = "按条件同步自定义互联应用")//ignoreI18n
    void listUptreamLinkAppByPage(@HeaderMap Map<String, String> headers,@Body ListUptreamLinkAppByPageArg arg);

    @POST(value = "/webPage/LinkAppRestService/updateCustomerLinkAppScopeList", desc = "按条件同步自定义互联应用")//ignoreI18n
    void updateCustomerLinkAppScopeList(@HeaderMap Map<String, String> headers);

}
