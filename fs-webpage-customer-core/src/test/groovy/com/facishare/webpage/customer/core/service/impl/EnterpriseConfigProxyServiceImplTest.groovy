package com.facishare.webpage.customer.core.service.impl

import com.facishare.organization.adapter.api.config.model.GetConfigDto
import com.facishare.organization.adapter.api.config.model.SetConfigDto
import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService
import spock.lang.Specification
import spock.lang.Unroll

class EnterpriseConfigProxyServiceImplTest extends Specification {
    
    def enterpriseConfigService
    def service

    
    def setup() {
        enterpriseConfigService = Mock(EnterpriseConfigService)
        service = new EnterpriseConfigProxyServiceImpl(
            enterpriseConfigService: enterpriseConfigService
        )
    }

    @Unroll
    def "测试 isOpenNewCrm 方法 - 企业ID:#enterpriseId, 配置值:#configValue, 期望结果:#expectedResult"() {
        given:
        def argument = new GetConfigDto.Argument(
            currentEmployeeId: -10000,
            employeeId: -10000,
            enterpriseId: enterpriseId,
            key: "goNewCRM"
        )
        def result = Mock(GetConfigDto.Result)
        result.getValue() >> configValue
        
        when:
        def actualResult = service.isOpenNewCrm(enterpriseId)
        
        then:
        1 * enterpriseConfigService.getConfig(argument) >> result
        actualResult == expectedResult
        
        where:
        enterpriseId | configValue | expectedResult
        1           | "0"         | false    // 关闭状态
        2           | "1"         | true     // 开启状态
        3           | null        | true     // 空值默认开启
        -1          | "0"         | false    // 负数企业ID
        0           | "0"         | false    // 零值企业ID
        Integer.MAX_VALUE | "0"   | false    // 最大企业ID
    }
    
    def "测试 isOpenNewCrm 方法 - RPC调用异常场景"() {
        given:
        def enterpriseId = 1
        def argument = new GetConfigDto.Argument(
            currentEmployeeId: -10000,
            employeeId: -10000,
            enterpriseId: enterpriseId,
            key: "goNewCRM"
        )
        
        when:
        service.isOpenNewCrm(enterpriseId)
        
        then:
        1 * enterpriseConfigService.getConfig(argument) >> { throw new RuntimeException("RPC调用失败") }
        thrown(RuntimeException)
    }
    
    def "测试 openNewCrm 方法"() {
        given:
        def enterpriseId = 1
        def argument = new SetConfigDto.Argument(
            currentEmployeeId: -10000,
            employeeId: -10000,
            enterpriseId: enterpriseId,
            key: "goNewCRM",
            value: "1"
        )
        
        when:
        service.openNewCrm(enterpriseId)
        
        then:
        1 * enterpriseConfigService.setConfig(argument)
    }
    
    def "测试 closeNewCrm 方法"() {
        given:
        def enterpriseId = 1
        def argument = new SetConfigDto.Argument(
            currentEmployeeId: -10000,
            employeeId: -10000,
            enterpriseId: enterpriseId,
            key: "goNewCRM",
            value: "0"
        )
        
        when:
        service.closeNewCrm(enterpriseId)
        
        then:
        1 * enterpriseConfigService.setConfig(argument)
    }
} 