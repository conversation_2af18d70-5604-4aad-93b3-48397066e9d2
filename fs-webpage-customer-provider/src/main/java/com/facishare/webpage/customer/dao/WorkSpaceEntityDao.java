package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.dao.entity.WorkSpaceEntity;

import java.util.List;
import java.util.Set;

/**
 * 工作区DAO接口
 */
public interface WorkSpaceEntityDao {

    /**
     * 根据条件查询工作区列表
     *
     * @param condition 查询条件
     * @param offset    起始位置
     * @param limit     查询数量
     * @return 工作区列表
     */
    List<WorkSpaceEntity> findByCondition(WorkSpaceEntity condition, int offset, int limit);

    long getCountByCondition(WorkSpaceEntity condition);

    /**
     * 根据ApiName查询工作区
     *
     * @param tenantId 租户ID
     * @param apiName  工作区ApiName
     * @return 工作区信息
     */
    WorkSpaceEntity findByApiName(Integer tenantId, String apiName);

    List<WorkSpaceEntity> findWorkSpaceList(Integer tenantId, Set<String> apiNameList);

    List<WorkSpaceEntity> findWorkSpaceIncludesDeleted(Integer tenantId, Set<String> apiNameList);


    /**
     * 保存工作区
     *
     * @param user   用户信息
     * @param entity 工作区实体
     * @return 保存后的工作区实体
     */
    WorkSpaceEntity save(User user, WorkSpaceEntity entity);

    /**
     * 保存工作区
     *
     * @param entity 工作区实体
     */
    void save(WorkSpaceEntity entity);

    /**
     * 更新工作区
     *
     * @param user   用户信息
     * @param entity 工作区实体
     * @return 更新后的工作区实体
     */
    WorkSpaceEntity update(User user, WorkSpaceEntity entity);

    /**
     * 更新工作区
     *
     * @param entity 工作区实体
     */
    void update(WorkSpaceEntity entity);

    /**
     * 更新工作区状态
     *
     * @param user    用户信息
     * @param apiName 工作区ApiName
     * @param status  状态
     */
    void updateStatusByApiName(User user, String apiName, Integer status);

    /**
     * 删除工作区
     *
     * @param tenantId 租户ID
     * @param apiName  工作区ApiName
     */
    void delete(User user, String apiName);


    /**
     * 根据ApiName列表查询工作区
     *
     * @param tenantId 租户ID
     * @param apiNames ApiName列表
     * @return 工作区列表
     */
    List<WorkSpaceEntity> findByApiNames(Integer tenantId, List<String> apiNames);
}
