package com.facishare.webpage.customer.api.console.item;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PageTempleItem implements Serializable {
    private String templeId;
    private int tenantId;
    private String appId;
    private String type;
    private String name;
    private String description;
    private String sourceType;
    private String sourceId;
    private List<String> scopes;
    private String webMenuId;
    private String webPageId;
    private String appPageId;
    private int creatorId;
    private Long createTime;
    private int updaterId;
    private Long updateTime;
    private int status;
}
