package com.facishare.webpage.customer.controller.model.arg.cms;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.List;

/**
 * 启用资源参数
 */
@Data
public class EnableResourceArg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 启用资源列表
     */
    private List<EnableResourceItem> resourceList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EnableResourceItem {
        /**
         * 文件ApiName
         */
        private String apiName;

        /**
         * 是否递归启用子文件，仅当type=folder时有效，默认true
         */
        private Boolean recursive = true;
    }
}
