package com.facishare.webpage.customer.api.model.arg;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Locale;

@Data
public class GetlinkAppListArg implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer tenantId;
    //TODO 不传就是查预制和自定义互联应用
    private Integer type;
    //TODO
    private Integer status;
    private Integer pageSize;
    private Integer pageNum;
    private List<String> linkAppIds;
    private String roleId;
    private Locale local;
    // Locale.forLanguageTag
    // 中文是 zh_CN 不是 zh-CN
}
