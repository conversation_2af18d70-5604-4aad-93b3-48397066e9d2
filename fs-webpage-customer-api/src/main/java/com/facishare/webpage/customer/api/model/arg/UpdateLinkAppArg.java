package com.facishare.webpage.customer.api.model.arg;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.utils.UploadIcon;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@Data
public class UpdateLinkAppArg implements Serializable {

    private String appId;

    private List<Scope> scopeList;

    private String name;

    private String description;

    private String icon;
    /**
     * 预置icon统一使用iconIndex
     */
    private String iconIndex;

    //todo
    private int status;

    private String sourceType;

    /**
     * 是否允许该应用 使用个人级应用视图
     */
    private boolean useUserPageTempleFlag = false;
    /**
     * 上传图标内容
     */
    private UploadIcon uploadIcon;


    /**
     * 应用web端跳转地址 todo
     */
    private String weburl;

    //应用管理员，目前只有互联应用有用到
    private List<Scope> admins;

    private Boolean customIcon;     // 预置互联应用基础信息支持修改功能, 点击修改保存后该值即为true,  即使改之后再改回去, 也按照数据库的值处理

    public void valid() {
        if (StringUtils.isEmpty(appId) || CollectionUtils.isEmpty(scopeList) || StringUtils.isEmpty(name)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
