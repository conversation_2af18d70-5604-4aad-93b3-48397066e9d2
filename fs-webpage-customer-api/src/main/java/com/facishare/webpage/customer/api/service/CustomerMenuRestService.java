package com.facishare.webpage.customer.api.service;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.arg.QueryAppDropCusMenuItemsArg;
import com.facishare.webpage.customer.api.model.arg.QueryManAppCusMenuItemsArg;
import com.facishare.webpage.customer.api.model.arg.QueryUserAppCusMenuItemsArg;
import com.facishare.webpage.customer.api.model.result.QueryAppDropCusMenuItemsResult;
import com.facishare.webpage.customer.api.model.result.QueryManAppCusMenuItemsResult;
import com.facishare.webpage.customer.api.model.result.QueryUserAppCusMenuItemsResult;

/**
 * <AUTHOR>
 * @date 2022/2/15 10:20 上午
 */
@RestResource(
        value = "WebPageResource",
        desc = "自定义页面",  // ignoreI18n
        contentType = "application/json"
)
public interface CustomerMenuRestService {

    /**
     * 移动端获取自定义菜单项设计器数据
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/customerMenu/queryAppDropCusMenuItems", desc = "移动端获取自定义菜单项设计器数据")
    QueryAppDropCusMenuItemsResult queryAppDropCusMenuItems(@HeaderParam("x-fs-ei") String tenantId, @Body QueryAppDropCusMenuItemsArg arg);

    /**
     * 根据clientType获取自定义菜单项设计器数据
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/customerMenu/queryDropCusMenuItems", desc = "获取自定义菜单项设计器数据")
    QueryAppDropCusMenuItemsResult queryDropCusMenuItems(@HeaderParam("x-fs-ei") String tenantId, @Body QueryAppDropCusMenuItemsArg arg);


    /**
     * 移动端获取管理后台自定义菜单项数据
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/customerMenu/queryManAppCusMenuItems", desc = "移动端获取管理后台自定义菜单项数据")
    QueryManAppCusMenuItemsResult queryManAppCusMenuItems(@HeaderParam("x-fs-ei") String tenantId, @Body QueryManAppCusMenuItemsArg arg);

    /**
     * 移动端获取用户态自定义菜单项数据
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/customerMenu/queryUserAppCusMenuItems", desc = "移动端获取用户态自定义菜单项数据")
    QueryUserAppCusMenuItemsResult queryUserAppCusMenuItems(@HeaderParam("x-fs-ei") String tenantId, @Body QueryUserAppCusMenuItemsArg arg);

}
