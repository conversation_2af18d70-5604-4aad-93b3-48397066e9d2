package com.facishare.webpage.customer.controller.model.result.menu;

import com.facishare.webpage.customer.controller.model.arg.BaseArg;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetUserMenuModelArg extends BaseArg {


    private String key;

    @Override
    public void valid() throws Exception {

    }
}
