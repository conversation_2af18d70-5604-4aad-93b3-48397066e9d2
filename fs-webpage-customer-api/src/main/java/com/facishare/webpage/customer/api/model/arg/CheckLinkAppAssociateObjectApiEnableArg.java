package com.facishare.webpage.customer.api.model.arg;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CheckLinkAppAssociateObjectApiEnableArg implements Serializable {
    private String appId;
    private String action;
    private List<String> objectApiName;

    public interface Action {
        String ADD = "add";
        String REMOVE = "remove";
    }
}
