package com.facishare.webpage.customer.core.model;

import com.fxiaoke.release.GrayRule;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Range;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>ui
 * @Data : 2024/12/12
 *         "tenantEnvInfos": [
 *             {
 *                 "env": "FONESHARE",
 *                 "minEI": 1,
 *                 "nowEI": 801448
 *             }
 *         ],
 *         "manualTenantInfo": {
 *             "oldTenant": "deny:",
 *             "newTenant": "deny:74255"
 *         }
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TenantEnvInfo {
    String env;
    long minEI;
    long nowEI;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ManualTenantInfo {
        GrayRule newTenant; // 强制命中灰度
        GrayRule oldTenant; // 强制不命中灰度
    }

    public static boolean isManualEiOrNewTenant(String tenantId, ManualTenantInfo manualTenantInfo, List<TenantEnvInfo> tenantEnvInfos) {
        if (Objects.nonNull(manualTenantInfo)) {
            if (manualTenantInfo.getNewTenant().isAllow(tenantId)) {//手动，该企业强制灰度
                return true;
            }
            if (manualTenantInfo.getOldTenant().isAllow(tenantId)) {//手动，该企业走源逻辑
                return false;
            }
        }
        return isNewTenant(tenantId, tenantEnvInfos)    ;
    }

    private static boolean isNewTenant(String tenantId, List<TenantEnvInfo> tenantEnvInfos) {
        if (ObjectUtils.isEmpty(tenantId)) {
            return false;
        }
        if (tenantEnvInfos == null || tenantEnvInfos.isEmpty()) {
            return false;
        }
        long tenant = Long.parseLong(tenantId);
        for (TenantEnvInfo info : tenantEnvInfos) {
            if (Range.between(info.getMinEI(), info.getNowEI()).contains(tenant)) {
                return false;
            }
        }
        return true;
    }
}
