package com.facishare.webpage.customer.dao.entity;


import lombok.Data;
import org.mongodb.morphia.annotations.*;

import java.util.List;

/**
 * 工作区实体类
 */
@Data
@Indexes({
        @Index(fields = {@Field("tenantId"), @Field("apiName")}
                , options = @IndexOptions(unique = true, dropDups = true, background = true)),
        @Index(fields = {@Field("tenantId"), @Field("status")},
                options = @IndexOptions(dropDups = true, background = true)),
})
public class WorkSpaceEntity {

    /**
     * 主键ID
     */
    @Id
    private String id;

    /**
     * 租户ID
     */
    @Property("tenantId")
    private Integer tenantId;

    /**
     * 工作区名称
     */
    @Property("name")
    private String name;

    /**
     * 工作区ApiName，唯一标识
     */
    @Property("apiName")
    private String apiName;

    /**
     * 工作区描述
     */
    @Property("description")
    private String description;

    /**
     * 状态：0-启用，1-禁用, -1-删除
     */
    @Property("status")
    private Integer status;

    /**
     * 关联的频道列表
     */
    @Property("channelList")
    private List<String> channelList;

    /**
     * 创建人ID
     */
    @Property("creatorId")
    private String creatorId;

    /**
     * 创建时间
     */
    @Property("createTime")
    private Long createTime;

    /**
     * 更新人ID
     */
    @Property("updaterId")
    private String updaterId;

    /**
     * 更新时间
     */
    @Property("updateTime")
    private Long updateTime;
}
