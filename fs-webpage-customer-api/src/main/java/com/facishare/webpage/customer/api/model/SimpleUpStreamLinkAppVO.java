package com.facishare.webpage.customer.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SimpleUpStreamLinkAppVO {
    private int  id;
    private String linkAppId;
    private String upstreamEa;
    private Integer status; //企业应用状态  1-启用，2-禁用
    private Integer type;
    private List<String> roleIds;
    private Date createTime;
    private Date updateTime;

    private List<ScopeForCross> scopeListForCross;

}
