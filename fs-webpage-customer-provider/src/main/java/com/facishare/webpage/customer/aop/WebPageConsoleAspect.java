package com.facishare.webpage.customer.aop;

import com.facishare.webpage.customer.api.exception.WebPageException;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Aspect
public class WebPageConsoleAspect {
    private static Logger logger = LoggerFactory.getLogger(WebPageConsoleAspect.class);

    @Before(value = "execution(* com.facishare.webpage.customer.console..*.*(..))")
    public void beforeCall(JoinPoint joinPoint) {
        logger.info("before WebPageConsole method:{} Args:{}", joinPoint.getSignature().toShortString(), joinPoint.getArgs());
    }

    @AfterReturning(value = "execution(* com.facishare.webpage.customer.console..*.*(..))", returning = "ret")
    public void afterReturn(JoinPoint joinPoint, Object ret) {
        logger.info("afterReturn, WebPageConsole:{} arg:{} Return:{}",
                joinPoint.getSignature().toShortString(), joinPoint.getArgs(), ret);
    }

    @AfterThrowing(value = "execution(* com.facishare.webpage.customer.console..*.*(..))", throwing = "e")
    public void afterThrowing(JoinPoint joinPoint, Throwable e) throws Throwable {
        if (e instanceof WebPageException) {
            logger.warn("afterThrowing, WebPageConsole:{} ", joinPoint.getSignature().toShortString(), e);
        } else {
            logger.error("afterThrowing, WebPageConsole:{} ", joinPoint.getSignature().toShortString(), e);
            throw e;
        }
    }

}
