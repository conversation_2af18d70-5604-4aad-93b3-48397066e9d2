package com.facishare.webpage.customer.api.model.arg;

import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2021/10/8 10:01 上午
 */
@Data
public class GetUserMenuByIdRestArg  {

    private String menuId;
    private int pageTemplateType = 2;
    private Integer enterpriseId;
    private String enterpriseAccount;
    private Integer employeeId;
    private ClientTypeEnum type;
    private String version;
    private String deviceId;
    private Locale locale;
    private String osVersion;

    public void valid() throws WebPageException {
        if (StringUtils.isEmpty(menuId)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
