package com.facishare.webpage.customer.dao.entity;

import java.util.Date;
import java.util.List;

import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.constant.ClientType;
import com.facishare.webpage.customer.api.constant.SourceType;
import com.facishare.webpage.customer.api.constant.Status;
import com.facishare.webpage.customer.api.model.LayoutType;
import com.google.common.collect.Lists;

import lombok.Data;

/**
 * Created by zhangyu on 2019/9/2
 */
@Data
@Entity(value = "HomePageLayout", noClassnameStored = true)
@Indexes({
        @Index(fields = {@Field("tenantId"), @Field("scopes"), @Field("status")},
                options = @IndexOptions(name = "tenantId_1_scopes_1_status_1", background = true)),

        @Index(fields = {@Field("appId"), @Field("status"), @Field("layoutType")},
                options = @IndexOptions(name = "appId_1_status_1_layoutType_1", background = true)),

        @Index(fields = {@Field("tenantId"), @Field("appType"), @Field("appId"), @Field("status"), @Field("applyType")},
                options = @IndexOptions(name = "telentId_1_appType_1_appId_1_status_1_applyType_1", background = true))
})

//TODO 补索引
public class HomePageLayoutEntity implements com.facishare.qixin.sysdb.model.Data {

    @Override
    public String getDataId() {
        return layoutId;
    }

    @Override
    public void setDataId(String layoutId) {
        this.layoutId = layoutId;
    }

    @Override
    public void setSystem(boolean systemFlag) {
        this.sourceType = systemFlag ? SourceType.SYSTEM : SourceType.CUSTOMER;
    }

    /**
     * 首页ID
     */
    @Id
    private String layoutId;
    /**
     * 企业ID
     */
    @Property("tenantId")
    private int tenantId;
    /**
     * 自定义页面的apiName
     */
    @Property("apiName")
    private String apiName;
    /**
     * 应用Id
     */
    @Property("appId")
    private String appId;
    /**
     * 首页类型
     *
     * @see BizType
     */
    @Property("appType")
    private int appType;
    /**
     * 首页应用Id
     */
    @Property("appTemplateId")
    private String appTemplateId;
    /**
     * 首页类型
     *
     * @see LayoutType
     */
    @Property("layoutType")
    private int layoutType;
    /**
     * 首页的优先级
     */
    @Property("priorityLevel")
    private int priorityLevel;
    /**
     * 自定义页面类型
     * 0：企业内
     * 1：互联
     */
    @Property("applyType")
    private Integer applyType;
    /**
     * 首页适用范围
     */
    @Property("scopes")
    private List<String> scopes = Lists.newArrayList();
    /**
     * 卡片集合
     */
    @Property("homePageCardEntityList")
    private List<String> homePageCardEntityList = Lists.newArrayList();
    /**
     * 首页来源
     *
     * @see SourceType
     */
    @Property("sourceType")
    private String sourceType;
    /**
     * 首页来源Id
     */
    @Property("sourceId")
    private String sourceId;
    /**
     * 首页状态
     *
     * @see Status
     * (Status不完全)
     */
    @Property("status")
    private int status;
    /**
     * 首页名称
     */
    @Property("name")
    private String name;
    /**
     * 首页描述
     */
    @Property("description")
    private String description;
    /**
     * 预置模板是否被修改过
     */
    @Property("isChange")
    private boolean isChange;
    /**
     * 创建人ID
     */
    @Property("creatorId")
    private int creatorId;
    /**
     * 创建时间
     */
    @Property("createTime")
    private Date createTime;
    /**
     * 最后修改人ID
     */
    @Property("updaterId")
    private int updaterId;
    /**
     * 最后修改时间
     */
    @Property("updateTime")
    private Date updateTime;
    /**
     * 自定义页面
     */
    @Property("customerLayout")
    private String customerLayout;
    /**
     * 自定义页面版本
     */
    @Property("dataVersion")
    private int dataVersion = 100;
    /**
     * 页面布局类型
     */
    @Property("pageLayoutType")
    private int pageLayoutType = 3;

    /**
     * 顶导航开启 1开启  0未开启
     */
    @Property("pageMultiType")
    private int pageMultiType;
    /**
     * 默认标签页index
     */
    @Property("defaultLabelIndex")
    private int defaultLabelIndex;
    /**
     * 所有标签页数据信息，每一项为customerLayout"，此项考虑和customerLayout冗余，customerLayout作为往前兼容的冗余数据
     */
    @Property("customerLayoutList")
    private List<String> customerLayoutList;

    @Property("iconIndex")
    private Integer iconIndex = 0;

    @Property("fromOldCrmHomePage")
    private Boolean fromOldCrmHomePage = false;

    @Property("siteApiName")
    private String siteApiName;

    @Property("themeLayoutApiName")
    private String themeLayoutApiName;

    @Property("isHomePage")
    private Boolean isHomePage;

    @Property("objectApiName")
    private String objectApiName;

    @Property("needLogin")
    private Boolean needLogin;

    @Property("clientType")
    private String clientType = ClientType.web.getValue();

    @Property("themeStyleApiName")
    private String themeStyleApiName;
}
