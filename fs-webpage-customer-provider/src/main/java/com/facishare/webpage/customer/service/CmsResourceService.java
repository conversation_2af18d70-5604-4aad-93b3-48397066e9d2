package com.facishare.webpage.customer.service;

import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.controller.model.arg.cms.*;
import com.facishare.webpage.customer.controller.model.result.cms.ConvertResourceResult;
import com.facishare.webpage.customer.controller.model.result.cms.OperationResult;
import com.facishare.webpage.customer.controller.model.result.cms.ResourceListResult;
import com.facishare.webpage.customer.controller.model.result.cms.ValidateFileStatusResult;
import com.facishare.webpage.customer.dao.entity.FileEntity;

import java.util.List;
import java.util.Map;

/**
 * CMS资源管理服务接口
 */
public interface CmsResourceService {

    /**
     * 根据工作区ApiName查询资源列表
     *
     * @param arg      工作区相关信息
     * @return 资源列表
     */
    ResourceListResult getResourceListByWorkSpace(User user, GetResourceListArg arg);


    /**
     * 获取文件的url
     *
     * @param user
     * @param fileEntities
     * @return
     */
    Map<String, String> getFileUrl(User user, List<FileEntity> fileEntities);

    /**
     * 批量保存文件信息
     *
     * @param user 用户信息
     * @param args 文件信息列表
     * @return 文件ApiName列表
     */
    void batchSaveFileInfo(User user, SaveFileInfoArg args);

    /**
     * 批量删除资源
     *
     * @param user 用户信息
     * @param args 删除参数列表
     */
    List<OperationResult.OperationInfo> batchDeleteResource(User user, DeleteResourceArg args);

    /**
     * 移动资源
     *
     * @param user 用户信息
     * @param args 移动参数
     */
    void moveResource(User user, MoveResourceArg args);

    /**
     * 更新资源信息
     *
     * @param user 用户信息
     * @param arg 更新参数
     */
    void updateResource(User user, UpdateResourceArg arg);

    /**
     * 启用资源
     *
     * @param user 用户信息
     * @param args 启用参数
     */
    void enableResource(User user, EnableResourceArg args);

    /**
     * 禁用资源
     *
     * @param user 用户信息
     * @param args 禁用参数
     */
    void disableResource(User user, DisableResourceArg args);

    /**
     * 根据apiName转换资源获取URL
     *
     * @param user          用户信息
     * @param outerUserInfo 外部用户信息
     * @return 资源URL
     */
    List<ConvertResourceResult.ConvertResourceInfo> convertResourceByApiName(User user, OuterUserInfo outerUserInfo, List<String> apiNameList);

    List<FileEntity> findFileEntityByApiNames(User user, List<String> fileApiNameList);


    List<FileEntity> findByApiNamesIncludesDelete(User user, List<String> fileApiNameList);

    List<ValidateFileStatusResult.ValidateFileStatusInfo> validateFileStatus(User user, OuterUserInfo outerUserInfo, List<String> apiNameList);
}
