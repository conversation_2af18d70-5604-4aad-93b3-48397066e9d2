package com.facishare.webpage.customer.core.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NSaveFileFromTempFile;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.core.model.CalculateUrlArg;
import com.facishare.webpage.customer.core.service.FileService;
import com.fxiaoke.stone.commons.SystemPresetClient;
import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class FileServiceImpl implements FileService {

    private static final String CMS = "CMS";
    public static final String TN = "TN_";

    private static final Long EXPIRED_TIME = 3000l;
    @Resource
    private NFileStorageService nFileStorageService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private SystemPresetClient metadataExclusiveClient;
    @Autowired
    private SystemPresetClient appFrameworkExclusiveClient;

    @Override
    public Map<String, String> batchTnConvertNPath(User user, Set<String> tNPathSet) {
        if (CollectionUtils.isEmpty(tNPathSet) || Objects.isNull(user)) {
            return Maps.newHashMap();
        }
        String ea = eieaConverter.enterpriseIdToAccount(user.getTenantId());
        Map<String, String> resultMap = Maps.newHashMap();
        for (String tempFile : tNPathSet) {
            if (!tempFile.startsWith(TN)) {
                resultMap.put(tempFile, tempFile);
            }
            try {
                NSaveFileFromTempFile.Arg nFileArg = new NSaveFileFromTempFile.Arg();
                nFileArg.setTempFileName(tempFile);
                nFileArg.setEa(ea);
                nFileArg.setBusiness(CMS);
                NSaveFileFromTempFile.Result result = nFileStorageService.nSaveFileFromTempFile(nFileArg, ea);
                resultMap.put(tempFile, result.getFinalNPath());
            } catch (Exception e) {
                log.warn("tNPathToNPath error by enterpriseAccount : {}, tempFile : {}", ea, tempFile, e);
            }
        }
        return resultMap;
    }

    @Override
    public Map<String, String> batchGetSignatureByNPath(User user, Collection<String> nPathList) {
        if (CollectionUtils.isEmpty(nPathList)) {
            return Maps.newHashMap();
        }
        Map<String, String> resultMap = Maps.newHashMap();
        for (String nPath : nPathList) {
            Optional<String> signature = metadataExclusiveClient.generateSignature(user.getTenantId(), nPath);
            signature.ifPresent(x -> resultMap.put(nPath, x));
        }
        return resultMap;
    }

    @Override
    public Map<String, String> batchCalculateUrl(User user, List<CalculateUrlArg> args) {
        Map<String, String> result = Maps.newHashMap();
        for (CalculateUrlArg arg : args) {
            try {
                String url = appFrameworkExclusiveClient.generateUrl(paramToString(user.getTenantId()), paramToString(user.getUserId()),
                        paramToString(user.getOutTenantId()), paramToString(user.getOutUserId()), paramToString(user.getUpstreamOwnerId())
                        , AuthModel.SIGN, arg.getPath(), arg.getSignature(), EXPIRED_TIME, arg.getName(), arg.getExtension());
                result.put(getGeneralUrlKey(arg.getPath(), arg.getSignature()), url);
            } catch (Exception e) {
                log.error("generateUrl error! arg:{}", arg);
            }
        }
        return result;
    }

    private String paramToString(Object param){
        if (Objects.isNull(param)) {
            return null;
        }
        return String.valueOf(param);
    }
    public static String getGeneralUrlKey(String path, String signature) {
        return path + "\\|" + signature;
    }
}
