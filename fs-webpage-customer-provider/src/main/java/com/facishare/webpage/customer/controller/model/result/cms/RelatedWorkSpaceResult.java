package com.facishare.webpage.customer.controller.model.result.cms;

import com.facishare.webpage.customer.controller.model.vo.cms.WorkSpaceVO;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 关联工作区列表结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RelatedWorkSpaceResult {


    /**
     * 关联工作区列表
     */
    @Builder.Default
    private List<WorkSpaceVO> workspaceList = Lists.newArrayList();

    @Builder.Default
    Long totalNum = 0L;
    @Builder.Default
    Integer pageNum = 1;
    @Builder.Default
    Integer pageSize = 20;

}
