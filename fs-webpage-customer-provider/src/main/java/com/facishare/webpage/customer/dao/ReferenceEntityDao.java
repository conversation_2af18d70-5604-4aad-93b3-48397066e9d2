package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.dao.entity.ReferenceEntity;
import com.facishare.webpage.customer.util.ReferenceTargetType;

import java.util.List;
import java.util.Set;

/**
 * 引用关系DAO接口
 */
public interface ReferenceEntityDao {

    /**
     * 根据站点ApiName查询引用关系
     *
     * @param tenantId    租户ID
     * @param siteApiName 站点ApiName
     * @return 引用关系列表
     */
    List<ReferenceEntity> findBySiteApiName(Integer tenantId, String siteApiName);

    /**
     * 根据目标对象查询引用关系
     *
     * @param tenantId   租户ID
     * @param targetType 目标对象类型
     * @param targetId   目标对象ID
     * @return 引用关系列表
     */
    List<ReferenceEntity> findByTargetId(Integer tenantId, String targetType, String targetId);

    /**
     * 根据源对象查询引用关系
     *
     * @param tenantId   租户ID
     * @param sourceType 源对象类型
     * @param sourceId   源对象ID
     * @return 引用关系列表
     */
    List<ReferenceEntity> findBySource(Integer tenantId, String sourceType, String sourceId);

    /**
     * 根据引用者和目标对象查询引用关系
     *
     * @param tenantId   租户ID
     * @param sourceType 引用者类型
     * @param sourceId   引用者ID
     * @param targetType 目标对象类型
     * @param targetId   目标对象ID
     * @return 引用关系实体
     */
    ReferenceEntity findBySourceAndTarget(Integer tenantId, String sourceType, String sourceId,
                                          String targetType, String targetId);

    /**
     * 保存引用关系
     *
     * @param entity 引用关系实体
     */
    void save(ReferenceEntity entity);

    /**
     * 批量保存引用关系
     *
     * @param entities 引用关系实体列表
     */
    void batchSave(List<ReferenceEntity> entities);

    List<ReferenceEntity> findReferenceByCondition(ReferenceEntity referenceEntity);

    /**
     * 根据ID删除引用关系
     *
     * @param id 引用关系ID
     */
    void delete(String id);

    /**
     * 批量删除引用关系
     *
     * @param ids 引用关系ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 根据目标对象删除引用关系
     *
     * @param tenantId   租户ID
     * @param targetType 目标对象类型
     * @param targetId   目标对象ID
     */
    void deleteByTarget(Integer tenantId, String targetType, String targetId);

    /**
     * 批量删除目标对象相关的引用关系
     *
     * @param tenantId   租户ID
     * @param targetType 目标对象类型
     * @param targetIds  目标对象ID列表
     */
    void batchDeleteByTarget(Integer tenantId, String targetType, List<String> targetIds);

    void batchUpdate(User user, List<ReferenceEntity> referenceEntities);

    /**
     * 根据源对象删除引用关系
     *
     * @param tenantId   租户ID
     * @param sourceType 源对象类型
     * @param sourceId   源对象ID
     */
    void deleteBySource(Integer tenantId, String sourceType, String sourceId);

    /**
     * 根据资源ID查询相关的工作区ApiName列表
     *
     * @param tenantId     租户ID
     * @param resourceType 资源类型
     * @param resourceId   资源ID
     * @return 工作区ApiName列表
     */
    List<String> findWorkSpaceApiNamesByResourceId(Integer tenantId, String resourceType, String resourceId);

    /**
     * 检查是否存在与指定对象相关的资源
     *
     * @return 是否存在相关资源
     */
    boolean hasRelatedResources(String tenantId, ReferenceTargetType targetType, Set<String> targetIdList, String sourceType, Set<String> sourceIdList);


    List<String> filterHasRelatedTargetIds(String tenantId, ReferenceTargetType targetType, Set<String> targetIdList, String sourceType, Set<String> sourceIdList);
}
