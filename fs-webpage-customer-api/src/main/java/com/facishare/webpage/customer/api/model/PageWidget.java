package com.facishare.webpage.customer.api.model;

import com.alibaba.fastjson.JSONArray;
import com.fxiaoke.i18n.client.api.Localization;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageWidget {

    private String pageName;
    private String pageId;
    @Deprecated
    private String i18Key;
    private String widgetName;
    private String apiName;
    private String widgetType;
    @Deprecated // 弃用, 暂留防止bug
    private Localization preTransValue;   // 预置翻译值, 由于needTrans和transValue两个都需要查预置的, 返回所有语言环境的
    @Deprecated // 弃用, 暂留防止bug
    private boolean edit = false;   // 组件名称是否编辑过, 用于决定是否展示预置翻译
    private List<I18nInfo> i18nInfos;

    private String defaultTranslateKey;
    private List<String> preTranslateKeys;
    private String dataId;
    private JSONArray containerComponentExtra;
}
