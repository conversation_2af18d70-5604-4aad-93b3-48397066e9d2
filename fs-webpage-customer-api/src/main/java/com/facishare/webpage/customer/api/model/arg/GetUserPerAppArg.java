package com.facishare.webpage.customer.api.model.arg;

import com.facishare.open.oauth.model.enums.AccessTypeEnum;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetUserPerAppArg {
    /**
     * 员工Id
     */
    private int employeeId;
    /**
     * 租户Id
     */
    private int tenantId;
    /**
     * 适用端
     */
    List<AccessTypeEnum> accessTypeList;

    public void valid() {
        if (tenantId < 0 || CollectionUtils.isEmpty(accessTypeList) || employeeId < 0) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
