package com.facishare.webpage.customer.brush.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/11/22 4:26 下午
 */
public interface MenuIconBrush {

    @Data
    class Arg implements Serializable {
        private Integer fromTenantId;
        private Integer toTenantId;
    }

    @Data
    @Builder
    class Result implements Serializable {
        private Integer count;
    }

}
