package com.facishare.webpage.customer.controller.model.vo.cms;

import lombok.Data;
import java.util.List;

/**
 * 工作区VO
 */
@Data
public class WorkSpaceVO {

    /**
     * 工作区ApiName
     */
    private String apiName;

    /**
     * 工作区名称
     */
    private String name;

    /**
     * 工作区描述
     */
    private String description;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 关联的频道列表
     */
    private List<String> channelList;

    /**
     * 创建人ID
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新人ID
     */
    private String updaterId;

    /**
     * 更新时间
     */
    private Long updateTime;
}
