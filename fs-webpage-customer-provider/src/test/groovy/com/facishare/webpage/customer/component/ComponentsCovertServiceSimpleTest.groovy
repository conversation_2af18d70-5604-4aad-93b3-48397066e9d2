package com.facishare.webpage.customer.component

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.facishare.cep.plugin.model.UserInfo
import com.facishare.webpage.customer.api.model.core.customelayout.Filter
import com.facishare.webpage.customer.component.impl.ComponentsCovertServiceImpl
import com.facishare.webpage.customer.constant.ComponentConstant
import com.facishare.webpage.customer.core.util.WebPageGraySwitch
import com.facishare.webpage.customer.service.HomePageCommonService
import com.fxiaoke.release.FsGrayReleaseBiz
import org.powermock.reflect.Whitebox
import spock.lang.Specification

/**
 * 简化的ComponentsCovertServiceImpl测试
 * 专注于验证核心功能
 */
class ComponentsCovertServiceSimpleTest extends Specification {

    ComponentsCovertServiceImpl componentsCovertService
    HomePageCommonService homePageCommonService

        def setup() {
        homePageCommonService = Mock(HomePageCommonService)
        
        componentsCovertService = new ComponentsCovertServiceImpl()
        componentsCovertService.homePageCommonService = homePageCommonService
        
        // Mock WebPageGraySwitch静态方法
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(WebPageGraySwitch, "gray", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(_, _) >> false // 默认非灰度用户
    }

    def "测试covertFilterComponent基本功能"() {
        given: "准备基本测试数据"
        def userInfo = new UserInfo()
        userInfo.enterpriseId = 123

        def existingFilter = new Filter()
        existingFilter.filterType = "existingType"
        existingFilter.filterData = "existingData"

        def filterJsonArray = new JSONArray()
        def newFilterJson = new JSONObject()
        newFilterJson.put(ComponentConstant.filterType, "newType")
        newFilterJson.put(ComponentConstant.filterData, "newData")
        filterJsonArray.add(newFilterJson)

        def filterJsonObject = new JSONObject()
        filterJsonObject.put(ComponentConstant.FILTERS, filterJsonArray)
        filterJsonObject.put("apiName", "testFilterComponent")
        filterJsonObject.put("propsType", "filters")

        def filterJSONObjects = [filterJsonObject]

        when: "调用covertFilterComponent方法"
        homePageCommonService.convertFilter(userInfo, null, "testApi") >> [existingFilter]
        def result = componentsCovertService.covertFilterComponent(userInfo, null, "testApi", filterJSONObjects)

        then: "验证基本结果"
        result != null
        result.size() == 1

        def resultComponent = result[0]
        resultComponent != null

        // 验证组件结构
        def componentKeys = resultComponent.keySet()
        componentKeys.size() > 0

        def componentApiName = componentKeys.iterator().next()
        def componentData = resultComponent.getJSONObject(componentApiName)
        componentData != null
        componentData.containsKey("api_name") // api_name可能为null
        componentData.containsKey("type")

        def props = componentData.getJSONObject("props")
        props != null

        def filters = props.getJSONArray(ComponentConstant.FILTERS)
        filters != null
        filters.size() >= 1 // 至少有一个filter
    }

    def "测试灰度用户不调用convertFilter"() {
        given: "灰度用户"
        def userInfo = new UserInfo()
        userInfo.enterpriseId = 123

        def filterJsonObject = new JSONObject()
        filterJsonObject.put(ComponentConstant.FILTERS, new JSONArray())
        filterJsonObject.put("apiName", "testFilterComponent")
        filterJsonObject.put("propsType", "filters")
        def filterJSONObjects = [filterJsonObject]

        when: "调用covertFilterComponent方法"
        // 在when块中设置为灰度用户
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(WebPageGraySwitch, "gray", fsGrayReleaseBiz)

        fsGrayReleaseBiz.isAllow(_, _) >> true

        def result = componentsCovertService.covertFilterComponent(userInfo, null, "testApi", filterJSONObjects)

        then: "验证不调用convertFilter"
        0 * homePageCommonService.convertFilter(_, _, _)
        result != null
        result.size() == 1
    }

    def "测试空数据处理"() {
        given: "空数据"
        def userInfo = new UserInfo()
        userInfo.enterpriseId = 123

        when: "传入空列表"
        def result = componentsCovertService.covertFilterComponent(userInfo, null, "testApi", [])

        then: "返回空结果"
        result != null
        result.size() == 0
    }

    def "测试JSON序列化性能对比"() {
        given: "性能测试数据"
        def testSize = 100
        def filterJsonArray = new JSONArray()

        for (int i = 0; i < testSize; i++) {
            def filterJson = new JSONObject()
            filterJson.put(ComponentConstant.filterType, "type_${i}")
            filterJson.put(ComponentConstant.filterData, "data_${i}")
            filterJsonArray.add(filterJson)
        }

        when: "测试原始方法性能"
        def startTime1 = System.currentTimeMillis()
        filterJsonArray.forEach { filter ->
            JSONObject filterJsonObject = JSONObject.parseObject(JSONObject.toJSONString(filter))
            JSONObject.toJavaObject(filterJsonObject, Filter.class)
        }
        def originalTime = System.currentTimeMillis() - startTime1

        and: "测试优化方法性能"
        def startTime2 = System.currentTimeMillis()
        filterJsonArray.forEach { filter ->
            JSONObject filterJsonObject = extractJsonObjectOptimized(filter)
            createFilterFromJsonOptimized(filterJsonObject)
        }
        def optimizedTime = System.currentTimeMillis() - startTime2

        then: "验证性能提升"
        println "原始方法: ${originalTime}ms, 优化方法: ${optimizedTime}ms"
        optimizedTime <= originalTime || originalTime < 5 // 允许小数据量时差异不明显
    }

    /**
     * 优化的JSON对象提取方法
     */
    private JSONObject extractJsonObjectOptimized(Object filter) {
        if (filter instanceof JSONObject) {
            return (JSONObject) filter
        } else if (filter instanceof String) {
            return JSONObject.parseObject((String) filter)
        } else {
            return (JSONObject) JSONObject.toJSON(filter)
        }
    }

    /**
     * 优化的Filter创建方法
     */
    private Filter createFilterFromJsonOptimized(JSONObject jsonObject) {
        if (jsonObject == null) {
            return null
        }

        Filter filter = new Filter()
        filter.setFilterType(jsonObject.getString(ComponentConstant.filterType))

        Object filterData = jsonObject.get(ComponentConstant.filterData)
        if (filterData != null) {
            if (filterData instanceof String) {
                filter.setFilterData((String) filterData)
            } else {
                filter.setFilterData(filterData.toString())
            }
        }

        return filter
    }
} 