package com.facishare.webpage.customer.core.config;

import com.alibaba.fastjson.JSON;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/12/1.
 */
public class IconPathConfig {
    private static Logger logger = LoggerFactory.getLogger(IconPathConfig.class);
    private static final String SVG_ICON_PATH_CONFIG_NAME = "fs-webpage-svg-icon-config";
    private static final String PNG_ICON_PATH_CONFIG_NAME = "fs-webpage-png-icon-config";
    private Map<String, String> svgIconPathMap = new HashMap<>();
    private Map<String, String> pngIconPathMap = new HashMap<>();

    public void init() {
        ConfigFactory.getConfig(SVG_ICON_PATH_CONFIG_NAME, this::loadSvgConfig);
        ConfigFactory.getConfig(PNG_ICON_PATH_CONFIG_NAME, this::loadPngConfig);
    }

    private void loadSvgConfig(IConfig config) {
        Map<String,String> iconPathMapNew = JSON.parseObject(config.getString(), Map.class);
        svgIconPathMap = iconPathMapNew;
        logger.info("init config : {} success svgIconPathMap:{}", SVG_ICON_PATH_CONFIG_NAME, svgIconPathMap);
    }

    private void loadPngConfig(IConfig config) {
        Map<String,String> iconPathMapNew = JSON.parseObject(config.getString(), Map.class);
        pngIconPathMap = iconPathMapNew;
        logger.info("init config : {} success svgIconPathMap:{}", PNG_ICON_PATH_CONFIG_NAME, pngIconPathMap);
    }

    public String getPngIconPath(String key) {
        return MapUtils.getString(pngIconPathMap, key, "");
    }

    public String getSvgIconPath(String key) {
        return MapUtils.getString(svgIconPathMap, key, "");
    }

}
