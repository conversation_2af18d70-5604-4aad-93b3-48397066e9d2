package com.facishare.webpage.customer.config;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
public class TenantUiConfig {

    private static JSONArray tenantUiConfig = new JSONArray();

    private static final String SKIN_COLORS = "skinColors";

    static {
        try {
            ConfigFactory.getConfig("fs-webpage-tenant-ui-config", config -> {
                JSONObject tenantUIConfig = JSONObject.parseObject(config.getString());
                JSONArray skinColors = tenantUIConfig.getJSONArray(SKIN_COLORS);
                if (Objects.nonNull(skinColors)) {
                    tenantUiConfig = skinColors;
                }
            });
        } catch (Exception e) {
            log.error("load fs-webpage-tenant-ui-config error", e);
        }
    }

    public static JSONArray getTenantUiConfig() {
        // 返回深拷贝，避免外部修改原始数据
        return JSONArray.parseArray(tenantUiConfig.toJSONString());
    }
}

