package com.facishare.webpage.customer.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Id;

import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LinkAppObjectAssociationOldVO {
    private Integer id;
    /**
     * 上游ea
     */
    private String upstreamEa;
    /**
     * 对象apiName
     */
    private String objectApiName;
    /**
     * 对象apiName
     */
    private List<String> objectApiNames;
    /**
     * 企业互联应用Id
     */
    private String linkAppId;
    /**
     * 企业互联应用Id
     */
    private List<String> linkAppIds;
    /**
     * 是否允许被删除
     */
    private Boolean allowRemove = Boolean.TRUE;
    /**
     * 对象状态 0:启用  1:禁用
     */
    private int objectStatus;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 不允许分配的业务类型名称列表 中间用英文 ',' 隔开
     */
    private String notRecordTypeNames;
    /**
     * 不允许分配的布局名称列表 中间用英文 ',' 隔开
     */
    private String notLayoutNames;
    /**
     * 对象label
     */
    private String objectLabel;
    /**
     * 是否内置
     */
    private Boolean inLay = Boolean.FALSE;
    /**
     * 是否需要分配业务类型、布局
     */
    private Boolean needAllocate = Boolean.TRUE;
    /**
     * 是否分配新建编辑页面布局
     */
    private Boolean needAllocateCreateEditLayout = Boolean.FALSE;
    /**
     * 是否分配移动端页面布局
     */
    private Boolean needAllocateMobileSummaryLayout = Boolean.FALSE;

    /**
     * 1:上游企业互联应用对象  2:预置互联应用对象
     */
    private Integer type;
}
