package com.facishare.webpage.customer.api.utils;

import com.facishare.common.parallel.ParallelUtils;
import com.facishare.paas.timezone.TimeZoneContext;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.github.trace.TraceContext;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by zhouwr on 2024/9/6.
 */
@Slf4j
public final class UiPaasParallelUtils {
    public static final int MAX_PARALLEL_NUM = 200;
    private static final ExecutorService executorService;
    private static final ExecutorService bakExecutorService;

    private static final String WEB_PAGE_PARALLEL_UTILS_THREAD_NAME_PREFIX = "WebPageParallelUtils";

    private static final String BAK_WEB_PAGE_PARALLEL_UTILS_THREAD_NAME_PREFIX = "BakWebPageParallelUtils";

    static {
        ThreadFactory workerFactory = new ThreadFactoryBuilder().setNameFormat(WEB_PAGE_PARALLEL_UTILS_THREAD_NAME_PREFIX + "-%d").setDaemon(true).build();
        executorService = new ThreadPoolExecutor(50, 200, 10000L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(1000), workerFactory, new ThreadPoolExecutor.CallerRunsPolicy());

        ThreadFactory bakWorkerFactory = new ThreadFactoryBuilder().setNameFormat(BAK_WEB_PAGE_PARALLEL_UTILS_THREAD_NAME_PREFIX + "-%d").setDaemon(true).build();
        bakExecutorService = new ThreadPoolExecutor(50, 100, 10000L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(500), bakWorkerFactory, new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Deprecated
    public static ParallelUtils.ParallelTask createParallelTask(String tenantId) {
        return createParallelTask();
    }

    public static ParallelUtils.ParallelTask createParallelTask() {
        return new ParallelTaskImpl(chooseExecutor());
    }

    private static ExecutorService chooseExecutor() {
        if (isCurrentThreadInThreadPool()) {
            return bakExecutorService;
        } else {
            return executorService;
        }
    }

    private static boolean isCurrentThreadInThreadPool() {
        return StringUtils.startsWith(Thread.currentThread().getName(), WEB_PAGE_PARALLEL_UTILS_THREAD_NAME_PREFIX);
    }

    private static boolean isCurrentThreadInBakThreadPool() {
        return StringUtils.startsWith(Thread.currentThread().getName(), BAK_WEB_PAGE_PARALLEL_UTILS_THREAD_NAME_PREFIX);
    }

    private UiPaasParallelUtils() {

    }

    private static class ParallelTaskImpl implements ParallelUtils.ParallelTask {
        private final List<Runnable> runnableList = new ArrayList<>();
        private final ExecutorService executor;

        private Map<String, String> mdcMap;
        private TraceContext traceContext;
        private TimeZoneContext timeZoneContext;
        private RequestContext requestContext;

        public ParallelTaskImpl(ExecutorService executor) {
            this.executor = executor;
            getMDC();
        }

        private void getMDC() {
            try {
                mdcMap = MDC.getCopyOfContextMap();
                traceContext = TraceContext.get().copy();
                timeZoneContext = TimeZoneContextHolder.getTimezoneContext();
                requestContext = RequestContextManager.getContext();
            } catch (Throwable e) {
                log.warn("getMDC", e);
            }
        }

        private void putMDC() {
            try {
                if (mdcMap != null) {
                    MDC.setContextMap(mdcMap);
                } else {
                    MDC.put("traceId", traceContext.getTraceId());
                    MDC.put("userId", traceContext.getUid());
                }
                TraceContext._set(traceContext);
                TimeZoneContextHolder.setTimezoneContext(timeZoneContext);
                RequestContextManager.setContext(requestContext);
            } catch (Throwable e) {
                log.warn("putMDC", e);
            }
        }

        private void clearMDC() {
            try {
                MDC.clear();
                TraceContext.remove();
                TimeZoneContextHolder.clearContext();
                RequestContextManager.removeContext();
            } catch (Throwable e) {
                log.warn("clearMDC", e);
            }
        }


        @Override
        public ParallelUtils.ParallelTask submit(Runnable runnable) {
            if (runnable != null) {
                if (runnableList.size() <= MAX_PARALLEL_NUM) {
                    runnableList.add(runnable);
                } else {
                    throw new ParallelTaskValidateException("Max Parallel Task Number:" + MAX_PARALLEL_NUM);
                }
            }
            return this;
        }

        @Override
        public boolean await(long timeout, TimeUnit timeUnit) throws TimeoutException {
            if (runnableList.isEmpty()) {
                return true;
            }

            //如果当前线程已经是备用线程池中的线程，就降级成串行执行，防止出现死锁
            if (isCurrentThreadInBakThreadPool()) {
                log.warn("submit by thread of bak_thread_pool,transfer to sequence process");
                runnableList.forEach(Runnable::run);
                return true;
            }

            final AtomicBoolean ret = new AtomicBoolean(true);
            CountDownLatch countDownLatch = new CountDownLatch(runnableList.size());

            for (Runnable runnable : runnableList) {
                try {
                    executor.submit(() -> {
                        try {
                            putMDC();
                            runnable.run();
                        } catch (WebPageException e) {
                            ret.compareAndSet(true, false);
                            log.warn("execute task error", e);
                        } catch (Throwable e) {
                            ret.compareAndSet(true, false);
                            log.error("execute task error", e);
                        } finally {
                            countDownLatch.countDown();
                            clearMDC();
                        }
                    });
                } catch (Throwable e) {
                    log.error("submit task error!", e);
                    throw e;
                }
            }

            try {
                boolean finished = countDownLatch.await(timeout, timeUnit);
                if (!finished) {
                    if (executor instanceof ThreadPoolExecutor) {
                        ThreadPoolExecutor poolExecutor = (ThreadPoolExecutor) executor;
                        throw new TimeoutException(String.format("execute task timeout,poolSize:%s,activeCount:%s,queueSize:%s",
                                poolExecutor.getPoolSize(), poolExecutor.getActiveCount(), poolExecutor.getQueue().size()));
                    } else {
                        throw new TimeoutException("execute task timeout");
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new TimeoutException("execute task interrupted");
            }

            return ret.get();
        }

        @Override
        public void run() {
            if (runnableList.isEmpty()) {
                return;
            }

            for (Runnable runnable : runnableList) {
                try {
                    executor.submit(() -> {
                        try {
                            putMDC();
                            runnable.run();
                        } catch (WebPageException e) {
                            log.warn("execute task failed", e);
                        } catch (Throwable e) {
                            log.error("execute task error", e);
                        } finally {
                            clearMDC();
                        }
                    });
                } catch (Throwable e) {
                    log.error("submit task error!", e);
                }
            }
        }

    }

    public static class ParallelTaskValidateException extends RuntimeException {
        public ParallelTaskValidateException(String message) {
            super(message);
        }
    }
}
