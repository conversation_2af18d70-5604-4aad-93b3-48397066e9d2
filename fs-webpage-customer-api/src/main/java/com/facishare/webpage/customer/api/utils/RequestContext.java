package com.facishare.webpage.customer.api.utils;

import com.facishare.webpage.customer.api.model.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RequestContext {
    private boolean isFromManage;
    private boolean isFromRest;
    @Builder.Default
    private boolean isIncomplete = false;
    @Builder.Default
    private User user = new User();
}
