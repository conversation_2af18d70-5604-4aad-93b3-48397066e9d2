package com.facishare.webpage.customer.service;

import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.controller.model.arg.cms.*;
import com.facishare.webpage.customer.controller.model.result.cms.OptionalWorkSpaceResult;
import com.facishare.webpage.customer.controller.model.result.cms.RelatedWorkSpaceResult;
import com.facishare.webpage.customer.controller.model.result.cms.WorkSpaceListResult;
import com.facishare.webpage.customer.controller.model.vo.cms.WorkSpaceVO;

/**
 * CMS工作区管理服务接口
 */
public interface CmsWorkSpaceService {

    /**
     * 查询工作区列表
     *
     * @param user
     * @param arg 查询参数
     * @return 工作区列表
     */
    WorkSpaceListResult findWorkSpaceList(User user, WorkSpaceListArg arg);

    /**
     * 根据ApiName查询工作区详情
     *
     * @param user 用户信息
     * @param apiName 工作区ApiName
     * @return 工作区详情
     */
    WorkSpaceVO findWorkSpaceInfoByApiName(User user, String apiName);

    /**
     * 查询关联工作区列表
     *
     * @param user 用户信息
     * @param arg 查询参数
     * @return 关联工作区列表
     */
    RelatedWorkSpaceResult findRelatedWorkSpace(User user, FindRelatedWorkSpaceArg arg);

    /**
     * 查询可选工作区列表
     *
     * @param user 用户信息
     * @param arg 查询参数
     * @return 可选工作区列表
     */
    OptionalWorkSpaceResult findOptionalWorkSpaceList(User user, OptionalWorkSpaceArg arg);

    /**
     * 创建工作区
     *
     * @param user
     * @param arg  创建参数
     */
    void createWorkSpace(User user, CreateWorkSpaceArg arg);

    /**
     * 更新工作区
     *
     * @param user
     * @param arg  更新参数
     */
    void updateWorkSpace(User user, UpdateWorkSpaceArg arg);

    /**
     * 删除工作区
     *
     * @param user
     * @param apiName 工作区ApiName
     */
    void deleteWorkSpace(User user, String apiName);

    /**
     * 启用工作区
     *
     * @param user
     * @param apiName 工作区ApiName
     */
    void enableWorkSpace(User user, String apiName);

    /**
     * 禁用工作区
     *
     * @param user
     * @param apiName 工作区ApiName
     */
    void disableWorkSpace(User user, String apiName);
}
