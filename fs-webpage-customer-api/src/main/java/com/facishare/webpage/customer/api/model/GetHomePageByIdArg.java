package com.facishare.webpage.customer.api.model;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.google.common.base.Strings;
import lombok.Data;

import java.io.Serializable;

@Data
public class GetHomePageByIdArg implements Serializable {

    private String enterpriseAccount;
    private int employeeId;
    private int appType;
    private int enterpriseId;
    private int oldEnterpriseId;
    private String layoutId;
    private String apiName;
    private String language;

    public void valid() {
        if (Strings.isNullOrEmpty(layoutId)) {
            throw new WebPageException(InterErrorCode.HOME_DOES_NOT_EXIST);
        }
    }
}
