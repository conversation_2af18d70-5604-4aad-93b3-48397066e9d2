package com.facishare.webpage.customer.core.configuration;


import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.SystemPresetClient;
import com.fxiaoke.stone.commons.impl.AppFrameworkExclusiveClient;
import com.fxiaoke.stone.commons.impl.MetadataExclusiveClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 文件服务配置类，用于初始化fs-stone-commons-client的相关Bean
 */
@Configuration
public class FileServiceConfig {
    
    /**
     * 创建HttpSupport Bean
     */
    @Bean
    public HttpSupportFactoryBean okHttpSupport() {
        HttpSupportFactoryBean factoryBean = new HttpSupportFactoryBean();
        factoryBean.init();
        return factoryBean;
    }
    
    /**
     * 创建SystemPresetClient Bean
     */
    @Bean(value = "metadataExclusiveClient")
    public SystemPresetClient metadataExclusiveClient(OkHttpSupport okHttpSupport) {
        return new MetadataExclusiveClient(okHttpSupport);
    }

    @Bean (value = "appFrameworkExclusiveClient")
    public SystemPresetClient appFrameworkExclusiveClient(OkHttpSupport okHttpSupport) {
        return new AppFrameworkExclusiveClient(okHttpSupport);
    }
}