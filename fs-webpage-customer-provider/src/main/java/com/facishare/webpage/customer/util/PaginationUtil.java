
package com.facishare.webpage.customer.util;

/**
 * 分页工具类
 * 用于处理智能分页逻辑，解决删除最后一页数据后页面为空的问题
 */
public class PaginationUtil {

    /**
     * 分页结果封装类
     */
    public static class PaginationResult {
        private final int actualPageNum;
        private final int offset;
        private final long totalCount;

        public PaginationResult(int actualPageNum, int offset, long totalCount) {
            this.actualPageNum = actualPageNum;
            this.offset = offset;
            this.totalCount = totalCount;
        }

        public int getActualPageNum() {
            return actualPageNum;
        }

        public int getOffset() {
            return offset;
        }

        public long getTotalCount() {
            return totalCount;
        }
    }

    /**
     * 计算智能分页参数
     * 
     * @param requestPageNum 请求的页码
     * @param pageSize 每页大小
     * @param totalCount 总数据量
     * @return 分页结果
     */
    public static PaginationResult calculatePagination(int requestPageNum, int pageSize, long totalCount) {
        if (totalCount == 0) {
            return new PaginationResult(1, 0, 0);
        }

        // 计算最大页码
        int maxPage = (int) Math.ceil((double) totalCount / pageSize);
        
        // 如果请求的页码超过了最大页码，自动调整到最后一页
        int actualPageNum = Math.min(requestPageNum, maxPage);
        
        // 确保页码至少为1
        actualPageNum = Math.max(actualPageNum, 1);
        
        // 计算偏移量
        int offset = (actualPageNum - 1) * pageSize;
        
        return new PaginationResult(actualPageNum, offset, totalCount);
    }

    /**
     * 处理查询结果为空的情况
     * 当查询结果为空但总数不为0时，自动回退到第一页
     * 
     * @param pageSize 每页大小
     * @param totalCount 总数据量
     * @return 回退到第一页的分页结果
     */
    public static PaginationResult fallbackToFirstPage(int pageSize, long totalCount) {
        return new PaginationResult(1, 0, totalCount);
    }

    /**
     * 验证分页参数
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @throws IllegalArgumentException 如果参数无效
     */
    public static void validatePaginationParams(int pageNum, int pageSize) {
        if (pageNum < 1) {
            throw new IllegalArgumentException("页码必须大于0");
        }
        if (pageSize < 1) {
            throw new IllegalArgumentException("每页大小必须大于0");
        }
        if (pageSize > 1000) {
            throw new IllegalArgumentException("每页大小不能超过1000");
        }
    }
} 