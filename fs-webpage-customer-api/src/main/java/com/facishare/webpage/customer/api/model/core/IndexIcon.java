package com.facishare.webpage.customer.api.model.core;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/12/18.
 */
@Data
public class IndexIcon implements Serializable {
    @JSONField(ordinal = 1)
    private Integer index;
    @JSONField(ordinal = 2)
    private Icon icon;
    @JSONField(ordinal = 3)
    private List<Integer> supportTenantIds = Lists.newArrayList();
}
