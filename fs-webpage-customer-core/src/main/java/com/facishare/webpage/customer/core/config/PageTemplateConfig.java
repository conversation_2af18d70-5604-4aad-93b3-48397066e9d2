package com.facishare.webpage.customer.core.config;

import com.alibaba.fastjson.JSON;
import com.facishare.webpage.customer.core.model.PageTemplateConfigVO;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Created by zhangyu on 2020/1/9
 */
public class PageTemplateConfig {

    private static Logger logger = LoggerFactory.getLogger(PageTemplateConfig.class);

    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();

    private void init() {
        ConfigFactory.getConfig("fs-webpage-app-potrol-template", this::loadConfig);
    }

    private Map<String, PageTemplateConfigVO> pageTemplateConfigMap;

    private void loadConfig(IConfig config) {
        String templateJson = config.getString();
        Map<String, Object> map = JSON.parseObject(templateJson, Map.class);
        Map<String, PageTemplateConfigVO> pageTemplateHashMap = Maps.newHashMap();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            String value = gson.toJson(entry.getValue());
            PageTemplateConfigVO pageTemplateConfigVO = JSON.parseObject(value, PageTemplateConfigVO.class);
            pageTemplateHashMap.put(key, pageTemplateConfigVO);
        }
        pageTemplateConfigMap = pageTemplateHashMap;
        logger.info("init PageTemplateConfig config pageTemplateConfigMap:{};", pageTemplateConfigMap);
    }

    public PageTemplateConfigVO getPageTemplateConfig(String appId) {
        return pageTemplateConfigMap.get(appId);
    }

    /**
     * 判断是否是互联
     *
     * @param appId
     * @return
     */
    public boolean checkCrossApp(String appId) {
        if (StringUtils.isBlank(appId)) {
            return false;
        }
        PageTemplateConfigVO pageTemplateConfigVO = pageTemplateConfigMap.get(appId);
        if (pageTemplateConfigVO == null) {
            return false;
        }
        return pageTemplateConfigVO.isCrossApp();
    }

    public Map<String, PageTemplateConfigVO> getPageTemplateConfigMap() {
        return pageTemplateConfigMap;
    }

    public static void main(String[] args) {
        System.setProperty("process.profile", "fstest");
        PageTemplateConfig pageTemplateConfig = new PageTemplateConfig();
        pageTemplateConfig.init();
        PageTemplateConfigVO pageTemplateConfigVO = pageTemplateConfig.getPageTemplateConfig("FSAID_98979c");

        System.out.println(pageTemplateConfigVO);
    }
}
