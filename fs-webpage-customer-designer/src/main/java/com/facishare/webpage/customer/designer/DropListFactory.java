package com.facishare.webpage.customer.designer;

import com.facishare.webpage.customer.designer.model.DropListType;
import com.google.common.collect.Maps;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class DropListFactory implements ApplicationContextAware {

    private static Map<DropListType, DropListService> dropListServiceMap;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, DropListService> map = applicationContext.getBeansOfType(DropListService.class);
        dropListServiceMap = Maps.newHashMap();
        map.forEach((key, value) -> dropListServiceMap.put(value.getDropListType(), value));
    }

    public static <T extends DropListService> T getDropListService(DropListType dropListType) {
        return (T) dropListServiceMap.get(dropListType);
    }

}
