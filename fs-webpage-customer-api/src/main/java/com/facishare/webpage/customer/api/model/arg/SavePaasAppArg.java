package com.facishare.webpage.customer.api.model.arg;

import com.facishare.webpage.customer.api.model.Scope;
import lombok.Data;

import java.util.List;

@Data
public class SavePaasAppArg {
    /**
     * 租户Id
     */
    private int tenantId;
    /**
     * 用户Id
     */
    private int userId;
    /**
     * appId
     */
    private String appId;
    /**
     * 适用范围
     */
    private List<Scope> scopes;
    /**
     * 适用端
     */
    private String accessType;
    /**
     * 应用状态
     */
    private Integer status;
    /**
     * 父appId
     */
    private String parentAppId;




}
