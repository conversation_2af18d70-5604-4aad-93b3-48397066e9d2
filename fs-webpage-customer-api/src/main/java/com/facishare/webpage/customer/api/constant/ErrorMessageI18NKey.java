package com.facishare.webpage.customer.api.constant;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2024/12/30.
 */
public interface ErrorMessageI18NKey {
    String SITE_API_NAME_DUPLICATE = "webpage.site.apiName.duplicate";
    String SITE_ID_DUPLICATE = "webpage.site.id.duplicate";
    String SITE_PAGE_API_NAME_DUPLICATE = "webpage.site.page.apiName.duplicate";
    String SITE_THEME_LAYOUT_API_NAME_DUPLICATE = "webpage.site.themeLayout.apiName.duplicate";
    String SITE_THEME_STYLE_API_NAME_DUPLICATE = "webpage.site.themeStyle.apiName.duplicate";
    String SITE_MENU_API_NAME_DUPLICATE = "webpage.site.menu.apiName.duplicate";
    String SITE_PAGE_OVER_COMPONENTS_LIMIT = "webpage.site.page.overComponentsLimit";
    String SITE_THEME_LAYOUT_OVER_COMPONENTS_LIMIT = "webpage.site.themeLayout.overComponentsLimit";
    String WEB_APP_VIEW_IN_EXISTENCE_OR_DELETE = "web.app.view.inExistenceOrDelete";
    String SITE_PARAM_ERROR = "webpage.site.paramError";
    String SITE_NOT_FOUND_ERROR = "webpage.site.notFound";
    String APP_NOT_FOUND_ERROR = "webpage.app.notFound";
    String APP_NOT_ENABLE_ERROR = "webpage.app.notEnable";
    String PAGE_NOT_FOUND_ERROR = "webpage.page.notFound";
    String SITE_NOT_PUBLISHED_ERROR = "webpage.site.notPublished";

    String PARAMS_ERROR = "webpage.common.paramError"; //参数错误
    String SEARCH_INVALID_CHARACTERS = "webpage.common.search.invalidCharacters"; //搜索包含非法字符
    String SEARCH_KEYWORD_TOO_LONG = "webpage.common.search.keywordTooLong"; //搜索关键字过长

    // CMS工作区相关错误
    String WORKSPACE_ALREADY_EXISTS = "webpage.cms.workspace.alreadyExists"; //工作区已存在
    String WORKSPACE_NOT_EXISTS = "webpage.cms.workspace.notExists"; //工作区不存在
    String WORKSPACE_HAS_RELATED_RESOURCES = "webpage.cms.workspace.hasRelatedResources"; //工作区有关联资源，不能删除
    String WORKSPACE_NOT_DISABLE = "webpage.cms.workspace.workspace_not_disable";//工作区没有，禁用不能删除


    // CMS资源相关错误
    String PARENT_FOLDER_NOT_EXISTS = "webpage.cms.resource.parentFolderNotExists"; //父文件夹不存在
    String PARENT_NOT_FOLDER = "webpage.cms.resource.parentNotFolder"; //父级不是文件夹
    String FILE_NOT_EXISTS = "webpage.cms.resource.fileNotExists"; //文件不存在
    String FOLDER_HAS_CHILDREN = "webpage.cms.resource.folderHasChildren"; //文件夹有子文件，不能删除
    String FILE_HAS_REFERENCES = "webpage.cms.resource.fileHasReferences"; //文件有引用关系，不能删除
    String TARGET_FOLDER_NOT_EXISTS = "webpage.cms.resource.targetFolderNotExists"; //目标文件夹不存在
    String TARGET_NOT_FOLDER = "webpage.cms.resource.targetNotFolder"; //目标不是文件夹
    String CONVERT_PATH_FAILED = "webpage.cms.resource.convertPathFailed"; //转换文件路径失败
    String FILE_API_NAME_REPEAT = "webpage.cms.resource.fileApiNameRepeat"; //文件ApiName重复
    String FILE_PATH_CONVERT_FAILED = "webpage.cms.resource.filePathConvertFailed"; //文件路径转换失败
    String FILE_API_NAME_NOT_EXISTS = "webpage.cms.resource.fileApiNameNotExists"; //文件路径转换失败
    String FILE_STATUS_NOT_ENABLE = "webpage.cms.resource.fileApiNameNotExists"; //文件已禁用或删除，请检查一下再保存!{0}
    String WORK_SPACE_STATUS_NOT_ENABLE = "webpage.cms.resource.work_space_status_not_enable"; //工作区已禁用或删除，请检查一下再保存!{0}
    String SITE_REFERENCE_INFO_ERROR = "webpage.site.site_reference_info_error"; //站点多语或文件插入异常，请联系管理员!


    // 站点多语相关错误
    String SITE_I18N_QUERY_FAILED = "webpage.site.i18n.queryFailed"; //站点多语查询失败
    String SITE_I18N_SYNC_FAILED = "webpage.site.i18n.syncFailed"; //站点多语同步失败
    String SITE_I18N_VALIDATION_FAILED = "webpage.site.i18n.validationFailed"; //站点多语验证失败
    String SITE_I18N_REFERENCE_SAVE_FAILED = "webpage.site.i18n.referenceSaveFailed"; //站点多语引用关系保存失败
    String SITE_LANG_CONFIG_INVALID = "webpage.site.langConfig.invalid"; //站点语言配置无效
    String FILE_URL_GENERATE_FAILED = "webpage.cms.resource.fileUrlGenerateFailed"; //文件URL生成失败
    String FILE_URL_NOT_EXISTS = "webpage.cms.resource.urlNotExists"; //文件url不存在
}
