<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-2.5.xsd">

    <bean id="webPageRestServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-qixin-objgroup-rest-proxy-config" init-method="init"/>


    <!-- web主频道接口 -->
    <bean id="userWebMainChannelService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.UserWebMainChannelService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

    <bean id="webMainChannelManagerService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.WebMainChannelManagerService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

    <!-- web主频道接口 -->
    <bean id="webPageService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.WebPageService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

    <!-- 自定义菜单项接口 -->
    <bean id="customerMenuRestService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.CustomerMenuRestService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

    <bean id="appPageService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.AppPageService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

    <bean id="paaSAppService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.PaaSAppRestService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

    <bean id="tenantMenuRestService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.TenantMenuRestService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

    <bean id="linkAppRestService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.LinkAppRestService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

    <bean id="synLinkDataRestService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.SynLinkDataRestService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

    <bean id="tenantPageTempleRestService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.TenantPageTempleRestService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

    <bean id="homePageRestService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.HomePageRestService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

    <bean id="mainChannelRestService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.MainChannelRestService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>
    <bean id="tenantBrandColorService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.TenantBrandColorService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

    <bean id="menusRegisterRestService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.MenusRegisterRestService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

    <bean id="siteRestService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.api.service.SiteRestService">
        <property name="factory" ref="webPageRestServiceProxyFactory"/>
    </bean>

</beans>
