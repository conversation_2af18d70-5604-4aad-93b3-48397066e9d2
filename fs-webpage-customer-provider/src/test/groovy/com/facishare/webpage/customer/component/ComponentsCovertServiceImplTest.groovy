package com.facishare.webpage.customer.component

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.facishare.cep.plugin.model.OuterUserInfo
import com.facishare.cep.plugin.model.UserInfo
import com.facishare.webpage.customer.api.model.core.customelayout.Filter
import com.facishare.webpage.customer.component.impl.ComponentsCovertServiceImpl
import com.facishare.webpage.customer.constant.ComponentConstant
import com.facishare.webpage.customer.core.util.WebPageGraySwitch
import com.facishare.webpage.customer.service.HomePageCommonService
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.release.FsGrayReleaseBiz
import org.powermock.reflect.Whitebox
import spock.lang.Specification

/**
 * ComponentsCovertServiceImpl单元测试
 * 重点测试covertFilterComponent方法的性能和功能
 */
class ComponentsCovertServiceImplTest extends Specification {

    ComponentsCovertServiceImpl componentsCovertService
    HomePageCommonService homePageCommonService

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        homePageCommonService = Mock(HomePageCommonService)
        
        componentsCovertService = new ComponentsCovertServiceImpl()
        componentsCovertService.homePageCommonService = homePageCommonService
    }
    
    def cleanup() {
        // 重置Mock状态
        GroovySystem.metaClassRegistry.removeMetaClass(WebPageGraySwitch)
    }

    def "测试covertFilterComponent - 正常场景"() {
        setup: "设置非灰度用户Mock"
        // Mock WebPageGraySwitch为非灰度用户
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(WebPageGraySwitch, "gray", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(_, _) >> false
        
        and: "准备测试数据"
        def userInfo = new UserInfo()
        userInfo.enterpriseId = 123
        userInfo.employeeId = 456
        
        def outerUserInfo = null
        def apiName = "testApi"
        
        // 准备现有的filters
        def existingFilter = new Filter()
        existingFilter.filterType = "existingType"
        existingFilter.filterData = "existingData"
        
        // 准备filterJSONObjects
        def filterJsonArray = new JSONArray()
        def newFilterJson = new JSONObject()
        newFilterJson.put(ComponentConstant.filterType, "newType")
        newFilterJson.put(ComponentConstant.filterData, "newData")
        filterJsonArray.add(newFilterJson)
        
        def filterJsonObject = new JSONObject()
        filterJsonObject.put(ComponentConstant.FILTERS, filterJsonArray)
        filterJsonObject.put("apiName", "testFilterComponent")
        filterJsonObject.put("propsType", "filters")
        
        def filterJSONObjects = [filterJsonObject]
        
        when: "调用covertFilterComponent方法"
        homePageCommonService.convertFilter(userInfo, outerUserInfo, apiName) >> [existingFilter]
        def result = componentsCovertService.covertFilterComponent(userInfo, outerUserInfo, apiName, filterJSONObjects)
        
        then: "验证结果"
        result != null
        result.size() == 1
        
        def resultComponent = result[0]
        resultComponent != null
        
        // 验证返回的是包装后的组件结构
        def componentApiName = resultComponent.keySet().iterator().next()
        def componentData = resultComponent.getJSONObject(componentApiName)
        
        // 验证组件结构
        componentData.containsKey("api_name") // 验证字段存在
        componentData.containsKey("type")
        componentData.getJSONObject("props") != null
        
        // 验证filters在props中
        def props = componentData.getJSONObject("props")
        def filters = props.getJSONArray(ComponentConstant.FILTERS)
        filters != null
        filters.size() == 2 // 原有1个 + 新增1个
    }

    def "测试covertFilterComponent - 灰度用户场景"() {
        given: "准备灰度用户测试数据"
        def userInfo = new UserInfo()
        userInfo.enterpriseId = 123
        
        def filterJsonObject = new JSONObject()
        filterJsonObject.put(ComponentConstant.FILTERS, new JSONArray())
        filterJsonObject.put("apiName", "testFilterComponent")
        filterJsonObject.put("propsType", "filters")
        def filterJSONObjects = [filterJsonObject]
        
        when: "调用covertFilterComponent方法"
        // 在when块中设置Mock，确保在方法调用前生效
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(WebPageGraySwitch, "gray", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(_,_)>>true
//        WebPageGraySwitch.metaClass.static.isAllownotQueryUserFilterGrayEis = { Integer enterpriseId ->
//            println "Mock called with enterpriseId: ${enterpriseId}, returning true"
//            return true // 灰度用户
//        }
        
        def result = componentsCovertService.covertFilterComponent(userInfo, null, "testApi", filterJSONObjects)
        
        then: "验证灰度用户不会调用convertFilter"
        0 * homePageCommonService.convertFilter(_, _, _)
        result != null
        result.size() == 1
    }

    def "测试covertFilterComponent - 空filterJSONObjects"() {
        setup: "设置非灰度用户Mock"
        // Mock WebPageGraySwitch为非灰度用户
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(WebPageGraySwitch, "gray", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(_, _) >> false
        
        and: "空的filterJSONObjects"
        def userInfo = new UserInfo()
        userInfo.enterpriseId = 123
        
        def filterJSONObjects = []
        
        when: "调用covertFilterComponent方法"
        homePageCommonService.convertFilter(_, _, _) >> []
        def result = componentsCovertService.covertFilterComponent(userInfo, null, "testApi", filterJSONObjects)
        
        then: "返回空列表"
        result != null
        result.size() == 0
    }

    def "测试covertFilterComponent - 重复filterType过滤"() {
        setup: "设置非灰度用户Mock"
        // Mock WebPageGraySwitch为非灰度用户
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(WebPageGraySwitch, "gray", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(_, _) >> false
        
        and: "准备重复filterType的测试数据"
        def userInfo = new UserInfo()
        userInfo.enterpriseId = 123
        
        // 准备现有的filter
        def existingFilter = new Filter()
        existingFilter.filterType = "duplicateType"
        existingFilter.filterData = "existingData"
        
        // 准备新的filter，但filterType相同
        def filterJsonArray = new JSONArray()
        def duplicateFilterJson = new JSONObject()
        duplicateFilterJson.put(ComponentConstant.filterType, "duplicateType")
        duplicateFilterJson.put(ComponentConstant.filterData, "newData")
        filterJsonArray.add(duplicateFilterJson)
        
        def filterJsonObject = new JSONObject()
        filterJsonObject.put(ComponentConstant.FILTERS, filterJsonArray)
        filterJsonObject.put("apiName", "testFilterComponent")
        filterJsonObject.put("propsType", "filters")
        
        def filterJSONObjects = [filterJsonObject]
        
        when: "调用covertFilterComponent方法"
        homePageCommonService.convertFilter(userInfo, null, "testApi") >> [existingFilter]
        def result = componentsCovertService.covertFilterComponent(userInfo, null, "testApi", filterJSONObjects)
        
        then: "验证重复的filterType不会被添加"
        result != null
        result.size() == 1
        
        def resultComponent = result[0]
        def componentApiName = resultComponent.keySet().iterator().next()
        def componentData = resultComponent.getJSONObject(componentApiName)
        def props = componentData.getJSONObject("props")
        def filters = props.getJSONArray(ComponentConstant.FILTERS)
        filters.size() == 1 // 只有原有的1个，重复的不会被添加
    }

    def "测试covertFilterComponent - 多个不同filterType"() {
        setup: "设置非灰度用户Mock"
        // Mock WebPageGraySwitch为非灰度用户
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(WebPageGraySwitch, "gray", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(_, _) >> false
        
        and: "准备多个不同filterType的测试数据"
        def userInfo = new UserInfo()
        userInfo.enterpriseId = 123
        
        // 准备现有的filter
        def existingFilter = new Filter()
        existingFilter.filterType = "existingType"
        existingFilter.filterData = "existingData"
        
        // 准备多个新的filters
        def filterJsonArray = new JSONArray()
        
        def newFilter1 = new JSONObject()
        newFilter1.put(ComponentConstant.filterType, "newType1")
        newFilter1.put(ComponentConstant.filterData, "newData1")
        filterJsonArray.add(newFilter1)
        
        def newFilter2 = new JSONObject()
        newFilter2.put(ComponentConstant.filterType, "newType2")
        newFilter2.put(ComponentConstant.filterData, "newData2")
        filterJsonArray.add(newFilter2)
        
        def filterJsonObject = new JSONObject()
        filterJsonObject.put(ComponentConstant.FILTERS, filterJsonArray)
        filterJsonObject.put("apiName", "testFilterComponent")
        filterJsonObject.put("propsType", "filters")
        
        def filterJSONObjects = [filterJsonObject]
        
        when: "调用covertFilterComponent方法"
        homePageCommonService.convertFilter(userInfo, null, "testApi") >> [existingFilter]
        def result = componentsCovertService.covertFilterComponent(userInfo, null, "testApi", filterJSONObjects)
        
        then: "验证所有不同的filterType都被添加"
        result != null
        result.size() == 1
        
        def resultComponent = result[0]
        def componentApiName = resultComponent.keySet().iterator().next()
        def componentData = resultComponent.getJSONObject(componentApiName)
        def props = componentData.getJSONObject("props")
        def filters = props.getJSONArray(ComponentConstant.FILTERS)
        filters.size() == 3 // 原有1个 + 新增2个
    }

    def "测试covertFilterComponent - 边界值测试"() {
        setup: "设置非灰度用户Mock"
        // Mock WebPageGraySwitch为非灰度用户
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(WebPageGraySwitch, "gray", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(_, _) >> false
        
        and: "边界值测试数据"
        def userInfo = new UserInfo()
        userInfo.enterpriseId = enterpriseId
        
        def filterJsonObject = new JSONObject()
        filterJsonObject.put("apiName", "testFilterComponent")
        filterJsonObject.put("propsType", "filters")
        if (hasFilters) {
            def filterJsonArray = new JSONArray()
            if (filterType != null) {
                def filterJson = new JSONObject()
                filterJson.put(ComponentConstant.filterType, filterType)
                filterJson.put(ComponentConstant.filterData, filterData)
                filterJsonArray.add(filterJson)
            }
            filterJsonObject.put(ComponentConstant.FILTERS, filterJsonArray)
        }
        
        def filterJSONObjects = [filterJsonObject]
        
        when: "调用covertFilterComponent方法"
        homePageCommonService.convertFilter(_, _, _) >> []
        def result = componentsCovertService.covertFilterComponent(userInfo, null, "testApi", filterJSONObjects)
        
        then: "验证边界情况处理"
        result != null
        result.size() == 1
        
        where: "测试各种边界值"
        enterpriseId | hasFilters | filterType | filterData | description
        0           | false      | null       | null       | "企业ID为0"
        -1          | false      | null       | null       | "企业ID为负数"
        123         | true       | null       | "data"     | "filterType为null"
        123         | true       | ""         | "data"     | "filterType为空字符串"
        123         | true       | "type"     | null       | "filterData为null"
        123         | true       | "type"     | ""         | "filterData为空字符串"
    }

    def "测试covertFilterComponent - 性能测试"() {
        setup: "设置非灰度用户Mock"
        // Mock WebPageGraySwitch为非灰度用户
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(WebPageGraySwitch, "gray", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(_, _) >> false
        
        and: "大量数据的性能测试"
        def userInfo = new UserInfo()
        userInfo.enterpriseId = 123
        
        // 准备大量的filterJSONObjects
        def filterJSONObjects = []
        for (int i = 0; i < 100; i++) {
            def filterJsonArray = new JSONArray()
            for (int j = 0; j < 10; j++) {
                def filterJson = new JSONObject()
                filterJson.put(ComponentConstant.filterType, "type_${i}_${j}")
                filterJson.put(ComponentConstant.filterData, "data_${i}_${j}")
                filterJsonArray.add(filterJson)
            }
            
            def filterJsonObject = new JSONObject()
            filterJsonObject.put(ComponentConstant.FILTERS, filterJsonArray)
            filterJsonObject.put("apiName", "testFilterComponent_${i}")
            filterJsonObject.put("propsType", "filters")
            filterJSONObjects.add(filterJsonObject)
        }
        
        when: "调用covertFilterComponent方法并测量时间"
        homePageCommonService.convertFilter(_, _, _) >> []
        
        def startTime = System.currentTimeMillis()
        def result = componentsCovertService.covertFilterComponent(userInfo, null, "testApi", filterJSONObjects)
        def endTime = System.currentTimeMillis()
        def executionTime = endTime - startTime
        
        then: "验证性能和结果"
        result != null
        result.size() == 100
        executionTime < 5000 // 应该在5秒内完成
        
        println "Performance test: processed ${filterJSONObjects.size()} objects in ${executionTime}ms"
    }

    def "测试covertFilterComponent - JSON序列化异常处理"() {
        setup: "设置非灰度用户Mock"
        // Mock WebPageGraySwitch为非灰度用户
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(WebPageGraySwitch, "gray", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(_, _) >> false
        
        and: "包含异常JSON的测试数据"
        def userInfo = new UserInfo()
        userInfo.enterpriseId = 123
        
        // 准备包含异常数据的filterJsonArray
        def filterJsonArray = new JSONArray()
        
        // 添加正常的filter
        def normalFilter = new JSONObject()
        normalFilter.put(ComponentConstant.filterType, "normalType")
        normalFilter.put(ComponentConstant.filterData, "normalData")
        filterJsonArray.add(normalFilter)
        
        // 添加异常的filter（filterType为null）
        def invalidFilter = new JSONObject()
        // 故意不添加filterType，这样filterType会是null
        invalidFilter.put(ComponentConstant.filterData, "invalidData")
        filterJsonArray.add(invalidFilter)
        
        def filterJsonObject = new JSONObject()
        filterJsonObject.put(ComponentConstant.FILTERS, filterJsonArray)
        filterJsonObject.put("apiName", "testFilterComponent")
        filterJsonObject.put("propsType", "filters")
        
        def filterJSONObjects = [filterJsonObject]
        
        when: "调用covertFilterComponent方法"
        homePageCommonService.convertFilter(_, _, _) >> []
        def result = componentsCovertService.covertFilterComponent(userInfo, null, "testApi", filterJSONObjects)
        
        then: "验证异常处理"
        result != null
        result.size() == 1
        
        // 验证所有filter都被添加（包括filterType为null的）
        def resultComponent = result[0]
        def componentApiName = resultComponent.keySet().iterator().next()
        def componentData = resultComponent.getJSONObject(componentApiName)
        def props = componentData.getJSONObject("props")
        def filters = props.getJSONArray(ComponentConstant.FILTERS)
        filters.size() == 2 // 正常的filter + filterType为null的filter都被添加
        
        // 验证第一个filter是正常的
        def filter1 = filters.getJSONObject(0)
        filter1.getString("filterType") == "normalType"
        
        // 验证第二个filter的filterType为null
        def filter2 = filters.getJSONObject(1)
        filter2.get("filterType") == null
    }
} 