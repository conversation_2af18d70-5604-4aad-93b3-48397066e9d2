package com.facishare.webpage.customer.processor.siteDiffProcessor.service;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.ErrorMessageI18NKey;
import com.facishare.webpage.customer.api.exception.ValidateException;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.constant.MenuStatus;
import com.facishare.webpage.customer.dao.TenantMenuDao;
import com.facishare.webpage.customer.dao.entity.SiteEntity;
import com.facishare.webpage.customer.dao.entity.TenantMenuEntity;
import com.facishare.webpage.customer.processor.siteDiffProcessor.AbstractEntityDiffProcessor;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class TenantMenuDiffProcessor extends AbstractEntityDiffProcessor<TenantMenuEntity> {

    @Autowired
    private TenantMenuDao tenantMenuDao;

    @Override
    protected List<TenantMenuEntity> queryOldEntities(User user, SiteEntity siteEntity, String clientType, String diffType, Boolean isPublish) {
        return tenantMenuDao.findBySiteApiNameIncludeDisable(user.getTenantId(),
                siteEntity.getAppId(), siteEntity.getApiName(), clientType);
    }

    @Override
    protected String getEntityApiName(TenantMenuEntity entity) {
        return entity.getApiName();
    }

    @Override
    protected String getEntityId(TenantMenuEntity entity) {
        return entity.getId();
    }

    @Override
    protected void setEntityId(TenantMenuEntity entity, String id) {
        entity.setId(id);
    }

    @Override
    protected void validateNewEntities(User user, List<TenantMenuEntity> newEntities) {
        if (CollectionUtils.isEmpty(newEntities)) {
            return;
        }
        Set<String> apiNames = Sets.newHashSet();
        newEntities.forEach(x -> {
            if (StringUtils.isBlank(x.getApiName())) {
                throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
            }
            if (!apiNames.add(x.getApiName())) {
                throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_MENU_API_NAME_DUPLICATE, x.getApiName());
            }
            x.setStatus(MenuStatus.enableStatus);
        });
        List<TenantMenuEntity> oldMenuList = tenantMenuDao.findByApiNamesIncludeDisable(user.getTenantId(),
                Lists.newArrayList(apiNames));
        if (CollectionUtils.isNotEmpty(oldMenuList)) {
            List<String> apiNameList = oldMenuList.stream().map(TenantMenuEntity::getApiName).collect(Collectors.toList());
            throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_MENU_API_NAME_DUPLICATE, String.valueOf(apiNameList));
        }
    }

    @Override
    protected String getEntityType() {
        return "TenantMenu";
    }

}
