package com.facishare.webpage.customer.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Created by zhangyu on 2020/11/12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScopeForCross {

    private String roleId;

    /**
     * 角色类型，1：基础角色，2：预置角色，3：自定义角色
     */
    private Integer roleType;

    private Long updateTime = new Date().getTime();
    private Long createTime = new Date().getTime();


}
