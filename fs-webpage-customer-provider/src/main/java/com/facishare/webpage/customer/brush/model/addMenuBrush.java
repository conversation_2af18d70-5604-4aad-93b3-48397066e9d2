package com.facishare.webpage.customer.brush.model;

import com.facishare.webpage.customer.dao.entity.MenuDataEntity;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

public interface addMenuBrush {
    @Data
    class Arg implements Serializable {
        private String appId =  "";
        private List<MenuDataEntity> menuDataEntityList = Lists.newArrayList();

        private boolean brushOther = false;
    }
}
