package com.facishare.webpage.customer.brush.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 批量删除PaaSApp的模型
 */
public interface DeleteAppsByIdsBrush {

    @Data
    class Arg implements Serializable {
        private Integer tenantId;
        private Integer sourceTenantId;
        private List<String> appIds;
    }

    @Data
    @Builder
    class Result implements Serializable {
        private Boolean success;
    }
} 