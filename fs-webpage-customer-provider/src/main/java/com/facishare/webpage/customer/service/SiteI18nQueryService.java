package com.facishare.webpage.customer.service;

import com.facishare.webpage.customer.controller.model.I18nInfoDTO;
import com.facishare.webpage.customer.api.model.SiteLangDTO;

import java.util.List;

/**
 * 站点多语服务接口
 * 处理站点多语信息的查询、同步和管理
 */
public interface SiteI18nQueryService {

    /**
     * 查询站点的多语信息
     *
     * @param tenantId    租户ID
     * @param siteApiName 站点ApiName
     * @param langList    站点语言配置列表
     * @return 多语信息列表
     */
    List<I18nInfoDTO> queryI18nInfoForSite(Integer tenantId, String siteApiName, List<SiteLangDTO> langList);

    /**
     * 同步多语信息到翻译工作台并更新引用关系
     *
     * @param tenantId     租户ID
     * @param siteApiName  站点ApiName
     * @param i18nInfoList 多语信息列表
     */
    void syncI18nInfoForSite(Integer tenantId, String siteApiName, List<I18nInfoDTO> i18nInfoList);

    /**
     * 检查站点是否支持多语功能
     *
     * @param langList 站点语言配置列表
     * @return 是否支持多语功能
     */
    boolean isSiteMultilingualEnabled(List<SiteLangDTO> langList);

    /**
     * 获取站点支持的语言代码列表
     *
     * @param langList 站点语言配置列表
     * @return 支持的语言代码列表
     */
    List<String> getSupportedLanguageCodes(List<SiteLangDTO> langList);
}
