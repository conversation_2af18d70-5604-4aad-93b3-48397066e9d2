package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.api.constant.CmsStatus;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.dao.entity.FileEntity;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.regex.Pattern;

/**
 * 文件资源DAO实现类
 */
@Setter
public class FileEntityDaoImpl implements FileEntityDao {

    @Resource
    private Datastore datastore;

    @PostConstruct
    public void init() {
        datastore.ensureIndexes(FileEntity.class, true);
    }

    @Override
    public FileEntity findByApiNameIncludeDisable(Integer tenantId, String apiName) {
        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").equal(apiName);
        return query.get();
    }

    @Override
    public FileEntity findByApiName(Integer tenantId, String apiName) {
        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").equal(apiName);
        query.field("status").equal(CmsStatus.ENABLE);
        return query.get();
    }

    @Override
    public List<FileEntity> findByWorkSpace(Integer tenantId, String workSpaceApiName, String parentApiName) {
        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("workSpace").equal(workSpaceApiName);
        query.field("status").notEqual(CmsStatus.DELETE);

        if (StringUtils.isNotEmpty(parentApiName)) {
            query.field("parentApiName").equal(parentApiName);
        }

        return query.asList();
    }

    @Override
    public List<FileEntity> findByWorkSpace(Integer tenantId, String workSpaceApiName) {
        // 调用带parentApiName的方法，传入null表示查询根目录下的文件
        return findByWorkSpace(tenantId, workSpaceApiName, null);
    }

    @Override
    public Map<String, List<FileEntity>> batchFindByWorkSpace(Integer tenantId, Collection<String> workSpaceApiNameList) {
        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("workSpace").in(workSpaceApiNameList);
        query.field("status").notEqual(CmsStatus.DELETE);
        List<FileEntity> fileEntities = query.asList();
        return fileEntities.stream().collect(Collectors.groupingBy(FileEntity::getWorkSpace));
    }


    @Override
    public List<FileEntity> findByCondition(FileEntity condition, int offset, int limit) {
        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(condition.getTenantId());
        if (StringUtils.isNotBlank(condition.getName())) {
            query.field("name").containsIgnoreCase(condition.getName());
        }
        if (Objects.nonNull(condition.getStatus())) {
            query.field("status").equal(condition.getStatus());
        } else {
            query.field("status").notEqual(CmsStatus.DELETE);
        }
        if (Objects.nonNull(condition.getWorkSpace())) {
            query.field("workSpace").equal(condition.getWorkSpace());
        }
        query.order("-createTime");
        query.offset(offset);
        query.limit(limit);

        return query.asList();
    }


    @Override
    public List<FileEntity> findByParent(Integer tenantId, String parentApiName) {
        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("parentApiName").equal(parentApiName);
        query.field("status").equal(CmsStatus.ENABLE);
        return query.asList();
    }

    @Override
    public List<FileEntity> batchSave(User user, List<FileEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }
        for (FileEntity entity : entities) {
            fillUpdateSystemInfo(user, entity);
        }
        datastore.save(entities);
        return entities;
    }


    private void fillUpdateSystemInfo(User user, FileEntity entity) {
        entity.setUpdaterId(String.valueOf(user.getUserId()));
        entity.setUpdateTime(System.currentTimeMillis());
    }

    @Override
    public FileEntity save(User user, FileEntity entity) {
        if (Objects.isNull(entity.getId())) {
            entity.setId(UUID.randomUUID().toString());
        }

        if (Objects.isNull(entity.getCreateTime())) {
            entity.setCreateTime(System.currentTimeMillis());
            entity.setCreatorId(String.valueOf(user.getUserId()));
        }

        entity.setUpdateTime(System.currentTimeMillis());
        entity.setUpdaterId(String.valueOf(user.getUserId()));

        if (Objects.isNull(entity.getStatus())) {
            entity.setStatus(CmsStatus.ENABLE);
        }

        datastore.save(entity);
        return entity;
    }

    @Override
    public FileEntity update(User user, FileEntity entity) {
        if (StringUtils.isBlank(entity.getApiName())) {
            return null;
        }

        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(entity.getTenantId());
        query.field("apiName").equal(entity.getApiName());

        UpdateOperations<FileEntity> ops = datastore.createUpdateOperations(FileEntity.class);

        if (StringUtils.isNotBlank(entity.getName())) {
            ops.set("name", entity.getName());
        }

        if (StringUtils.isNotBlank(entity.getDescription())) {
            ops.set("description", entity.getDescription());
        }

        if (StringUtils.isNotBlank(entity.getPath())) {
            ops.set("path", entity.getPath());
        }

        if (Objects.nonNull(entity.getType())) {
            ops.set("type", entity.getType());
        }

        if (StringUtils.isNotBlank(entity.getMimeType())) {
            ops.set("mimeType", entity.getMimeType());
        }
        if (StringUtils.isNotEmpty(entity.getNodeType())) {
            ops.set("nodeType", entity.getNodeType());
        }
        if (StringUtils.isNotEmpty(entity.getCreatorId())) {
            ops.set("creatorId", entity.getCreatorId());
        }
        if (Objects.nonNull(entity.getUpdateTime())) {
            ops.set("createTime", entity.getCreateTime());
        }
        if (StringUtils.isNotEmpty(entity.getSignature())) {
            ops.set("signature", entity.getSignature());
        }
        if (Objects.nonNull(entity.getStatus())) {
            ops.set("status", entity.getStatus());
        }

        ops.set("updateTime", System.currentTimeMillis());
        ops.set("updaterId", user.getUserId());

        return datastore.findAndModify(query, ops);
    }

    @Override
    public void batchUpdateStatus(User user, Collection<String> apiNames, Integer status) {
        if (CollectionUtils.isEmpty(apiNames)) {
            return;
        }

        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        query.field("apiName").in(apiNames);

        UpdateOperations<FileEntity> ops = datastore.createUpdateOperations(FileEntity.class);
        ops.set("status", status);
        ops.set("updateTime", System.currentTimeMillis());
        ops.set("updaterId", user.getUserId());

        datastore.update(query, ops);
    }

    @Override
    public FileEntity moveFile(User user, String apiName, String parentApiName) {
        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        query.field("apiName").equal(apiName);

        UpdateOperations<FileEntity> ops = datastore.createUpdateOperations(FileEntity.class);
        ops.set("parentApiName", parentApiName);
        ops.set("updateTime", System.currentTimeMillis());
        ops.set("updaterId", user.getUserId());

        return datastore.findAndModify(query, ops);
    }

    @Override
    public List<FileEntity> findByApiNames(Integer tenantId, Collection<String> apiNames) {
        if (CollectionUtils.isEmpty(apiNames)) {
            return Collections.emptyList();
        }

        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").in(apiNames);
        query.field("status").notEqual(CmsStatus.DELETE);

        return query.asList();
    }


    @Override
    public List<FileEntity> findByApiNamesIncludesDelete(Integer tenantId, Collection<String> apiNames) {
        if (CollectionUtils.isEmpty(apiNames)) {
            return Collections.emptyList();
        }
        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").in(apiNames);
        return query.asList();
    }

    @Override
    public void delete(User user, String apiName) {
        // 标记删除文件
        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        query.field("apiName").equal(apiName);

        UpdateOperations<FileEntity> ops = datastore.createUpdateOperations(FileEntity.class);
        ops.set("status", CmsStatus.DELETE);
        ops.set("updateTime", System.currentTimeMillis());
        ops.set("updaterId", user.getUserId());
        datastore.update(query, ops);
    }

    @Override
    public void deleteByWorkSpace(User user, String workSpaceApiName) {
        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        query.field("workSpace").equal(workSpaceApiName);

        UpdateOperations<FileEntity> ops = datastore.createUpdateOperations(FileEntity.class);
        ops.set("status", CmsStatus.DELETE);
        ops.set("updateTime", System.currentTimeMillis());
        ops.set("updaterId", String.valueOf(user.getUserId()));
        datastore.update(query, ops);
    }

    @Override
    public void batchDelete(User user, List<FileEntity> allFile) {
        if (CollectionUtils.isEmpty(allFile)) {
            return;
        }

        // 提取所有文件的ID
        List<String> apiNames = allFile.stream()
                .map(FileEntity::getApiName)
                .filter(Objects::nonNull) // 过滤掉null的ID
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(apiNames)) {
            return;
        }

        // 使用批量更新操作
        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("apiName").in(apiNames);

        UpdateOperations<FileEntity> ops = datastore.createUpdateOperations(FileEntity.class);
        ops.set("status", CmsStatus.DELETE);
        ops.set("updateTime", System.currentTimeMillis());
        ops.set("updaterId", String.valueOf(user.getUserId()));

        // 执行更新并获取更新结果
        datastore.update(query, ops);
    }

    @Override
    public void batchUpdate(User user, List<FileEntity> updatedEntities) {
        if (CollectionUtils.isEmpty(updatedEntities)) {
            return;
        }
        for (FileEntity fileEntity : updatedEntities) {
            update(user, fileEntity);
        }
    }

    @Override
    public long findCountByCondition(FileEntity condition) {
        if (Objects.isNull(condition)) {
            return 0;
        }
        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(condition.getTenantId());
        if (StringUtils.isNotBlank(condition.getName())) {
            query.field("name").containsIgnoreCase(condition.getName());
        }
        if (Objects.nonNull(condition.getStatus())) {
            query.field("status").equal(condition.getStatus());
        } else {
            query.field("status").notEqual(CmsStatus.DELETE);
        }
        if (Objects.nonNull(condition.getWorkSpace())) {
            query.field("workSpace").equal(condition.getWorkSpace());
        }
        query.order("-createTime");
        return query.countAll();
    }

    @Override
    public List<FileEntity> batchFindFileByApiNames(int tenantId, List<String> apiNameList) {
        if (CollectionUtils.isEmpty(apiNameList)) {
            return Collections.emptyList();
        }
        Query<FileEntity> query = datastore.createQuery(FileEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").in(apiNameList);
        query.field("status").notEqual(CmsStatus.DELETE);
        return query.asList();
    }
}
