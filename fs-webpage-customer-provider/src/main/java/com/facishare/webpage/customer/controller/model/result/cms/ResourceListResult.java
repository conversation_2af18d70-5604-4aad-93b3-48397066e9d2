package com.facishare.webpage.customer.controller.model.result.cms;

import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.webpage.customer.controller.model.vo.cms.ResourceInfoVO;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 资源列表结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ResourceListResult {

    /**
     * 资源列表
     */
    @Builder.Default
    private List<ResourceInfoVO> resourceList = Lists.newArrayList();
    @Builder.Default
    Long totalNum = 0L;
    @Builder.Default
    Integer pageNum = 1;
    @Builder.Default
    Integer pageSize = 20;
    private boolean hasPermission;

}
