package com.facishare.webpage.customer.core.component;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * Created by zhangyu on 2020/6/19
 */
@Data
public abstract class ComponentCovert {

    public abstract String getApiName();

    public abstract String getType();

    public abstract int getLimit();

    public abstract JSONObject getProps();

    public JSONObject buildComponent() {
        JSONObject componentJson = new JSONObject();
        componentJson.put("api_name", getApiName());
        componentJson.put("type", getType());
        componentJson.put("props", getProps());
        componentJson.put("limit", getLimit());

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(getApiName(), componentJson);
        return jsonObject;
    }

}
