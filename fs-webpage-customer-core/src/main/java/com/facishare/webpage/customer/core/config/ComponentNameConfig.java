package com.facishare.webpage.customer.core.config;

import com.alibaba.fastjson.JSONObject;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeListener;
import com.github.autoconf.api.IConfig;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * Created by zhangyu on 2020/4/10
 */
public class ComponentNameConfig {

    private static Logger logger = LoggerFactory.getLogger(ComponentNameConfig.class);

    private static String componentNameConfig = "fs-webpage-component-name-config";

    private static String defaultBICardId = "BI_";

    private Map<String, String> componentNameMap = Maps.newHashMap();

    @PostConstruct
    public void init() {
        IChangeListener listener = config -> loadConfig();
        ConfigFactory.getConfig(componentNameConfig, listener, true);
    }

    private void loadConfig() {

        IConfig config = ConfigFactory.getConfig(componentNameConfig);
        String componentName = config.getString();
        Map<String, List<String>> map = JSONObject.parseObject(componentName, Map.class);
        Map<String, String> cardIdComponentMap = Maps.newHashMap();
        for (Map.Entry<String, List<String>> entry : map.entrySet()) {
            String key = entry.getKey();
            List<String> cardIds = entry.getValue();
            cardIds.stream().forEach(cardId -> {
                cardIdComponentMap.put(cardId, key);
            });
        }
        componentNameMap = cardIdComponentMap;

        logger.info("init LayoutNameConfig config layoutNameMap:{};", componentNameMap);
    }

    public String getComponentName(String componentId) {
        String componentName = componentNameMap.getOrDefault(componentId, "");
        if (StringUtils.isNotEmpty(componentName)){
            return componentName;
        }
        if (componentId.startsWith(defaultBICardId)) {
            return componentNameMap.get(defaultBICardId);
        }

        if (componentId.endsWith("__c")){
            return  "custom_comp";
        }
        return componentId;
    }

    public String getComponentName(String apiName, int widgetType){
        String componentName = componentNameMap.getOrDefault(apiName, "");
        if (StringUtils.isNotEmpty(componentName)){
            return componentName;
        }
        if (StringUtils.isEmpty(componentName) && apiName.startsWith(defaultBICardId)) {
            return componentNameMap.get(defaultBICardId);
        }
        return componentNameMap.getOrDefault(String.valueOf(widgetType), "");
    }

    public static void main(String[] args) {
        System.setProperty("process.profile", "fstest");
        ComponentNameConfig componentNameConfig = new ComponentNameConfig();
        componentNameConfig.init();
        String componentName = componentNameConfig.getComponentName("aa__c");
        System.out.println(componentName);
    }

}
