package com.facishare.webpage.customer.api.service;

import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@RestResource(
        value = "WebPageResource",
        desc = "应用页面服务",//ignoreI18n
        contentType = "application/json"
)
public interface AppPageService {

    @POST(value = "/webPage/AppPageService/getInnerAppPageTemplate", desc = "获取移动端企业内应用页面模板id")//ignoreI18n
    GetInnerAppPageTemplateResult getInnerAppPageTemplate(@HeaderMap Map<String, String> headers, @Body GetInnerAppPageTemplateArg arg);

    @POST(value = "/webPage/AppPageService/getCrossAppPageTemplate", desc = "获取移动端互联应用页面模板id")//ignoreI18n
    GetCrossAppPageTemplateResult getCrossAppPageTemplate(@HeaderMap Map<String, String> headers, @Body GetCrossAppPageTemplateArg arg);

    @POST(value = "/webPage/AppPageService/getVendorAppPageTemplate", desc = "获取移动端多上游下互联应用模板id")//ignoreI18n
    GetVendorAppPageTemplateResult getVendorAppPageTemplate(@HeaderMap Map<String, String> headers, @Body GetVendorAppPageTemplateArg arg);

    @POST(value = "/webPage/AppPageService/getCrossAppPageTemples", desc = "上游管理态获取移动端当前互联应用下所有模板")//ignoreI18n
    GetCrossAppPageTemplesResult getCrossAppPageTemples(@HeaderMap Map<String, String> headers, @Body GetCrossAppPageTemplesArg arg);

    @POST(value = "/webPage/AppPageService/queryInnerWebStatAccessInfo", desc = "查找内部web应用飘数信息")//ignoreI18n
    QueryAppStatAccessInfoResult queryInnerWebStatAccessInfo(@HeaderMap Map<String, String> headers,
                                                             @Body GetInnerAppPageTemplateArg arg);

    @POST(value = "/webPage/AppPageService/queryCrossWebStatAccessInfo", desc = "查找互联web应用飘数信息")//ignoreI18n
    QueryAppStatAccessInfoResult queryCrossWebStatAccessInfo(@HeaderMap Map<String, String> headers,
                                                             @Body GetCrossAppPageTemplateArg arg);

    @POST(value = "/webPage/AppPageService/queryVendorWebStatAccessInfo", desc = "查找多上游web应用飘数信息")//ignoreI18n
    QueryAppStatAccessInfoResult queryVendorWebStatAccessInfo(@HeaderMap Map<String, String> headers,
                                                              @Body GetVendorAppPageTemplateArg arg);


    @POST(value = "/webPage/AppPageService/getEmployeeConfigValueByApiName", desc = "查找多上游web应用飘数信息")//ignoreI18n
    GetEmployeeConfigValueByKeysResult getEmployeeConfigValueByApiName(@HeaderMap Map<String, String> headers,
                                                                       @Body GetEmployeeConfigValueByApiNameArg arg);

    @POST(value = "/webPage/AppPageService/setEmployeeConfigValueByApiName", desc = "查找多上游web应用飘数信息")//ignoreI18n
    SetEmployeeConfigValueResult setEmployeeConfigValueByApiName(@HeaderMap Map<String, String> headers,
                                                                 @Body SetEmployeeConfigValueByApiNameArg arg);

    @POST(value = "/webPage/AppPageService/getAppPageInfoByUserHighestPageTemple", desc = "查找多上游web应用飘数信息")//ignoreI18n
    GetAppPageInfoByUserHighestPageTempleResult getAppPageInfoByUserHighestPageTemple(@HeaderMap Map<String, String> headers,
                                                                 @Body GetAppPageInfoByUserHighestPageTempleArg arg);

    @POST(value = "/webPage/AppPageService/getEmployeeConfigValueByApiNameHasNotDefault", desc = "查询个人级筛选器设计，不补充默认筛选器")//ignoreI18n
    GetEmployeeConfigValueByKeysResult getEmployeeConfigValueByApiNameHasNotDefault(@HeaderMap Map<String, String> headers,
                                                                       @Body GetEmployeeConfigValueByApiNameArg arg);

}
