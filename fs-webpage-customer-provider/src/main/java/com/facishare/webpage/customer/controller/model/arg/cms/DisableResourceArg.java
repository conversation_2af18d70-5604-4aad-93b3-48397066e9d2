package com.facishare.webpage.customer.controller.model.arg.cms;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 禁用资源参数
 */
@Data
public class DisableResourceArg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 禁用资源列表
     */
    private List<DisableResourceItem> resourceList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DisableResourceItem {
        /**
         * 文件ApiName
         */
        private String apiName;

        /**
         * 是否递归禁用子文件，仅当type=folder时有效，默认true
         */
        private Boolean recursive = true;
    }
}
