package com.facishare.webpage.customer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.converter.EIEAConverter;
import com.facishare.organization.adapter.api.permission.model.CheckHasManageAbility;
import com.facishare.organization.adapter.api.permission.service.PermissionService;
import com.facishare.organization.api.exception.OrganizationException;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.BatchGetDepartmentByPrincipalArg;
import com.facishare.organization.api.model.department.arg.BatchGetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.BatchGetDepartmentByPrincipalResult;
import com.facishare.organization.api.model.department.result.BatchGetDepartmentDtoResult;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.model.param.BatchGetUpperDepartmentIdsMap;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.organization.api.util.enums.NameLanguageEnum;
import com.facishare.paas.I18N;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.Result.ParaInfoResult;
import com.facishare.paas.license.arg.QueryModuleArg;
import com.facishare.paas.license.arg.QueryModuleParaArg;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.common.LicenseObjectInfoContext;
import com.facishare.paas.license.common.Result;
import com.facishare.paas.license.exception.PaasMessage;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.facishare.qixin.common.monitor.SlowLog;
import com.facishare.qixin.objgroup.common.service.CusComponentService;
import com.facishare.qixin.objgroup.common.service.PaasOrgGroupService;
import com.facishare.qixin.objgroup.common.service.model.GetGroupListResult;
import com.facishare.qixin.objgroup.common.service.model.resource.QueryComponentsResult;
import com.facishare.qixin.objgroup.common.service.utils.PaasMetaUtils;
import com.facishare.qixin.permission.filter.PermissionFilterManager;
import com.facishare.qixin.permission.model.FunctionPermissionArg;
import com.facishare.qixin.relation.rest.EnterpriseRelationRoleService;
import com.facishare.qixin.relation.rest.RelationApiNamesConfig;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.facishare.warehouse.api.model.arg.CopyFileFromOneEnterpriseToOther;
import com.facishare.warehouse.api.model.arg.User;
import com.facishare.warehouse.api.service.FileUnityService;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.model.PaaSAppConfig;
import com.facishare.webpage.customer.api.model.core.SimpObjectDescription;
import com.facishare.webpage.customer.api.utils.WebPageUtils;
import com.facishare.webpage.customer.common.resource.UdObjRestResource;
import com.facishare.webpage.customer.common.resource.model.arg.FindControlLevelArg;
import com.facishare.webpage.customer.common.resource.model.arg.GetBusComponentsArg;
import com.facishare.webpage.customer.common.resource.model.result.ControlLevelResult;
import com.facishare.webpage.customer.common.resource.model.result.GetBusComponentsResult;
import com.facishare.webpage.customer.constant.ComponentConstant;
import com.facishare.webpage.customer.constant.ObjectLayoutTypeEnum;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.core.constant.Constant;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.remote.GroupService;
import com.facishare.webpage.customer.remote.ObjectService;
import com.facishare.webpage.customer.remote.RoleService;
import com.facishare.webpage.customer.service.RemoteCrossService;
import com.facishare.webpage.customer.service.RemoteService;
import com.facishare.webpage.customer.util.MenuUtil;
import com.facishare.webpage.customer.util.TempleIdUtil;
import com.fxiaoke.common.Pair;
import com.fxiaoke.enterpriserelation2.result.SimpleLinkAppResult;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhangyi on 2019/9/24.
 */
public class RemoteServiceImpl implements RemoteService {
    private static final Logger logger = LoggerFactory.getLogger(RemoteServiceImpl.class);

    private static String GoalBoard = "GoalBoard";
    private static String GoalValueObj = "GoalValueObj";
    private static String HighSeasObj = "HighSeasObj";
    private static String LeadsPoolObj = "LeadsPoolObj";

    private static final String TN_PATH_PREFIX = "TN_";
    private static final String N_PATH_PREFIX = "N_";

    private static final FsGrayReleaseBiz UDOBJ_GRAY = FsGrayRelease.getInstance("udobj");

    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;

    private static int defaultDepartmentId = 999999;

    @Resource
    private EmployeeProviderService employeeProviderService;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private PermissionFilterManager permissionFilterManager;
    @Autowired
    private EnterpriseRelationRoleService enterpriseRelationRoleService;
    @Resource
    private PaasOrgGroupService paasOrgGroupService;
    @Resource
    private DepartmentProviderService departmentProviderService;
    @Autowired
    private RelationApiNamesConfig relationApiNamesConfig;
    @Autowired
    private RoleService roleService;
    @Autowired
    private GroupService groupService;
    @Resource
    private ObjectService objectService;
    @Resource
    private CusComponentService cusComponentService;
    @Resource
    private UdObjRestResource udObjRestResource;
    @Resource
    private RemoteCrossService remoteCrossService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private FileUnityService fileUnityService;
    @Resource
    private EIEAConverter eieaConverter;

    @Override
    public boolean checkUserPermission(int tenantId, String appId, long outerTenantId, long outerUserId, List<String> scopes) {
        logger.debug("checkUserPermission tenantId {}, appId {}, outerTenantId {}, outerUserId {}, scopes {}", tenantId, appId, outerTenantId, outerUserId, scopes);
        if (CollectionUtils.isEmpty(scopes)) {
            return true;
        }
        List<String> userOuterRoleIds = remoteCrossService.getUserOuterRoleIds(tenantId, outerTenantId, outerUserId, appId);
        if (CollectionUtils.isEmpty(userOuterRoleIds)) {
            return false;
        }
        scopes = scopes.stream().filter(x -> userOuterRoleIds.contains(x)).collect(Collectors.toList());
        logger.debug("checkUserPermission scopes{}", scopes);
        return CollectionUtils.isNotEmpty(scopes);
    }

    @Override
    public List<String> getAllObjectByAppId(String appId, int tenantId, String upEa, Integer linkType) {
        List<String> apNames = enterpriseRelationRoleService.getAllOutRelationObject(tenantId, upEa, appId, linkType);
        return relationApiNamesConfig.getSortedApiNames(apNames);
    }

    @Override
    public List<String> getObjectApiNamesByLicense(int tenantId) {
        List<String> apNames = Lists.newArrayList();
        LicenseContext licenseContext = buildLicenseContext(tenantId);
        LicenseObjectInfoContext arg = new LicenseObjectInfoContext();
        arg.setContext(licenseContext);
        arg.setCrmKey("crm_manage_custom_object");
        arg.setModuleType("0");
        Result<Set<String>> queryApiNameByLicense = this.licenseClient.queryApiNameByLicense(arg);
        if (!Objects.isNull(queryApiNameByLicense) && this.isSuccess(queryApiNameByLicense) && queryApiNameByLicense.getResult() != null) {
            if (queryApiNameByLicense.getResult() == null) {
                logger.error("queryApiNameByLicense result:{}", queryApiNameByLicense.getResult());
                return apNames;
            }
            apNames = new ArrayList<>(queryApiNameByLicense.getResult());
        }
        return apNames;
    }

    private LicenseContext buildLicenseContext(int tenantId) {
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(String.valueOf(tenantId));
        licenseContext.setUserId("-10000");
        return licenseContext;
    }

    @Override
    public Map<String, SimpObjectDescription> getDescribeByApiNames(Integer enterpriseId, List<String> apiNames, Locale locale) {
        Map<String, SimpObjectDescription> objectDescribeMap = new HashMap<>();
        List<SimpObjectDescription> describes = objectService.getAllDescribe(enterpriseId, locale);
        if (CollectionUtils.isNotEmpty(describes) && CollectionUtils.isNotEmpty(apiNames)) {
            Map<String, SimpObjectDescription> describeMap = describes.stream().collect(Collectors.toMap(SimpObjectDescription::getApiName, objectDescribe -> objectDescribe, (key1, key2) -> key2));
            apiNames.stream().forEach(x -> {
                SimpObjectDescription objectDescribe = (SimpObjectDescription) MapUtils.getObject(describeMap, x, null);
                if (ObjectUtils.isNotEmpty(objectDescribe)) {
                    objectDescribeMap.put(x, objectDescribe);
                }
            });
        }
        return objectDescribeMap;
    }


    @Override
    public Map<Integer, String> getEmployeeName(int tenantId, List<Integer> employeeIds) {
        if (CollectionUtils.isEmpty(employeeIds)) {
            return Maps.newHashMap();
        }
        BatchGetEmployeeDtoArg employeeArg = new BatchGetEmployeeDtoArg();
        employeeArg.setEnterpriseId(tenantId);
        employeeArg.setEmployeeIds(employeeIds);
        employeeArg.setRunStatus(RunStatus.ACTIVE);
        BatchGetEmployeeDtoResult batchGetSimpleEmployeeDto;
        HashMap<Integer, String> employeeMap = new HashMap<>();
        try {
            batchGetSimpleEmployeeDto = employeeProviderService.batchGetEmployeeDto(employeeArg);
        } catch (OrganizationException e) {
            logger.error("batchGetSimpleEmployeeDto error by employeeArg {}", employeeArg, e);
            return Maps.newHashMap();
        }
        if (ObjectUtils.isNotEmpty(batchGetSimpleEmployeeDto) || CollectionUtils.isNotEmpty(batchGetSimpleEmployeeDto.getEmployeeDtos())) {
            batchGetSimpleEmployeeDto.getEmployeeDtos().stream().forEach(employeeDto -> {
                employeeMap.put(employeeDto.getEmployeeId(), employeeDto.getName());
            });
        }
        return employeeMap;
    }

    @Override
    public Map<Integer, String> getTenantAccountNames(List<Integer> tenantIds) {
        Map<Integer, String> map = new HashMap<>();
        BatchGetSimpleEnterpriseDataArg dataArg = new BatchGetSimpleEnterpriseDataArg();
        dataArg.setEnterpriseIds(tenantIds);
        BatchGetSimpleEnterpriseDataResult dataResult = null;
        try {
            dataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(dataArg);
        } catch (Exception e) {
            logger.error("batchGetSimpleEnterpriseData error by dataArg {}", dataArg, e);
        }
        if (ObjectUtils.isNotEmpty(dataResult) && CollectionUtils.isNotEmpty(dataResult.getSimpleEnterpriseList())) {
            dataResult.getSimpleEnterpriseList().stream().forEach(simpleEnterpriseData -> {
                map.put(simpleEnterpriseData.getEnterpriseId(), simpleEnterpriseData.getEnterpriseName());
            });
        }
        return map;
    }

    @Override
    public Map<String, List<String>> permissionFuncAccess(UserInfo userInfo,
                                                          OuterUserInfo outerUserInfo,
                                                          String appId,
                                                          List<String> apiNames,
                                                          List<String> actionCodes,
                                                          Map<String, List<String>> funcAccessWithMenuItems) {
        FunctionPermissionArg arg = new FunctionPermissionArg();
        arg.setAppId(appId);
        arg.setEnterpriseId(userInfo.getEnterpriseId());
        arg.setObjectActionCodesMap(funcAccessWithMenuItems);

        if (outerUserInfo == null) {
            arg.setRelation(false);
            arg.setEmployeeId(userInfo.getEmployeeId());
        } else {
            Long outUserId = outerUserInfo.getOutUserId();
            arg.setRelation(true);
            arg.setOutTenantId(outerUserInfo.getOutTenantId());
            arg.setOutUserId(outUserId);
            arg.setEmployeeId(outerUserInfo.getUpstreamOwnerId() == null ? 0 : outerUserInfo.getUpstreamOwnerId());
            arg.setOutIdentityType(String.valueOf(outerUserInfo.getOutLinkType()));

        }
        arg.setApiNames(apiNames);
        arg.setFunctionCodes(actionCodes);
        Map<String, List<String>> filterFunctionPermission = Maps.newHashMap();
        try {
            Map<String, List<String>> functionPermission = permissionFilterManager.filterFunctionPermission(arg);
            for (Map.Entry<String, List<String>> entry : functionPermission.entrySet()) {
                if (entry.getValue().contains(WebPageConstants.FUNC_CODE_LIST)) {
                    filterFunctionPermission.put(entry.getKey(), entry.getValue());
                }
            }
        } catch (Exception e) {
            logger.error("permissionFuncAccess error by userInfo:{}, outerUserInfo:{}, appId:{}, apiNames:{}, actionCodes:{}, funcAccessWithMenuItems:{}",
                    userInfo, outerUserInfo, appId, apiNames, actionCodes, funcAccessWithMenuItems);
        }
        if (WebPageConstants.APP_CRM.equals(appId)) {
            List<String> goalValueObjCodes = filterFunctionPermission.get(GoalValueObj);
            filterFunctionPermission.put(GoalBoard, goalValueObjCodes);
        }

        return filterFunctionPermission;
    }

    @Override
    public List<Integer> getMainDepartmentIdsByEmployee(int tenantId, int employeeId) {
        if (WebPageUtils.isOutUser(employeeId)) {
            return Lists.newArrayList();
        }

        GetEmployeeDtoArg employeeDtoArg = new GetEmployeeDtoArg();
        employeeDtoArg.setEnterpriseId(tenantId);
        employeeDtoArg.setEmployeeId(employeeId);
        GetEmployeeDtoResult employeeDtoResult;
        try {
            employeeDtoResult = employeeProviderService.getEmployeeDto(employeeDtoArg);
        } catch (Exception e) {
            logger.error("getEmployeeDto error by tenantId:{},employeeId:{}", tenantId, employeeId, e);
            return Lists.newArrayList();
        }
        EmployeeDto employeeDto = employeeDtoResult.getEmployeeDto();
        if (null == employeeDto || CollectionUtils.isEmpty(employeeDto.getDepartmentIds())) {
            logger.warn("getEmployeeDto failed by tenantId:{},employeeId:{},employeeDto:{} ", tenantId, employeeId, employeeDto);
            return Lists.newArrayList();
        }
        List<Integer> departmentIds = employeeDto.getDepartmentIds();
        if (CollectionUtils.isEmpty(departmentIds)) {
            departmentIds.add(defaultDepartmentId);
        }
        return departmentIds;
    }

    @Override
    public List<Integer> getDepartmentIdsByEmployee(int tenantId, int employeeId) {

        List<Integer> departmentIds = getMainDepartmentIdsByEmployee(tenantId, employeeId);

        BatchGetUpperDepartmentIdsMap.Arg batchGetUpperDepartmentDtosArg = new BatchGetUpperDepartmentIdsMap.Arg();
        batchGetUpperDepartmentDtosArg.setDepartmentIds(departmentIds);
        batchGetUpperDepartmentDtosArg.setEnterpriseId(tenantId);
        batchGetUpperDepartmentDtosArg.setSelf(true);
        batchGetUpperDepartmentDtosArg.setRunStatus(RunStatus.ACTIVE);
        BatchGetUpperDepartmentIdsMap.Result batchGetUpperDepartmentDtosResult;
        try {
            batchGetUpperDepartmentDtosResult = departmentProviderService.batchGetUpperDepartmentIdsMap(batchGetUpperDepartmentDtosArg);
        } catch (Exception e) {
            logger.error("batchGetUpperDepartmentDtos error by batchGetUpperDepartmentDtosArg {}", batchGetUpperDepartmentDtosArg, e);
            return departmentIds;
        }
        List<Integer> upDepartmentIds = Optional.ofNullable(batchGetUpperDepartmentDtosResult)
                .map(BatchGetUpperDepartmentIdsMap.Result::getUpperDepartmentIdMap)
                .map(Map::values)
                .map(it -> it.stream().flatMap(Collection::stream).distinct().collect(Collectors.toList()))
                .orElse(Lists.newArrayList());
        departmentIds.addAll(upDepartmentIds);
        return departmentIds;
    }

    @Override
    public List<String> getRoleIdsByEmployee(int tenantId, int employeeId) {
        return roleService.getRoleIds(tenantId, employeeId);
    }

    @Override
    public List<String> getGroupIdsByEmployee(int tenantId, int employeeId) {
        return groupService.getGroupIds(tenantId, employeeId);
    }

    @Override
    public Map<Integer, String> getDepartmentsName(int tenantId, Collection<Integer> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return Maps.newHashMap();
        }
        BatchGetDepartmentDtoArg batchGetDepartmentDtoArg = new BatchGetDepartmentDtoArg();
        batchGetDepartmentDtoArg.setEnterpriseId(tenantId);
        batchGetDepartmentDtoArg.setDepartmentIds(departmentIds);
        batchGetDepartmentDtoArg.setRunStatus(RunStatus.ACTIVE);
        BatchGetDepartmentDtoResult departmentDtoResult;
        try {
            departmentDtoResult = departmentProviderService.batchGetDepartmentDto(batchGetDepartmentDtoArg);
        } catch (Exception e) {
            logger.error("batchGetDepartmentDto error by batchGetDepartmentDtoArg {}", batchGetDepartmentDtoArg, e);
            return Maps.newHashMap();
        }
        HashMap<Integer, String> departmentMap = new HashMap<>();
        List<DepartmentDto> departments = departmentDtoResult.getDepartments();
        departments.stream().forEach(departmentDto -> {
            departmentMap.put(departmentDto.getDepartmentId(),
                    StringUtils.firstNonBlank(
                            NameLanguageEnum.getNameLang(I18N.getContext().getLanguage(), departmentDto.getDepartmentNameLanguage()),
                            departmentDto.getName()));
        });
        return departmentMap;
    }

    @Override
    public Map<String, String> getGroupsName(int tenantId, String appId, List<String> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Maps.newHashMap();
        }
        List<GetGroupListResult.Group> groupDetailList;
        try {
            groupDetailList = paasOrgGroupService.getGroupDetailList(tenantId, appId, groupIds);
        } catch (Exception e) {
            logger.error("getGroupDetailList error by tenantId {}, appId {}, groupIds {} ", tenantId, appId, groupIds, e);
            return Maps.newHashMap();
        }
        Map<String, String> groupMap = Maps.newHashMap();
        groupDetailList.stream().forEach(group -> {
            groupMap.put(group.getId(), group.getName());
        });
        return groupMap;
    }

    @Override
    public List<String> getVersionAndPackages(int tenantId) {
        List<ProductVersionPojo> versionPojoList;
        LicenseContext context = buildLicenseContext(tenantId);
        QueryProductArg arg = new QueryProductArg();
        arg.setLicenseContext(context);
        LicenseVersionResult licenseVersionResult = licenseClient.queryProductVersion(arg);
        if (!Objects.isNull(licenseVersionResult) && isSuccess(licenseVersionResult) && CollectionUtils.isNotEmpty(licenseVersionResult.getResult())) {
            versionPojoList = licenseVersionResult.getResult();
        } else {
            versionPojoList = Lists.newArrayList();
        }
        return versionPojoList.stream().map(ProductVersionPojo::getCurrentVersion).collect(Collectors.toList());

    }

    @Override
    public List<ProductVersionPojo> getProductionVersion(int tenantId) {
        try {
            LicenseContext licenseContext = new LicenseContext();
            licenseContext.setAppId("CRM");
            licenseContext.setTenantId(String.valueOf(tenantId));
            QueryProductArg queryProductArg = new QueryProductArg();
            queryProductArg.setLicenseContext(licenseContext);
            LicenseVersionResult versionResult = licenseClient.queryProductVersion(queryProductArg);
            if (versionResult == null || CollectionUtils.isEmpty(versionResult.getResult())) {
                return Lists.newArrayList();
            }
            return versionResult.getResult();
        } catch (Exception e) {
            logger.error("queryProductVersion error by tenantId:{}", tenantId);
        }
        return Lists.newArrayList();
    }

    @Override
    public Set<String> licenseModule(int tenantId) {
        LicenseContext context = this.buildLicenseContext(tenantId);
        QueryModuleArg arg = new QueryModuleArg();
        arg.setLicenseContext(context);
        ModuleInfoResult moduleInfoResult = licenseClient.queryModule(arg);
        if (!Objects.isNull(moduleInfoResult) && isSuccess(moduleInfoResult) && CollectionUtils.isNotEmpty(moduleInfoResult.getResult())) {
            return moduleInfoResult.getResult().stream().map(ModuleInfoPojo::getModuleCode).collect(Collectors.toSet());
        } else {
            return Sets.newHashSet();
        }
    }

    @Override
    public int getQuotaByModule(int tenantId, String paraKey) {
        LicenseContext context = this.buildLicenseContext(tenantId);
        QueryModuleParaArg arg = new QueryModuleParaArg();
        arg.setContext(context);
        arg.setParaKeys(Sets.newHashSet(new String[]{paraKey}));
        ParaInfoResult paraInfoResult = licenseClient.queryModulePara(arg);
        if (!Objects.isNull(paraInfoResult) && isSuccess(paraInfoResult) && CollectionUtils.isNotEmpty(paraInfoResult.getResult())) {
            return Integer.parseInt((paraInfoResult.getResult().get(0)).getParaValue());
        } else {
            return 0;
        }
    }

    @Override
    public Map<String, Object> getHomePermissionsByTenantId(UserInfo userInfo, ClientInfo clientInfo, List<String> apiNames, SlowLog stopWatch) {
        Map<String, Object> map = Maps.newHashMap();
        try {
            Map<String, List<String>> funcAccess = permissionFuncAccess(
                    userInfo, null, WebPageConstants.APP_CRM, Lists.newArrayList("DuplicateCheckObj", "PriceBookObj"),
                    Lists.newArrayList(WebPageConstants.FUNC_CODE_LIST, "PriceTool"), null);
            stopWatch.lap("over funcAccess");
            if (funcAccess != null && CollectionUtils.isNotEmpty(funcAccess.get("DuplicateCheckObj"))) {
                map.put("IsShowDuplicateSearch", true);
            } else {
                map.put("IsShowDuplicateSearch", false);
            }
            List<String> priceBookProductObjFunc = funcAccess.getOrDefault("PriceBookObj", Lists.newArrayList());
            map.put("IsPriceTool", priceBookProductObjFunc.contains("PriceTool") ? true : false);
            Map<String, Object> poolPrivilegeData = getPoolPrivilegeData(apiNames);
            map.putAll(poolPrivilegeData);
        } catch (Exception e) {
            logger.error("getHomePermissionsByTenantId error by userInfo:{}", userInfo, e);
        }
        return map;
    }


    @Override
    public List<Integer> getResponsibleDepartmentId(int tenantId, List<Integer> employeeIds) {
        if (CollectionUtils.isEmpty(employeeIds)) {
            return Lists.newArrayList();
        }
        BatchGetDepartmentByPrincipalArg arg = new BatchGetDepartmentByPrincipalArg();
        arg.setPrincipalIds(employeeIds);
        arg.setEnterpriseId(tenantId);
        arg.setRunStatus(RunStatus.ACTIVE);
        List<Integer> departmentIds = Lists.newArrayList();
        try {
            BatchGetDepartmentByPrincipalResult result = departmentProviderService.batchGetDepartmentByPrincipal(arg);
            departmentIds = result.getDepartments().stream()
                    .map(departmentDto -> departmentDto.getDepartmentId())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("batchGetDepartmentByPrincipal error", e);
        }

        return departmentIds;
    }

    @Override
    public SimpObjectDescription getSimpObjectDescribe(int tenantId, String apiName) {
        SimpObjectDescription simpObjectDescription = null;
        try {
            List<SimpObjectDescription> simpObjectDescriptions = objectService.getAllDescribe(tenantId, Locale.CHINA);
            for (SimpObjectDescription objectDescribe : simpObjectDescriptions) {
                if (apiName.equals(objectDescribe.getApiName())) {
                    simpObjectDescription = objectDescribe;
                    break;
                }
            }
        } catch (Exception e) {
            logger.error("getCrmMenu error by tenantId:{}, apiName:{}", tenantId, apiName, e);
        }
        return simpObjectDescription;
    }

    private Pair<String, String> getCustomComponentTypeAndObj(int tenantId, int appType, String appId) {
        String targetScope = getCustomComponentType(appType, appId);
        String objectApiName = getObjectApiName(appType, appId);

        if (isCrossPortalPage(appId, tenantId)) {
            objectApiName = "PortalPage";
            targetScope = "PortalPage";
        }
        if (BizType.isWebsite(appType)) {
            BizType bizTypeValue = BizType.getBizTypeValue(appType);
            if (bizTypeValue != null) {
                objectApiName = "*";
                targetScope = "Website";
            }
        }

        return Pair.of(targetScope, objectApiName);
    }

    private String getCustomComponentType(int appType, String appId) {
        if (BizType.CUSTOMER.getType() == appType) {
            return TempleIdUtil.getCustomerAppId(BizType.CUSTOMER.getType(), appId);
        }
        if (ComponentConstant.DASHBOARD.equals(appId)) {
            return ComponentConstant.DASHBOARD_PAGE;
        }
        if (BizType.CRM.getType() == appType) {
            return ComponentConstant.CRM_PAGE;
        }
        return BizType.CUSTOMER.getDefaultAppId();
    }

    private String getObjectApiName(int appType, String appId) {
        if (BizType.CUSTOMER.getType() == appType) {
            return TempleIdUtil.getObjectApiName(BizType.CUSTOMER.getType(), appId);
        }
        return "*";
    }

    private boolean isCrossPortalPage(String appId, int tenantId) {
        if ("CrossPortalPage".equals(appId)) {
            return true;
        }
        List<String> linkAppIds = remoteCrossService.getUpSimpleLinkApp(tenantId)
                .stream()
                .map(SimpleLinkAppResult::getAppId)
                .collect(Collectors.toList());
        return linkAppIds.contains(appId);
    }

    @Override
    public List<QueryComponentsResult.ComponentData> getCusComponentList(int tenantId, int appType, String appId) {
        Pair<String, String> pair = getCustomComponentTypeAndObj(tenantId, appType, appId);
        String targetScope = pair.getT1();
        String objectApiName = pair.getT2();
        if (StringUtils.isEmpty(targetScope)) {
            return Lists.newArrayList();
        }
        try {
            return cusComponentService.getComponentList(tenantId, targetScope, objectApiName, Lists.newArrayList(Constant.APP, Constant.WEB));
        } catch (Exception e) {
            logger.error("getComponentList error by tenantId:{}, targetScope:{}, objectApiName:{}", tenantId, targetScope, objectApiName, e);
        }
        return Lists.newArrayList();
    }


    @Override
    public List<JSONObject> getBusinessComponents(int tenantId, int appType, String appId, Locale locale) {
        String objectApiName = TempleIdUtil.getObjectApiName(appType, appId);
        String webPageKey = TempleIdUtil.getPrefixByAppId(appType, appId);
        if (StringUtils.isEmpty(objectApiName) || StringUtils.isEmpty(webPageKey)) {
            return Lists.newArrayList();
        }
        try {
            GetBusComponentsArg arg = new GetBusComponentsArg();
            arg.setObjectApiName(objectApiName);
            arg.setLayoutType(ObjectLayoutTypeEnum.getBusinessKeyByWebPageKey(webPageKey));
            //重新设置TraceContext多语
            TraceContext.get().setLocale(StringUtils.isNotEmpty(locale.toLanguageTag()) ? locale.toLanguageTag() : TraceContext.get().getLocale());
            GetBusComponentsResult result = udObjRestResource.getBusComponents(arg, PaasMetaUtils.getHeaders(String.valueOf(tenantId), locale));
            if (result.getData() == null) {
                return Lists.newArrayList();
            } else {
                return result.getData().getComponents();
            }
        } catch (Exception e) {
            logger.error("getBusinessComponents error by tenantId : {}, objectApiName : {}, locale :{}", tenantId, appId, locale, e);
        }

        return Lists.newArrayList();
    }

    @Override
    public Map<String, List<String>> getFunctionCode(UserInfo userInfo, String appId, List<MetaMenuData> metaMenuDataList) {
        List<String> apiNames = metaMenuDataList.stream().map(metaMenuData -> metaMenuData.getApiName()).collect(Collectors.toList());
        Map<String, List<String>> functionCodesMap = MenuUtil.getFuncAccessWithMenuItems(metaMenuDataList);
        Map<String, List<String>> funcAccess = permissionFuncAccess(
                userInfo, null, appId, apiNames,
                Lists.newArrayList(WebPageConstants.FUNC_CODE_ADD, WebPageConstants.FUNC_CODE_LIST),
                functionCodesMap);
        return funcAccess;
    }

    @Override
    public boolean checkHasAbilityForNewManagement(int tenantId, int employeeId) {
        try {
            CheckHasManageAbility.Argument argument = new CheckHasManageAbility.Argument();
            argument.setEnterpriseId(tenantId);
            argument.setCurrentEmployeeId(employeeId);
            CheckHasManageAbility.Result result = permissionService.checkHasAbilityForNewManagement(argument);
            return result.getHasAbility();
        } catch (Exception e) {
            logger.error("checkHasAbilityForNewManagement tenantId:{},employeeId:{}", tenantId, employeeId, e);
        }
        return false;
    }

    @Override
    public void copyFileNPathToOther(int toEi, int fromEi, String nPath) {
        String toEa = eieaConverter.enterpriseIdToAccount(toEi);
        String fromEa = eieaConverter.enterpriseIdToAccount(fromEi);
        if (StringUtils.isBlank(nPath)) {
            return;
        }
        if (!(nPath.startsWith(N_PATH_PREFIX) || nPath.startsWith(TN_PATH_PREFIX))) {
            return;
        }
        try {
            User user = new User();
            user.setEmployId(-10000);
            user.setEnterpriseAccount(fromEa);
            CopyFileFromOneEnterpriseToOther.NDownloadFileArg downloadFile = new CopyFileFromOneEnterpriseToOther.NDownloadFileArg();
            downloadFile.setEa(fromEa);
            downloadFile.setnPath(nPath);
            downloadFile.setUser(user);
            CopyFileFromOneEnterpriseToOther.NUploadFileArg uploadFile = new CopyFileFromOneEnterpriseToOther.NUploadFileArg();
            uploadFile.setEa(toEa);
            uploadFile.setNamedFilePath(nPath);
            fileUnityService.CopyFileFromOneEnterpriseToOther(downloadFile, uploadFile);
        } catch (Exception e) {
            logger.error("copyFileNPathToOther, toEa:{}, fromEa:{}, nPath:{}",
                    toEa, fromEa, nPath, e);
        }
    }

    @Override
    public List<PaaSAppConfig> findPaaSAppConfigs(Integer tenantId) {
        List<ControlLevelResult.ControlLevelItem> controlLevel = findControlLevel(tenantId, null);
        if (CollectionUtils.isEmpty(controlLevel)) {
            return Collections.emptyList();
        }
        return controlLevel.stream()
                .map(this::fromControlLevelItem)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public PaaSAppConfig findPaaSAppConfig(Integer tenantId, String appId) {
        List<ControlLevelResult.ControlLevelItem> controlLevel = findControlLevel(tenantId, appId);
        if (CollectionUtils.isEmpty(controlLevel)) {
            return null;
        }
        return controlLevel.stream()
                .map(this::fromControlLevelItem)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    private List<ControlLevelResult.ControlLevelItem> findControlLevel(Integer tenantId, String appId) {
        try {
            String ei = String.valueOf(tenantId);
            if (!UDOBJ_GRAY.isAllow("configuration_package", ei)) {
                return Lists.newArrayList();
            }
            Map<String, String> headers = PaasMetaUtils.getHeaders(ei);
            FindControlLevelArg arg = FindControlLevelArg.builder()
                    .resourceType("PaaSAppEntity")
                    .primaryKey(appId)
                    .build();
            ControlLevelResult result = udObjRestResource.findControlLevel(headers, arg);
            if (!result.success()) {
                return Lists.newArrayList();
            }
            return Optional.of(result)
                    .map(ControlLevelResult::getData)
                    .map(ControlLevelResult.Result::getControlLevelInfos)
                    .orElse(Collections.emptyList());
        } catch (Exception e) {
            logger.error("findControlLevel fail! ei:{}, appId:{}", tenantId, appId, e);
        }
        return Lists.newArrayList();
    }

    private PaaSAppConfig fromControlLevelItem(ControlLevelResult.ControlLevelItem controlLevelItem) {
        if (Objects.isNull(controlLevelItem)) {
            return null;
        }
        PaaSAppConfig paaSAppConfig = new PaaSAppConfig();
        paaSAppConfig.setAppId(controlLevelItem.getPrimaryKey());
        paaSAppConfig.setControlLevel(controlLevelItem.getControlLevel());
        return paaSAppConfig;
    }

    private boolean isSuccess(Result result) {
        return Objects.equals(Integer.valueOf(result.getErrCode()), Integer.valueOf(PaasMessage.SUCCESS.getCode()));
    }

    private Map<String, Object> getPoolPrivilegeData(List<String> apiNames) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("ShowHighSeas", apiNames.contains(HighSeasObj) ? true : false);
        map.put("ShowSalesCluePool", apiNames.contains(LeadsPoolObj) ? true : false);
        return map;
    }
}
