package com.facishare.webpage.customer.api.exception;

import com.facishare.cep.plugin.exception.BizException;

import java.util.List;

public class WebPageException extends BizException {


    public WebPageException(String failureMessage, int failureCode) {
        super(failureMessage, failureCode);
    }

    public WebPageException(String failureMessage, int failureCode, Object body) {
        super(failureMessage, failureCode, body);
    }

    public WebPageException(String errorCode) {
        super(errorCode);
    }

    public WebPageException(String errorCode, List<String> errorBody) {
        super(errorCode, errorBody);
    }

    public WebPageException(String errorCode, int failureCode, String failureMessage) {
        super(errorCode, failureCode, failureMessage);
    }

    public WebPageException(String errorCode, List<String> errorBody, int failureCode, String failureMessage) {
        super(errorCode, errorBody, failureCode, failureMessage);
    }

    public WebPageException(String errorCode, List<String> errorBody, int failureCode, String failureMessage, Object body) {
        super(errorCode, errorBody, failureCode, failureMessage, body);
    }
}