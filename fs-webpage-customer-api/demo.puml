@startuml
!theme plain
title BI组件itemI18nKeys刷数据流程图

start

:接收请求参数;

partition "1. 路由企业" {
  if (用户是否传企业列表?) then (是)
    :查询该企业信息;
    :将企业id放到x-fs-ei中;
    :调用刷库接口;
  else (否)
    :查询全网企业环境企业;
    :将其路由到对应的环境调刷库接口;
  endif
}

partition "2. 刷库处理" {
  :获取企业ID列表;

  :将企业idList分批
  每批100个企业;

  while (还有企业批次?) is (是)
    :处理当前批次企业;

    while (处理批次中的企业) is (继续)
      :查询HomePageLayout表数据;

      :循环解析customerLayout;
      :循环解析customerLayoutList;

      :获取BI组件;
      note right: 使用isBICard方法判断

      :过滤出itemI18nKeyList为空的组件id;

      if (有需要处理的BI组件?) then (是)
        :收集BI组件ID到集合;

        :调用 /api/v1/i18n/getI18nKeys接口
        批量获取itemI18nKeyList;

        if (接口调用成功?) then (是)
          :循环所有HomePageLayoutList数据;
          :将itemI18nKeyList塞回BI组件;
          :批量保存数据库;
          :记录成功企业ID;
        else (否)
          :记录失败企业ID和错误信息;
        endif
      else (否)
        :跳过该企业;
      endif
    endwhile (完成)
  endwhile (否)
}

partition "3. 记录状态" {
  :创建Redis key;
  note right: 过期时间为7天

  :设置value为刷完的企业和失败的企业id;

  :保存到Redis;
}

:返回处理结果;



stop

@enduml