package com.facishare.webpage.customer.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Id;

/**
 * 互联应用的角色关联表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SimpleLinkAppRoleVO {
    @Id
    private ObjectId id;
    private Integer tenantId;
    /**
     * 企业互联应用Id
     */
    private String linkAppId;
    private String roleId;

    /**
     * 1 表示预置角色   2表示企业分配的角色
     */
    private Integer type;
    /**
     * 角色类型，1：基础角色，2：预置角色，3：自定义角色
     */
    private Integer roleType;
    private Long updateTime;
    private Long createTime;
}
