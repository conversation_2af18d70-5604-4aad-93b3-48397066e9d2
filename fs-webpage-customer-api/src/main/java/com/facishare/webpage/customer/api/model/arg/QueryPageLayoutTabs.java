package com.facishare.webpage.customer.api.model.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2024/9/26
 * @Description :
 */
@Data
@Builder
@AllArgsConstructor
public class QueryPageLayoutTabs implements Serializable {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        int tenantId;
        String appId;
        int appType;
        int applyType;
        Locale locale;
        boolean includePreCustomPage;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        List<TabResult> tabs;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TabResult {
        String layoutId;
        String layoutName;
        @Deprecated
        Map<String, String> tabs; // api --> name
        List<Tab> tabList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Tab {
        String api;
        String customKey;
        String name;
        List<String> preKeys;
    }

}
