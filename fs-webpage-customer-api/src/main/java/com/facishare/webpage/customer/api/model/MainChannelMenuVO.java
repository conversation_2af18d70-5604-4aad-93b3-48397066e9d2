package com.facishare.webpage.customer.api.model;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by <PERSON>hangyu on 2020/11/13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MainChannelMenuVO {

    private String appId;

    private String openAppId;

    private int appType;

    private String appTypeName;

    private String upEnterpriseAccount;

    private String upTenantName;

    private String name;

    private List<String> searchWords;

    private String description;

    private String url;

    private String icon;
    /**
     * 上传图标类型
     */
    private String uploadIconType;

    private String crossName;

    private boolean hidden;

    /**
     * 兼容之前的字段，但不使用
     */
    @Deprecated
    private String highlightUrl;
    /**
     * 兼容之前的字段，但不使用
     */
    @Deprecated
    private int groupIndex;
    /**
     * 是否为初始页
     */
    private boolean selected;

    private JSONObject newIcon;
    /**
     * 是否有票数
     */
    private Boolean openStats;


    private Boolean showOwnerIconFlag= false;

}
