---
description: 单元测试示例
globs: 
alwaysApply: false
---
# 为每个未使用PowerMock的单测类添加。（已添加过的忽略）
必需导入：
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
 
Mock 示例：
def setupSpec() {
    def i18nClient = Mock(I18nClient)
    def i18nServiceImpl = Mock(I18nServiceImpl)
    Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
    Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
    i18nClient.getAllLanguage() >> []
}
 
# 单测中依赖注入时示例
MetaDataComputeService metaDataComputeService
ObjectDataProxy dataProxy = Mock(ObjectDataProxy)
def setup() {
   metaDataComputeService = new MetaDataComputeServiceImpl(dataProxy: dataProxy)
}
 
# 当被测类中使用 @WebPageGraySwitch.java 类方法判断灰度时，需要参考下面代码进行 mock。注意方法使用通配符 *_ 即可  。
 
必需导入：
import com.fxiaoke.release.FsGrayReleaseBiz
import com.facishare.webpage.customer.core.util.WebPageGraySwitch
 
Mock 示例：
def biz = Mock(FsGrayReleaseBiz)
Whitebox.setInternalState(WebPageGraySwitch, "biz", biz)
biz.isAllow(*_) >> trueOrFalse
 
注意：实际情况请参数 @WebPageGraySwitch.java 类中方法是使用 biz 属性还是 gray 属性。