package com.facishare.webpage.customer.api.model.arg;

import lombok.Data;

import java.io.Serializable;
import java.util.Locale;

@Data
public class HomePageLayoutTransListArg implements Serializable {
    // appType, appId 是固定参数 com.facishare.webpage.customer.api.constant.BizType.CRM
    private Integer appType = 1;
    private String appId = "CRM";

    private Integer ApplyType = 0;  // 互联是1，0是企业应用
    private Integer tenantId;
    private Locale locale;
    private boolean includePreCustomPage;

}
