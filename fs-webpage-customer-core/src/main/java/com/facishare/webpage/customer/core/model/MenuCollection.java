package com.facishare.webpage.customer.core.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.webpage.customer.api.model.core.TenantPrivilege;
import lombok.Data;

import java.util.List;

/**
 * Created by she<PERSON> on 19/12/10.
 */
@Data
public class MenuCollection {

    @JSONField(ordinal = 1)
    private String id;

    @JSONField(ordinal = 2)
    private String name;

    @JSONField(ordinal = 3)
    private String nameI18nKey;

    @JSONField(ordinal = 4)
    private TenantPrivilege tenantPrivilege;

    @JSONField(ordinal = 5)
    private List<String> menus;

    @JSONField(ordinal = 6)
    private int menuSourceType; //1:来源Config配置;2:来源页面模板;3:来源CRM对象4:来源CRM互联对象
    /**
     * @see MenuCollectionType
     */
    @JSONField(ordinal = 7)
    private String type;     //0、不进行过滤；1、过滤出预置对象；2、过滤出自定义对象
}
