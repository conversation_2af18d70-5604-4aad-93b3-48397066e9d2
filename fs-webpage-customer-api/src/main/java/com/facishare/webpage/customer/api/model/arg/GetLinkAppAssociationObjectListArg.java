package com.facishare.webpage.customer.api.model.arg;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GetLinkAppAssociationObjectListArg implements Serializable {
    private static final long serialVersionUID = 1L;
    private String appId;

    private String upstreamEa;
    /**
     * 对象apiName
     */
    private String objectApiName;

    private Integer type;

    /**
     * 对象apiName
     */
    private List<String> objectApiNames;

    /**
     * 企业互联应用Id
     */
    private List<String> appIds;
    private boolean allAppIdFlag;

}
