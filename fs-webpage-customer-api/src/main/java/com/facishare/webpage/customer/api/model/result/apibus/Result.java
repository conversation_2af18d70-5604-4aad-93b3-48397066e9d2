package com.facishare.webpage.customer.api.model.result.apibus;

import lombok.Data;

import java.io.Serializable;

@Data
public class Result<T> implements Serializable {
    private static final long serialVersionUID = 1535964835070227503L;
    private int errCode;
    private String errMessage;
    private T result;
    private T data;
    private String traceId;

    public boolean isSuccess() {
        return errCode == ResultCode.OK.getErrorCode();
    }
}
