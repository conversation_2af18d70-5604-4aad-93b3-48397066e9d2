package com.facishare.webpage.customer.api.model.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetAppListByTypeAndHasEnableAppViewArg implements Serializable {

    private int tenantId;
    private int userId;
    /**
     * AppTypeConstant  1:平台应用 2:互联应用 3:paas应用
     */
    private int type;

    private String lang;

}
