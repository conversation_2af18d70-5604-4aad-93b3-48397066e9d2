package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.arg.GetHomePageLayoutByIdArg;
import com.facishare.webpage.customer.api.model.arg.ModifyHomePageLayoutArg;
import com.facishare.webpage.customer.api.model.arg.SetHomePageLayoutStatusArg;
import com.facishare.webpage.customer.api.model.result.GetHomePageLayoutByIdResult;
import com.facishare.webpage.customer.api.model.result.ModifyHomePageLayoutResult;
import com.facishare.webpage.customer.api.model.result.SetHomePageLayoutStatusResult;
import com.facishare.webpage.customer.controller.model.arg.homepage.*;
import com.facishare.webpage.customer.controller.model.result.homepage.*;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/10
 */
public interface TenantHomePageAction {

    GetHomePageLayoutByIdResult getHomePageLayoutById(UserInfo userInfo, ClientInfo clientInfo, GetHomePageLayoutByIdArg arg);

    @Deprecated
    GetHomePageComponentsResult getHomePageComponents(UserInfo userInfo, ClientInfo clientInfo, GetHomePageComponentsArg arg);

    ModifyHomePageLayoutResult modifyHomePageLayout(UserInfo userInfo, ClientInfo clientInfo, ModifyHomePageLayoutArg arg);

    GetHomePageLayoutListResult getHomePageLayoutList(UserInfo userInfo, ClientInfo clientInfo, GetHomePageListArg arg);

    SetHomePageLayoutStatusResult setHomePageLayoutStatus(UserInfo userInfo, SetHomePageLayoutStatusArg arg);

    DeleteHomePageLayoutResult deleteHomePageLayout(UserInfo userInfo, DeleteHomePageLayoutArg arg);

    CheckCanAddHomePageLayoutResult checkCanAddHomePageLayout(UserInfo userInfo, CheckCanAddHomePageLayoutArg arg);

    GetEmployeeHomePageLayoutListResult getEmployeeHomePageLayoutList(UserInfo userInfo, ClientInfo clientInfo);

    SetEmployeeCurrentHomePageLayoutResult setEmployeeCurrentHomePageLayout(UserInfo userInfo, SetEmployeeCurrentHomePageLayoutArg arg);

    @Deprecated
    ModifyVendorHomePageResult modifyVendorHomePage(UserInfo userInfo, ClientInfo clientInfo, ModifyVendorHomePageArg arg);

    @Deprecated
    GetVendorHomePageByIdResult getVendorHomePageById(UserInfo userInfo, ClientInfo clientInfo, GetVendorHomePageByIdArg arg);

    @Deprecated
    GetVendorComponentsResult getVendorComponents(UserInfo userInfo, ClientInfo clientInfo);

    @Deprecated
    GetHomePageComponentsResult getHomePageComponentList(UserInfo userInfo, ClientInfo clientInfo, GetHomePageComponentsArg arg);

    GetObjectListResult getObjectList(UserInfo userInfo, ClientInfo clientInfo, GetObjectListArg arg);

    Object getOutTemplate(UserInfo userInfo, ClientInfo clientInfo, GetOutTemplateArg arg);

    GetInnerObjectListResult getInnerObjectList(UserInfo userInfo, ClientInfo clientInfo);

    GetDropListItemsResult getDropListItems(UserInfo userInfo, ClientInfo clientInfo, GetDropListItemsArg arg);

    GetObjectLayoutComponentsResult getComponentList(UserInfo userInfo, ClientInfo clientInfo, GetObjectLayoutComponentsArg arg);

    SupportAppLayeredResult supportAppLayered(UserInfo userInfo, ClientInfo clientInfo, SupportAppLayeredArg arg);

    HasLicenseModuleResult checkHasLicenseModule(UserInfo userInfo, HasLicenseModuleArg arg);

    /**
     * 启用个人首页
     *
     * @param userInfo
     * @return
     */
    EnablePersonPageResult enablePersonPage(UserInfo userInfo);

    /**
     * 禁用个人首页
     *
     * @param userInfo
     * @return
     */
    DisablePersonPageResult disablePersonPage(UserInfo userInfo);

    /**
     * 获取个人首页配置
     *
     * @param userInfo
     * @return
     */
    QueryPersonPageConfigResult queryPersonPageConfig(UserInfo userInfo);


}
