package com.facishare.webpage.customer.api.service;

import com.facishare.webpage.customer.api.model.arg.GetHomePageLayoutByIdArg;
import com.facishare.webpage.customer.api.model.arg.MakeHomePageFormalArg;
import com.facishare.webpage.customer.api.model.arg.ModifyHomePageLayoutArg;
import com.facishare.webpage.customer.api.model.arg.SetHomePageLayoutStatusArg;
import com.facishare.webpage.customer.api.model.result.GetHomePageLayoutByIdResult;
import com.facishare.webpage.customer.api.model.result.MakeHomePageFormalResult;
import com.facishare.webpage.customer.api.model.result.ModifyHomePageLayoutResult;
import com.facishare.webpage.customer.api.model.result.SetHomePageLayoutStatusResult;

/**
 * Created by zhangyu on 2019/9/6
 */
public interface HomePageService {

    /**
     * 更新失效时间
     *
     * @param arg
     * @return
     */
    MakeHomePageFormalResult makeHomePageFormal(MakeHomePageFormalArg arg);

    ModifyHomePageLayoutResult modifyHomePageLayout(ModifyHomePageLayoutArg arg);

    GetHomePageLayoutByIdResult getHomePageLayoutById(GetHomePageLayoutByIdArg arg);

    SetHomePageLayoutStatusResult setHomePageLayoutStatus(SetHomePageLayoutStatusArg arg);

    }
