package com.facishare.webpage.customer.api.model.result;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.PageTemplate;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2020/11/27
 */
@Data
public class GetPageTemplesByTypeAndAppIdsResult implements Serializable {

    private List<PageTemplate> pageTemplateList;

}
