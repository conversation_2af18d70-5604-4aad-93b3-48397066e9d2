package com.facishare.webpage.customer.system.datasource;

import com.facishare.qixin.sysdb.filter.Filter;
import com.facishare.qixin.sysdb.model.Data;
import com.facishare.qixin.sysdb.serivce.DataSourceService;
import com.facishare.webpage.customer.dao.HomePageLayoutDao;
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity;
import com.facishare.webpage.customer.service.HomePageCommonService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by zhangyu on 2020/2/25
 */
public class HomePageDataServiceImpl implements DataSourceService {

    @Autowired
    private HomePageLayoutDao homePageLayoutDao;
    @Autowired
    private HomePageCommonService homePageCommonService;

    @Override
    public Data getDataByDataId(Integer integer, String layoutId, Filter filter) {
        HomePageLayoutEntity homePageLayoutById = homePageLayoutDao.getHomePageLayoutById(layoutId, filter);
        return homePageLayoutById;
    }

    @Override
    public List<? extends Data> batchGetDataByDataId(Integer integer, List layoutIds, Filter filter) {
        if (CollectionUtils.isEmpty(layoutIds)) {
            return Lists.newArrayList();
        }
        List<HomePageLayoutEntity> homePageLayoutEntityList = homePageLayoutDao.getBatchHomePageLayoutByIds((List<String>) layoutIds, filter);
        return homePageLayoutEntityList;
    }

    @Override
    public Data findAndModify(int tenantId, Data data) {
        if (data == null) {
            return null;
        }
        HomePageLayoutEntity homePageLayoutEntity = homePageLayoutDao.findAndModifyHomePage(tenantId, (HomePageLayoutEntity) data);
        return homePageLayoutEntity;
    }

    @Override
    public List<? extends Data> queryDataList(int tenantId, Filter filter) {
        List<HomePageLayoutEntity> homePageLayoutListByFilter = homePageLayoutDao.getHomePageLayoutListByFilter(tenantId, filter);
        return homePageLayoutListByFilter;
    }

    @Override
    public List<? extends Data> queryUserDataList(int tenantId, int employeeId, Filter filter) {
        //todo:扩展系统库个人级List的入参appId
        List<String> scopeList = homePageCommonService.getScopeList(tenantId, employeeId, null);
        List<HomePageLayoutEntity> homePageLayoutEntityList = homePageLayoutDao.getEmployeeHomePageLayoutListByFilter(tenantId, scopeList, filter);
        return homePageLayoutEntityList;
    }
}
