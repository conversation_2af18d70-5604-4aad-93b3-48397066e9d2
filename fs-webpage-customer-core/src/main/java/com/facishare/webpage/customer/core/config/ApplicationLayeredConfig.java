package com.facishare.webpage.customer.core.config;

import com.facishare.rest.core.util.JacksonUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2024/5/14
 */
@UtilityClass
public class ApplicationLayeredConfig {

    private Map<String, ApplicationLayeredGrayItem> map = Maps.newHashMap();

    static {
        ConfigFactory.getConfig("fs-application-layered-gray", ApplicationLayeredConfig::loadConfig);
    }

    private void loadConfig(IConfig config) {
        List<ApplicationLayeredGrayItem> applicationLayeredGrayList = JacksonUtil.fromJson(config.getString(), new TypeReference<List<ApplicationLayeredGrayItem>>() {
        });

        if (CollectionUtils.isEmpty(applicationLayeredGrayList)) {
            return;
        }
        map = applicationLayeredGrayList.stream()
                .collect(Collectors.toMap(ApplicationLayeredGrayItem::getAppId, Function.identity()));
    }

    public boolean isAllow(String appId, String tenantId) {
        ApplicationLayeredGrayItem applicationLayeredGrayItem = map.get(appId);
        if (Objects.isNull(applicationLayeredGrayItem)) {
            return false;
        }
        return applicationLayeredGrayItem.grayRule.isAllow(tenantId);
    }

    public boolean isDefineAppId(String appId) {
        return map.containsKey(appId);
    }

    @Data
    public static class ApplicationLayeredGrayItem {
        private final String appId;
        private final GrayRule grayRule;

        @JsonCreator
        public ApplicationLayeredGrayItem(@JsonProperty("appId") String appId, @JsonProperty("grayRule") GrayRule grayRule) {
            this.appId = appId;
            this.grayRule = grayRule;
        }
    }
}
