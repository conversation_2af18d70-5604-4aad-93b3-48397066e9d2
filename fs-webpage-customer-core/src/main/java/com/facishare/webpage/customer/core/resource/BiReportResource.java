package com.facishare.webpage.customer.core.resource;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.rest.core.codec.IRestCodeC;
import com.facishare.rest.core.exception.RestProxyRuntimeException;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.webpage.customer.api.model.GetDashBoardList;
import com.facishare.webpage.customer.api.model.GetGlobalFilter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Yu
 * @date 2022/3/8 11:15 AM
 */
@RestResource(
        value = "BiReportResource",
        desc = "fs-bi-crm-report-web",
        contentType = "application/json")
public interface BiReportResource {
    /**
     * 根据ID获取驾驶舱信息  // ignoreI18n
     *
     * @param headers
     * @param arg
     * @return
     */
    @POST(
            value = "/api/v1/dashboard/getDashBoardList",
            desc = "根据ID获取驾驶舱信息" // ignoreI18n
    )
    GetDashBoardList.Result getDashBoardList(@HeaderMap Map<String, String> headers, @Body GetDashBoardList.Arg arg);

    @POST(value = "/api/v1/component/parseGlobalFilters", desc = "根据Filter获取拍平后的人员信息（BI筛选器用）") // ignoreI18n
    GetGlobalFilter.Result getGlobalFilter(@HeaderMap Map<String, String> headers, @Body GetGlobalFilter.Arg arg);

}
