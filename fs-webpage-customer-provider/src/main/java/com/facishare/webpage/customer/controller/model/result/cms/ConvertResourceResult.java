package com.facishare.webpage.customer.controller.model.result.cms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConvertResourceResult {

    private List<ConvertResourceInfo> resourceInfos;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ConvertResourceInfo {
        private String apiName;
        private String url;
        private String name;
        private String workSpaceApiName;

    }
}
