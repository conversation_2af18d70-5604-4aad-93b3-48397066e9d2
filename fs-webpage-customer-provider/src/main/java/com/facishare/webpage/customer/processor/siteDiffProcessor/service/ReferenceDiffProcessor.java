package com.facishare.webpage.customer.processor.siteDiffProcessor.service;

import com.facishare.webpage.customer.api.exception.ValidateException;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.core.util.CollectionUtils;
import com.facishare.webpage.customer.dao.ReferenceEntityDao;
import com.facishare.webpage.customer.dao.entity.ReferenceEntity;
import com.facishare.webpage.customer.dao.entity.SiteEntity;
import com.facishare.webpage.customer.processor.siteDiffProcessor.AbstractEntityDiffProcessor;
import com.facishare.webpage.customer.service.SiteI18nQueryService;
import com.facishare.webpage.customer.util.ReferenceTargetType;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.facishare.webpage.customer.api.constant.ErrorMessageI18NKey.SITE_REFERENCE_INFO_ERROR;
import static com.facishare.webpage.customer.service.impl.SiteServiceImpl.DRAFT_SITE_API_NAME;

@Service
@Slf4j
public class ReferenceDiffProcessor extends AbstractEntityDiffProcessor<ReferenceEntity> {

    @Autowired
    private ReferenceEntityDao referenceEntityDao;

    @Autowired
    private SiteI18nQueryService siteI18nQueryService;


    @Override
    protected List<ReferenceEntity> queryOldEntities(User user, SiteEntity siteEntity, String clientType, String diffType, Boolean isPublish) {
        String siteEntityApiName = siteEntity.getApiName();
        if (Boolean.FALSE.equals(isPublish)) {
            siteEntityApiName = siteEntityApiName + DRAFT_SITE_API_NAME;
        }
        List<ReferenceEntity> referenceEntities = referenceEntityDao.findBySiteApiName(user.getTenantId(), siteEntityApiName);
        if (StringUtils.isNotEmpty(diffType) && (ReferenceTargetType.getCodes().contains(diffType))) {
            referenceEntities.removeIf(x -> !diffType.equals(x.getTargetType()));
        }
        return referenceEntities;
    }

    @Override
    protected String getEntityApiName(ReferenceEntity entity) {
        return entity.getTargetId() + "_" + entity.getSourceId() + "_" + entity.getSourceType();
    }

    @Override
    protected String getEntityId(ReferenceEntity entity) {
        return entity.getId();
    }

    @Override
    protected void setEntityId(ReferenceEntity entity, String id) {
        entity.setId(id);
    }

    @Override
    protected void validateNewEntities(User user, List<ReferenceEntity> newEntities) {
        List<ReferenceEntity> failReference = Lists.newArrayList();

        for (ReferenceEntity newEntity : newEntities) {
            if (StringUtils.isAnyEmpty(newEntity.getSourceId(), newEntity.getTargetId(),
                    newEntity.getSourceType(), newEntity.getTargetType(), newEntity.getIndexName(), newEntity.getIndexValue())) {
                failReference.add(newEntity);
            }
        }

        if (CollectionUtils.notEmpty(failReference)) {
            log.warn("site reference param error! user:{},failReference:{}", user, failReference);
            throw ValidateException.fromI18N(SITE_REFERENCE_INFO_ERROR);
        }
    }

    @Override
    protected String getEntityType() {
        return "Reference";
    }


}
