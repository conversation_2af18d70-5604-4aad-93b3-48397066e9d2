package com.facishare.webpage.customer.api.service;

import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.arg.CheckUserPermissionApiArg;
import com.facishare.webpage.customer.api.model.arg.GetPageTemplateByIdRestArg;
import com.facishare.webpage.customer.api.model.arg.GetPageTemplatesArg;
import com.facishare.webpage.customer.api.model.result.BaseApiResult;
import com.facishare.webpage.customer.api.model.result.CheckUserPermissionResult;
import com.facishare.webpage.customer.api.model.result.GetPageTemplateByIdRestResult;
import com.facishare.webpage.customer.api.model.result.GetPageTemplatesResult;

import java.util.List;

/**
 * Created by z<PERSON>yi on 2019/9/19.
 */
public interface TenantPageTempleService {

    BaseApiResult checkUserPermission(CheckUserPermissionApiArg apiArg);

    GetPageTemplatesResult getPageTemplates(GetPageTemplatesArg arg);

    CheckUserPermissionResult checkUserPermissionV2(CheckUserPermissionApiArg arg);

    GetPageTemplateByIdRestResult getPageTemplateById(GetPageTemplateByIdRestArg arg);

    List<Scope> queryScopeList(int tenantId, Integer employeeId, Long outTenantId, Long outUserId, String appId);



}
