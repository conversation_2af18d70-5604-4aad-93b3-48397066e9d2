package com.facishare.webpage.customer.api.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.webpage.customer.api.model.CommonlyUseMenuItem;
import com.facishare.webpage.customer.api.model.result.BaseResult;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/12/19.
 */
@Data
public class CommonlyUsedMenuResult extends BaseResult {
    @JSONField(name = "M1")
    @SerializedName("menuItems")
    private List<CommonlyUseMenuItem> commonlyUseMenuItems = Lists.newArrayList();
    @JSONField(name = "M2")
    private Boolean result = false;
    @JSONField(name = "M3")
    private Map<String,Object> configInfo;
    @JSONField(name = "M4")
    private Boolean isDefault = false;
}
