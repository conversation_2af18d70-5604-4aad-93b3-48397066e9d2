package com.facishare.webpage.customer.api.model.arg;

import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.MenuTempleRestVO;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.TenantMenuSimpleDataRestVo;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Locale;

/**
 * Created by z<PERSON><PERSON> on 2019/11/24.
 */
@Data
public class CreateMenuRestArg  implements Serializable {
    private MenuTempleRestVO menuTempleVO;
    private List<TenantMenuSimpleDataRestVo> tenantMenuSimpleDataVos;
    private List<String> roleIdList;
    private List<Scope> scopeList;
    private int appType = BizType.CRM.getType();
    private String appId;
    private Integer enterpriseId;
    private String enterpriseAccount;
    private Integer employeeId;
    private ClientTypeEnum type;
    private String version;
    private String deviceId;
    private Locale locale;
    private String osVersion;
    public void valid() throws WebPageException {
        if (BizType.CRM.getType() != appType){
            return;
        }
        if (ObjectUtils.isEmpty(menuTempleVO)
                || CollectionUtils.isEmpty(tenantMenuSimpleDataVos)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
