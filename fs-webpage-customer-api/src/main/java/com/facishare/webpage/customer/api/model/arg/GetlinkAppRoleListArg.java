package com.facishare.webpage.customer.api.model.arg;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class GetlinkAppRoleListArg implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer tenantId;

    private List<String> linkAppIds =new ArrayList<>();
    private String linkAppId;
    private String roleId;
    private List<String> roleIds = new ArrayList<>();

    /**
     * 1：预置应用开通时绑定的角色  2：企业自己分配的角色
     */
    private Integer type;
    /**
     * 角色类型，1：基础角色，2：预置角色，3：自定义角色
     */
    private Integer roleType;

    private List<Integer> roleTypes = new ArrayList<>();


    private Boolean orderByUpdateTimeDesc = Boolean.FALSE;


}
