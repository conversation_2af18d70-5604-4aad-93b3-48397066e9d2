package com.facishare.webpage.customer.api.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/11/24.
 */
@Data
public class MenuTempleRestVO implements Serializable {

    @SerializedName("_id")
    private String menuId;

    @SerializedName("api_name")
    private String apiName;

    @SerializedName("tenant_id")
    private String tenantId;

    @SerializedName("name")
    private String name;

    @SerializedName("description")
    private String description;

    @SerializedName("role_name_list")
    @Deprecated
    private List<String> roleNameList;

    @SerializedName("scopeList")
    private List<Scope> scopeList;

    @SerializedName("scopeNameList")
    private List<String> scopeNameList;

    @SerializedName("created_by")
    private List<String> createdBy;

    @SerializedName("create_time")
    private Long createTime;

    @SerializedName("last_modified_by")
    private List<String> lastModifiedBy;

    @SerializedName("last_modified_time")
    private Long lastModifiedTime;

    @SerializedName("total_num")
    private String totalNum;

    @SerializedName("is_deleted")
    private Boolean isDeleted;

    @SerializedName("is_system")
    private Boolean isSystem;

    @SerializedName("active_status")
    private String activeStatus;

    @SerializedName("is_show_menu_icon")
    private Boolean isShowMenuIcon = true;

    @SerializedName("hiddenQuickCreate")
    private Boolean hiddenQuickCreate= false;

    private String translateKey;
}
