package com.facishare.webpage.customer.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 站点语言配置DTO
 * 表示站点的语言设置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SiteLangDTO {
    
    /**
     * 语言代码，例如："zh_CN", "en", "ja_JP"
     */
    private String lang;
    
    /**
     * 是否为站点的默认语言
     */
    private Boolean defaultLang;
    
    /**
     * 验证站点语言配置
     * 
     * @throws IllegalArgumentException 如果验证失败
     */
    public void validate() {
        if (StringUtils.isBlank(lang)) {
            throw new IllegalArgumentException("Language code cannot be empty");
        }
        
        // 基本语言代码格式验证
        if (!isValidLanguageCode(lang)) {
            throw new IllegalArgumentException("Invalid language code format: " + lang);
        }
    }
    
    /**
     * 检查语言代码格式是否有效
     * 
     * @param langCode 要验证的语言代码
     * @return 如果有效返回true，否则返回false
     */
    private boolean isValidLanguageCode(String langCode) {
        if (StringUtils.isBlank(langCode)) {
            return false;
        }
        
        // 支持格式如：en, zh_CN, ja_JP, en_US
        return langCode.matches("^[a-z]{2}(_[A-Z]{2})?$");
    }
    
    /**
     * 检查这是否是默认语言
     * 
     * @return 如果是默认语言返回true
     */
    public boolean isDefaultLanguage() {
        return Boolean.TRUE.equals(defaultLang);
    }
}
