package com.facishare.webpage.customer.api.model.result;

import com.facishare.webpage.customer.api.model.AppPageTemplateVO;
import com.facishare.webpage.customer.api.model.PageTemplate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by zhangyu on 2019/10/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetAppPageInfoByUserHighestPageTempleResult implements Serializable {
    private AppPageTemplateVO appPageTemplate;


}
