package com.facishare.webpage.customer.api.service;

import com.facishare.rest.core.annotation.DELETE;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.PathParam;
import com.facishare.rest.core.annotation.RestResource;

@RestResource(
        value = "AppCenterManResource",
        desc = "应用中心服务",  // ignoreI18n
        contentType = "application/json"
)
public interface AppCenterService {

    @DELETE(value = "/open/manage/test/removeEnterpriseViewCache/{fsEa}/{appId}", desc = "清空缓存")
    void removeEnterpriseViewCache(@HeaderParam("x-fs-ei") String tenantId, @HeaderParam("x-fs-userInfo") String userId,
                                                          @PathParam("fsEa") String fsEa, @PathParam("appId") String appId);

}
