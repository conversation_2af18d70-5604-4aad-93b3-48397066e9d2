package com.facishare.webpage.customer.util;

import com.facishare.webpage.customer.api.constant.ErrorMessageI18NKey;
import com.facishare.webpage.customer.api.exception.ValidateException;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 搜索参数验证工具类 用于验证搜索输入，防止正则表达式特殊字符导致的异常和长度超限问题
 */
public class SearchValidationUtil {

    /**
     * 正则表达式特殊字符列表 这些字符在正则表达式中有特殊含义，如果直接用于搜索可能导致PatternSyntaxException
     */
    private static final String REGEX_SPECIAL_CHARS = ".*+?^${}()|[]\\";

    /**
     * 搜索关键字最大长度限制 防止MongoDB正则表达式过长错误
     */
    private static final int MAX_SEARCH_KEYWORD_LENGTH = 1000;

    /**
     * 验证搜索关键字是否包含正则表达式特殊字符和长度是否超限
     *
     * @param searchKeyword 搜索关键字
     * @throws ValidateException 如果包含非法字符或长度超限则抛出异常
     */
    public static void validateSearchKeyword(String searchKeyword) {
        if (StringUtils.isBlank(searchKeyword)) {
            return;
        }

        // 检查长度是否超限
        if (searchKeyword.length() > MAX_SEARCH_KEYWORD_LENGTH) {
            throw ValidateException.fromI18N(ErrorMessageI18NKey.SEARCH_KEYWORD_TOO_LONG);
        }

        // 检查是否包含正则表达式特殊字符
        for (char c : searchKeyword.toCharArray()) {
            if (REGEX_SPECIAL_CHARS.indexOf(c) != -1) {
                throw ValidateException.fromI18N(ErrorMessageI18NKey.SEARCH_INVALID_CHARACTERS);
            }
        }
    }

    /**
     * 清理搜索关键字，移除或转义正则表达式特殊字符 这是一个备选方案，如果不想抛出异常，可以使用此方法清理输入
     *
     * @param searchKeyword 原始搜索关键字
     * @return 清理后的搜索关键字
     */
    public static String sanitizeSearchKeyword(String searchKeyword) {
        if (StringUtils.isBlank(searchKeyword)) {
            return searchKeyword;
        }

        // 使用Pattern.quote来转义所有正则表达式特殊字符
        return Pattern.quote(searchKeyword);
    }

    /**
     * 检查字符串是否包含正则表达式特殊字符
     *
     * @param input 输入字符串
     * @return 如果包含特殊字符返回true，否则返回false
     */
    public static boolean containsRegexSpecialChars(String input) {
        if (StringUtils.isBlank(input)) {
            return false;
        }

        for (char c : input.toCharArray()) {
            if (REGEX_SPECIAL_CHARS.indexOf(c) != -1) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取输入字符串中的第一个正则表达式特殊字符
     *
     * @param input 输入字符串
     * @return 第一个特殊字符，如果没有则返回null
     */
    public static Character getFirstRegexSpecialChar(String input) {
        if (StringUtils.isBlank(input)) {
            return null;
        }

        for (char c : input.toCharArray()) {
            if (REGEX_SPECIAL_CHARS.indexOf(c) != -1) {
                return c;
            }
        }
        return null;
    }
}
