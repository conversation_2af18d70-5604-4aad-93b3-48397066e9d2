package com.facishare.webpage.customer.core.business;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.core.config.CustomerCoreConfig;
import com.facishare.webpage.customer.core.model.Component;
import com.facishare.webpage.customer.core.model.ComponentDto;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by shecheng on 19/12/27.
 */
public class ComponentListManager {

    @Resource
    private CustomerCoreConfig customerCoreConfig;

    private Map<String, List<Component>> allComponentList;

    private String configName;

    public ComponentListManager() {
    }

    public ComponentListManager(String configName) {
        this.configName = configName;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public void init() {
        ConfigFactory.getInstance().getConfig(configName, iConfig -> loadComponentConfig(iConfig));
    }

    public void loadComponentConfig(IConfig iConfig) {
        if (Strings.isNullOrEmpty(iConfig.getString())) {
            return;
        }
        Map<String, String> configMap = JSONObject.parseObject(iConfig.getString(), Map.class);
        Map<String, List<Component>> tmp = new HashMap<>();
        configMap.entrySet().forEach(entry -> {
            ConfigFactory.getInstance().getConfig(entry.getValue(), iConfig2 -> {
                if (!Strings.isNullOrEmpty(iConfig2.getString())) {
                    List<Component> componentList = JSONObject.parseArray(iConfig2.getString(), Component.class);
                    tmp.put(entry.getKey(), componentList);
                }
            });
        });
        allComponentList = tmp;
    }

    public List<ComponentDto> getComponentDtoListByAppId(String appId) {
        List<Component> componentList = allComponentList.getOrDefault(appId, Lists.newArrayList()); // collection和一级widget
        return customerCoreConfig.convert2Dto(componentList);   // 补充collection下的二级widget
    }
    /*
    * @description: 检测某个配置下是否存在数据, 部分应用需要降级(eg: c端门户)
     */
    public boolean isComponentEmpty(String appId){
        return CollectionUtils.isEmpty(allComponentList.getOrDefault(appId, null));
    }

    public List<Component> getComponentListByAppId(String appId) {
         return allComponentList.getOrDefault(appId, Lists.newArrayList());
    }

    public List<ComponentDto> getComponentDtoListByAppId(String appId, UserInfo userInfo) {
        List<Component> componentList = allComponentList.getOrDefault(appId, Lists.newArrayList());
        //中间添加了灰度处理
        return customerCoreConfig.convert2Dto(componentList, userInfo);
    }

    public List<ComponentDto> mergeRegisterComponentDtoListByAppId(String appId, Map<String, List<Menu>> menus) {
        List<Component> componentList = allComponentList.getOrDefault(appId, Lists.newArrayList());
        return customerCoreConfig.convert2Dto(componentList, menus);
    }

    public Optional<Component> getComponentById(String id) {
        return allComponentList.values().stream()
                .flatMap(Collection::stream)
                .filter(it -> Objects.equals(id, it.getId()))
                .findFirst();
    }

    public Optional<Component> getComponentByCollectionId(String collectionId) {
        return allComponentList.values().stream()
                .flatMap(Collection::stream)
                .filter(it -> Objects.equals(collectionId, it.getCollectionId()))
                .findFirst();
    }
}
