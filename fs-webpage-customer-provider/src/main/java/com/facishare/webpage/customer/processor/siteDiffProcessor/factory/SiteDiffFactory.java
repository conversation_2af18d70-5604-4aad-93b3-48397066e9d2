package com.facishare.webpage.customer.processor.siteDiffProcessor.factory;

import com.facishare.webpage.customer.processor.siteDiffProcessor.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SiteDiffFactory {

    @Autowired
    private ThemeStyleDiffProcessor themeStyleDiffProcessor;

    @Autowired
    private ThemeLayoutDiffProcessor themeLayoutDiffProcessor;

    @Autowired
    private TenantMenuDiffProcessor menuDiffProcessor;

    @Autowired
    private HomePageLayoutDiffProcessor pageDiffProcessor;

    @Autowired
    private ReferenceDiffProcessor referenceDiffProcessor;

    public ThemeStyleDiffProcessor getThemeStyleProcessor() {
        return themeStyleDiffProcessor;
    }

    public ThemeLayoutDiffProcessor getThemeLayoutProcessor() {
        return themeLayoutDiffProcessor;
    }

    public TenantMenuDiffProcessor getMenuProcessor() {
        return menuDiffProcessor;
    }

    public HomePageLayoutDiffProcessor getPageProcessor() {
        return pageDiffProcessor;
    }

    public ReferenceDiffProcessor getReferenceProcessor() {
        return referenceDiffProcessor;
    }

}
