package com.facishare.webpage.customer.api.model.result;

import com.facishare.webpage.customer.api.model.MenuTempleRestVO;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.TenantMenuItemRestVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2020/11/26
 */
@Data
public class GetMenuByIdRestResult implements Serializable {

    private MenuTempleRestVO objectData;

    private List<TenantMenuItemRestVO> detailMenuItems;

    private List<String> roleIdList;

    private List<Scope> scopeList;

}
