package com.facishare.webpage.customer.api.model.core;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * Created by she<PERSON> on 19/12/10.
 */
@Data
public class Menu implements Serializable {

    @JSONField(ordinal = 1)
    private String id;

    @JSONField(ordinal = 2)
    private String name;

    @JSONField(ordinal = 3)
    private String nameI18nKey;

    @JSONField(ordinal = 4)
    private Icon icon;

    @JSONField(ordinal = 5)
    private Url url;

    @JSONField(ordinal = 6)
    private List<String> deviceTypes = new ArrayList<>();

    @JSONField(ordinal = 7)
    private PersonPrivilege personPrivilege;

    @JSONField(ordinal = 8)
    private TenantPrivilege tenantPrivilege;

    @JSONField(ordinal = 9)
    private DataPrivilege dataPrivilege;

    @JSONField(ordinal = 10)
    private int groupIndex;

    @JSONField(ordinal = 11)
    private boolean hidden = false;    //默认菜单是否显示

    @JSONField(ordinal = 12)
    private String componentAppId;

    @JSONField(ordinal = 13)
    private String description;

    @JSONField(ordinal = 14)
    private boolean supportTabs;

    /**
     * 菜单不可以删除
     */
    @JSONField(ordinal = 14)
    private Boolean unDeletable;

    @JSONField(ordinal = 15)
    private Integer configAppType;

    /**
     * 菜单不展示的 客户端黑名单
     */
    @JSONField(ordinal = 16)
    private List<String> clientTypeBlackList;

    /**
     * web菜单 点击是否新开窗口  不为空即为true
     */
    @JSONField(ordinal = 17)
    private String target;

    /**
     * 菜单的fxui的名称
     */
    @JSONField(ordinal = 18)
    private int iconIndex;

    /**
     * 菜单是否可以多配置
     * 0:可以多配置
     */
    @JSONField(ordinal = 19)
    private Integer limit;

    @JSONField(ordinal = 20)
    private String appId;

    /**
     * 0:inner 1:cross
     */
    @JSONField(ordinal = 21)
    private Integer applyType;

    @JSONField(ordinal = 22)
    private String appTypeApiName;

}
