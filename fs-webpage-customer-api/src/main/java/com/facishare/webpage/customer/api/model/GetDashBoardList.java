package com.facishare.webpage.customer.api.model;

import com.facishare.webpage.customer.api.utils.BaseRestResult;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2022/3/8 11:16 AM
 */
public interface GetDashBoardList {

    @Data
    @Builder
    class Arg implements Serializable {
        private List<String> dashboardIds;
    }

    @Data
    @Builder
    class Result extends BaseRestResult<GetDashBoardListResult> {

    }

    @Data
    @Builder
    class GetDashBoardListResult{
        private List<DashBoardData> dashboardList;
    }

    @Data
    @Builder
    class DashBoardData implements Serializable {
        private String id;
        private String name;
    }
}
