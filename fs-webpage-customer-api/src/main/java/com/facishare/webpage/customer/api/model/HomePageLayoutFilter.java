package com.facishare.webpage.customer.api.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2019/9/10
 */
@Data
public class HomePageLayoutFilter implements Serializable {
    @JSONField(name = "M1")
    @SerializedName("ObjectApiName")
    private String objectApiName;
    @JSONField(name = "M2")
    @SerializedName("FilterKey")
    private String filterKey;
    @JSONField(name = "M3")
    @SerializedName("FilterName")
    private String filterName;
    @JSONField(name = "M4")
    @SerializedName("FilterMainID")
    private String filterMainID;
    @JSONField(name = "M5")
    @SerializedName("SearchApiName")
    private String searchApiName;


}
