package com.facishare.webpage.customer.api.model;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface GetGlobalFilter {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Arg {
        private JSONObject globalFilter;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result {
        Integer code;
        String message;
        DataMap data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class DataMap {
        @Builder.Default
        List<EmployeeAndCircles> employeeAndCircles = Lists.newArrayList();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class EmployeeAndCircles {
        String id;
        String type;
    }
}
