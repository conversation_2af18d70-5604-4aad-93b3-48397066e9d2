package com.facishare.webpage.customer.controller.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.annotation.FSClientInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.organization.adapter.api.permission.enums.functioncode.SystemFunctionCodeEnum;
import com.facishare.qixin.objgroup.common.service.PaasOrgGroupService;
import com.facishare.qixin.permission.ValidateFunctionPermission;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.*;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.*;
import com.facishare.webpage.customer.api.model.arg.FindCustomerWidgetArg;
import com.facishare.webpage.customer.api.model.arg.GetHomePageLayoutByIdArg;
import com.facishare.webpage.customer.api.model.arg.ModifyHomePageLayoutArg;
import com.facishare.webpage.customer.api.model.arg.SetHomePageLayoutStatusArg;
import com.facishare.webpage.customer.api.model.core.TenantPrivilege;
import com.facishare.webpage.customer.api.model.result.FindObjectPageComponentList;
import com.facishare.webpage.customer.api.model.result.GetHomePageLayoutByIdResult;
import com.facishare.webpage.customer.api.model.result.ModifyHomePageLayoutResult;
import com.facishare.webpage.customer.api.model.result.SetHomePageLayoutStatusResult;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.api.utils.WebPageUtils;
import com.facishare.webpage.customer.common.CheckService;
import com.facishare.webpage.customer.common.LanguageService;
import com.facishare.webpage.customer.common.OrganizationCommonService;
import com.facishare.webpage.customer.component.ComponentService;
import com.facishare.webpage.customer.config.ComponentConfig;
import com.facishare.webpage.customer.config.DefaultTenantConfig;
import com.facishare.webpage.customer.config.HomePageMaxConfig;
import com.facishare.webpage.customer.constant.*;
import com.facishare.webpage.customer.controller.TenantHomePageAction;
import com.facishare.webpage.customer.controller.model.arg.homepage.*;
import com.facishare.webpage.customer.controller.model.result.homepage.*;
import com.facishare.webpage.customer.core.business.ComponentListManager;
import com.facishare.webpage.customer.core.config.ComponentNameConfig;
import com.facishare.webpage.customer.core.config.WidgetCollectionConfig;
import com.facishare.webpage.customer.core.config.WidgetsConfig;
import com.facishare.webpage.customer.core.model.*;
import com.facishare.webpage.customer.core.service.I18nService;
import com.facishare.webpage.customer.core.service.impl.UIPaasLicenseServiceImpl;
import com.facishare.webpage.customer.core.util.BIUrlUtil;
import com.facishare.webpage.customer.core.util.ComponentExt;
import com.facishare.webpage.customer.core.util.ScopesUtil;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.facishare.webpage.customer.dao.HomePageLayoutDao;
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity;
import com.facishare.webpage.customer.event.WebPageEventService;
import com.facishare.webpage.customer.helper.HomePageHelper;
import com.facishare.webpage.customer.model.ObjectVO;
import com.facishare.webpage.customer.model.component.CustomerLayoutHelper;
import com.facishare.webpage.customer.remote.ObjectService;
import com.facishare.webpage.customer.remote.SceneService;
import com.facishare.webpage.customer.remote.TempFileToFormalFile;
import com.facishare.webpage.customer.service.*;
import com.facishare.webpage.customer.util.BIHomePageGraySwitch;
import com.facishare.webpage.customer.util.PageTemplateUtils;
import com.facishare.webpage.customer.util.RedisUtil;
import com.facishare.webpage.customer.util.TempleIdUtil;
import com.fxiaoke.enterpriserelation2.result.SimpleLinkAppResult;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.Comparator;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.webpage.customer.api.constant.CustomerLayoutField.functionEnter;
import static com.facishare.webpage.customer.util.PageTemplateUtils.isBICard;
import static com.facishare.webpage.customer.util.PageTemplateUtils.isScene;

/**
 * Created by zhangyu on 2019/9/10
 */
@Controller
@Slf4j
@RequestMapping({"/Homepage", "/HomePage"})
public class TenantHomePageActionImpl implements TenantHomePageAction {
    private static final Logger logger = LoggerFactory.getLogger(TenantHomePageActionImpl.class);

    private static int VENDORMAX = 20;

    @Autowired
    private HomePageBaseService homePageBaseService;
    @Autowired
    private UserHomePageBaseService userHomePageBaseService;
    @Autowired
    private RemoteService remoteService;
    @Autowired
    private HomePageMaxConfig homePageMaxConfig;
    @Autowired
    private DefaultTenantConfig defaultTenantConfig;
    @Autowired
    private BIUrlUtil biUrlUtil;
    @Autowired
    private PaasOrgGroupService paasOrgGroupService;
    @Autowired
    private ComponentListManager componentListManager;
    @Autowired
    private ObjectService objectService;
    @Autowired
    private LanguageService languageService;
    @Resource
    private WebPageEventService webPageEventService;
    @Resource
    private ComponentService componentService;
    @Resource
    private ComponentConfig componentConfig;
    @Resource
    private OrganizationCommonService organizationCommonService;
    @Resource
    private TempFileToFormalFile tempFileToFormalFile;
    @Resource
    private CustomerDropListService customerDropListService;
    @Resource
    private SceneService sceneService;
    @Autowired
    private CheckService checkService;
    @Autowired
    private I18nService i18nService;
    @Autowired
    private UIPaasLicenseServiceImpl uiPaasLicenseService;

    private Map<Integer, HomePageHelper> homePageHelperMap = Maps.newHashMap();

    @Autowired
    private RedisUtil redisUtil;

    @Resource
    private RemoteCrossService remoteCrossService;

    @Autowired
    private CrmPassCacheManager crmPassCacheManager;

    @Autowired
    private ComponentNameConfig componentNameConfig;

    @Autowired
    private UIPaasLicenseServiceImpl uIPaasLicenseService;

    @Autowired
    private WidgetsConfig widgetsConfig;

    @Autowired
    private WidgetCollectionConfig widgetCollectionConfig;
    @Resource
    private HomePageLayoutDao homePageLayoutDao;

    @Autowired
    private void setComponentHelperMap(List<HomePageHelper> homePageHelperList) {
        homePageHelperMap = homePageHelperList.stream().collect(Collectors.toMap(HomePageHelper::getAppType, Function.identity()));
    }

    public void setHomePageBaseService(HomePageBaseService homePageBaseService) {
        this.homePageBaseService = homePageBaseService;
    }

    @Override
    @RequestMapping(value = {"/GetHomePageLayoutById", "/GetHomePageLayoutByID"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetHomePageLayoutByIdResult getHomePageLayoutById(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody GetHomePageLayoutByIdArg arg) {
        try {
            arg.valid();
            RequestContextManager.initContextForIsFromManage(arg.isManage());
            int appType = getAppType(arg.getAppType(), null);
            HomePageLayoutTO homePageLayoutTO = homePageBaseService.getHomePageLayoutByApiName(userInfo, arg.getLayoutId(), arg.getLayoutApiName(), appType, clientInfo, arg.isTranslateFlag());
            if (homePageLayoutTO == null) {
                throw new WebPageException(InterErrorCode.HOME_DOES_NOT_EXIST);
            }
            GetHomePageLayoutByIdResult result = new GetHomePageLayoutByIdResult();
            if (StringUtils.isNotBlank(homePageLayoutTO.getAppId()) && BizType.CROSSCUSTOMER.getDefaultAppId().equals(homePageLayoutTO.getAppId())) {
                homePageLayoutTO.setAppId(defaultTenantConfig.getQudaomenhuAppId());
            }
            result.setHomePageLayout(homePageLayoutTO);
            logger.debug("getHomePageLayoutById result {}", result);
            //增加埋点
            webPageEventService.sendGetWebPageEvent(userInfo, clientInfo, appType, homePageLayoutTO.getAppId());
            return result;
        } finally {
            RequestContextManager.removeContext();
        }

    }

    @Override
    @RequestMapping(value = "/GetHomePageComponents", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetHomePageComponentsResult getHomePageComponents(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody(required = false) GetHomePageComponentsArg arg) {
        String appId = null;
        if (arg == null || Strings.isNullOrEmpty(arg.getAppId())) {
            appId = Constant.APP_CRM;
        } else {
            appId = arg.getAppId();
        }
        List<HomePageComponent> homePageComponentList = getHomePageComponent(BizType.CRM.getType(), userInfo.getEnterpriseId(), appId, clientInfo.getLocale());
        GetHomePageComponentsResult result = new GetHomePageComponentsResult();
        result.setComponents(homePageComponentList);
        result.setChartMaxCount(homePageMaxConfig.getChartMaxCount(userInfo.getEnterpriseAccount()));
        result.setOtherMaxCount(homePageMaxConfig.getOtherMaxCount(userInfo.getEnterpriseAccount()));
        logger.debug("getCRMHomePageComponents result {}", result);
        return result;
    }

    @Override
    @RequestMapping(value = "/ModifyHomePageLayout", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public ModifyHomePageLayoutResult modifyHomePageLayout(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody ModifyHomePageLayoutArg arg) {
        // web视图修改保存都是这个接口, 通过appId和appType（com.facishare.webpage.customer.api.constant.BizType）来判断是什么类型
        // appId就是应用id，对于CRM它的ID就是“CRM”，
        // 老版和新版区别在于appType，老版为1，新版是5（被视为PAAS应用的一种）
        try {
            // 打印日志记录参数 userInfo clientInfo arg
            logger.info("url: /Homepage/ModifyHomePageLayout \n{} \n {} \n {}", userInfo, clientInfo, arg);
            if (Objects.isNull(arg)) {
                throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
            }
            arg.valid();
            RequestContextManager.initContextForIsFromManage(arg.isManage());
            String appId = arg.getAppId();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(appId) && defaultTenantConfig.getQudaomenhuAppId().equals(appId)) {
                appId = BizType.CROSSCUSTOMER.getDefaultAppId();
            }
            int appType = arg.getAppType();

            appType = getAppType(appType, appId);
            appId = getAppId(appType, appId);

            HomePageLayoutTO homePageLayout = arg.getHomePageLayout();
            int enterpriseId = userInfo.getEnterpriseId();
            int employeeId = userInfo.getEmployeeId();
            List<HomePageLayoutCard> homePageCards = buildHomePageCardOrder(homePageLayout.getHomePageLayouts());
            homePageLayout.setHomePageLayouts(homePageCards);
            // 对入参的信息进行校验
            doCheckHomePage(userInfo, appType, appId, homePageLayout);

            // icon临时文件转正式文件
            tempFileToFormalFile(userInfo.getEnterpriseAccount(), employeeId, homePageLayout);

            HomePageLayoutTO homePageLayoutTO;

            String layoutId = homePageLayout.getLayoutId();
            String layoutApiName = homePageLayout.getLayoutApiName();

            if (!Strings.isNullOrEmpty(layoutId)) {
                int creatorId = homePageBaseService.getCreatorId(layoutId, layoutApiName, enterpriseId, employeeId);
                if (appType != 4 && homePageLayout.getLayoutType() == LayoutType.PERSONAL && creatorId != userInfo.getEmployeeId()) {
                    throw new WebPageException(InterErrorCode.ILLEGAL_OPERATION);
                }
                homePageLayoutTO = homePageBaseService.updateHomePageLayout(appType, appId, enterpriseId, employeeId, layoutApiName, arg.getApplyType(), homePageLayout);
            } else {
                homePageLayoutTO = homePageBaseService.insertHomePageLayout(appId, appType, enterpriseId, employeeId, layoutApiName, arg.getApplyType(), homePageLayout);
            }
            if (homePageLayoutTO == null) {
                throw new WebPageException(InterErrorCode.SAVE_FAILED);
            }

            if (arg.isManage()) {   // 翻译
                String i18Key = TranslateI18nUtils.getWebPageNameKey(layoutApiName);
                Map<String, String> keyValue = new HashMap<>();
                keyValue.put(i18Key, homePageLayoutTO.getName());
                keyValue.put(TranslateI18nUtils.getWebPageDescriptionKey(layoutApiName), homePageLayoutTO.getDescription());
                i18nService.syncTransValue(enterpriseId, keyValue, clientInfo.getLocale().toLanguageTag());
                List<I18nTrans.TransArg> transArg = Lists.newArrayList();
                transArg.add(I18nTrans.TransArg.builder()
                        .name(homePageLayoutTO.getDescription())
                        .customKey(TranslateI18nUtils.getWebPageDescriptionKey(layoutApiName))
                        .preKeyList(Lists.newArrayList(WebPageConstants.DESCRIBE_PREFIX + homePageLayoutTO.getDescription()))
                        .build());
                i18nService.syncTransValueIncludePreKeyV2(enterpriseId, transArg, clientInfo.getLocale().toLanguageTag());
                // 筛选出所有的bi图表组件，如果修改过名称，则需要更新bi图表组件在翻译工作台的翻译
                updateBICardTranslate(homePageLayoutTO, enterpriseId, clientInfo.getLocale());
                // web顶导航
                updateTransform(enterpriseId, clientInfo, homePageLayout);
                // 页面组件
                updateWidgetsTranslate(homePageLayoutTO, enterpriseId, clientInfo.getLocale());
                // 同步老版CRM首页名称多语, 对于用户的修改, 不需要同步
                if (BizType.CRM.getType() == appType && !checkService.checkGoNewCRM(userInfo.getEnterpriseId())) {
                    String sourceId = "";
                    // 查出老布局, 用于组装预置翻译key
                    HomePageLayoutEntity homePageLayoutEntity = homePageLayoutDao.getHomePageLayoutById(layoutId, null);
                    if (homePageLayoutEntity != null) {
                        sourceId = homePageLayoutEntity.getSourceId();
                    }
                    List<I18nTrans.TransArg> keyList = new ArrayList<>();
                    List<String> preKeyList = Lists.newArrayList(TranslateI18nUtils.getCrmFrontPageKey(layoutId));
                    if (StringUtils.isNotBlank(sourceId)) {
                        preKeyList.add(WebPageConstants.CRM_HOMEPAGE_PREFIX + TranslateI18nUtils.delAllEi(sourceId));
                    }
                    keyList.add(I18nTrans.TransArg.builder()
                            .name(homePageLayoutTO.getName())
                            .customKey(TranslateI18nUtils.getWebPageNameKey(homePageLayoutTO.getApiName()))
                            .preKeyList(preKeyList)
                            .build());
                    List<Localization> localizations = Lists.newArrayList();
                    localizations.addAll(i18nService.buildSyncParam(enterpriseId, keyList, clientInfo.getLocale().toLanguageTag(), false));
                    i18nService.syncTransValue(enterpriseId, localizations);
                }
            }

            ModifyHomePageLayoutResult result = new ModifyHomePageLayoutResult();
            result.setWebPageId(homePageLayoutTO.getLayoutId());
            result.setLayoutApiName(homePageLayoutTO.getLayoutApiName());
            logger.debug("modifyHomePageLayout result {}", result);
            // 增加埋点
            if (WebPageConstants.APP_CRM.equals(appId)) {
                webPageEventService.sendCreateCRMPageEvent(
                        userInfo,
                        clientInfo,
                        homePageLayout.getScopes(),
                        homePageLayout.getPageLayoutType(),
                        homePageLayout.getCustomerLayout(),
                        Strings.isNullOrEmpty(layoutId));
            } else {
                webPageEventService.sendCreateWebPageEvent(userInfo, clientInfo, appType, appId, homePageLayout.getPageLayoutType(), homePageLayout.getCustomerLayout());
            }
            return result;
        } finally {
            RequestContextManager.removeContext();
        }
    }

    // web顶导航
    private void updateTransform(int enterpriseId, ClientInfo clientInfo, HomePageLayoutTO homePageLayout) {
        List<I18nTrans.TransArg> transArgList = Lists.newArrayList();
        homePageLayout.getCustomerLayoutList().forEach(x -> {
            if (x.containsKey("labelPageName")) {
                String translateKey = TranslateI18nUtils.getLabelPageNameKey(homePageLayout.getLayoutId(), x.getString("labelIndex"));
                String value = x.getString("labelPageName");
                transArgList.add(TranslateI18nUtils.buildArgKeyWithoutEi(String.valueOf(enterpriseId), translateKey, value));
            }
        });
        i18nService.syncTransValueIncludePreKey(enterpriseId, transArgList, clientInfo.getLocale().toLanguageTag());
    }

    private void updateBICardTranslate(HomePageLayoutTO homePageLayoutTO, int tenantId, Locale locale) {
        // 理论上这些代码不需要了, 老key被新key兼容
        String model;
        if (Constant.APP_CRM.equals(homePageLayoutTO.getAppId())) {
            //appId==crm  则说明是crm首页
            model = WebPageConstants.HOME_PAGE_WIDGETS_PRE;
        } else if (homePageLayoutTO.getAppType() == BizType.CUSTOMER.getType()) {
            //appType==4 则说明是自定义页面
            model = WebPageConstants.CUSTOMER_PAGE_WIDGETS_PRE;
        } else {
            //其他情况暂不处理
            model = "";
            return;
        }
        List<JSONObject> list = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(homePageLayoutTO.getCustomerLayoutList()) && homePageLayoutTO.getPageMultiType() == 1) {
            list = homePageLayoutTO.getCustomerLayoutList();
        } else {
            list.add(homePageLayoutTO.getCustomerLayout());
        }
        List<Localization> keyList = new ArrayList<>();
        list.forEach(layout -> {
            if (null == layout) {
                return;
            }
            JSONArray components = new JSONArray();
            try {
                components = layout.getJSONArray(CustomerLayoutField.components);
            } catch (Exception e) {
                return;
            }
            if (CollectionUtils.isNotEmpty(components)) {
                components.forEach(component -> {
                    JSONObject jsonObject = (JSONObject) component;
                    if (
                            (
                                    (org.apache.commons.lang3.StringUtils.isNotBlank(jsonObject.getString(CustomerLayoutField.dataId)) && jsonObject.getString(CustomerLayoutField.dataId).startsWith(CustomerLayoutField.startWithBI))
                                            || (org.apache.commons.lang3.StringUtils.isNotBlank(jsonObject.getString(CustomerLayoutField.apiName)) && jsonObject.getString(CustomerLayoutField.apiName).contains(CustomerLayoutField.typeBiCard))
                            )

                                    && (org.apache.commons.lang3.StringUtils.isNotBlank(jsonObject.getString(CustomerLayoutField.newHeader)) && !jsonObject.getString(CustomerLayoutField.newHeader).equals(jsonObject.getString(CustomerLayoutField.title)))
                    ) {
                        String i18Key = WebPageUtils.buildWidgetI18Key(TempleType.WEB, model, homePageLayoutTO.getLayoutId(), jsonObject.getString(CustomerLayoutField.apiName));
                        String newHeader = jsonObject.getString(CustomerLayoutField.newHeader);
                        Localization localization = new Localization();
                        localization.set(locale.toLanguageTag(), newHeader);
                        localization.setKey(i18Key);
                        localization.setTags(Lists.newArrayList("server", "bi_view", "bi_custom"));
                        localization.setTenantId(tenantId);
                        keyList.add(localization);
                        redisUtil.addCache(i18Key, newHeader, locale.toLanguageTag());
                    }
                });
            }
        });
        languageService.save4Translate(keyList, tenantId, false, locale.toLanguageTag());
    }

    private void tempFileToFormalFile(String enterpriseAccount, int employeeId, HomePageLayoutTO homePageLayout) {
        if (homePageLayout == null) {
            return;
        }

        if (homePageLayout.getDataVersion() == DataVersion.dataVersion_100) {
            return;
        }

        List<JSONObject> customerLayoutList = new ArrayList<>();
        List<JSONObject> newCustomerLayoutList = new ArrayList<>();
        customerLayoutList.add(homePageLayout.getCustomerLayout());
        if (homePageLayout.getPageMultiType() == 1 && CollectionUtils.isNotEmpty(homePageLayout.getCustomerLayoutList())) {
            customerLayoutList.addAll(homePageLayout.getCustomerLayoutList());
        }
        for (int i = 0; i < customerLayoutList.size(); i++) {
            JSONObject customerLayout = customerLayoutList.get(i);

            JSONObject newCustomerLayout = JSONObject.parseObject(JSONObject.toJSONString(customerLayout));
            if (Objects.isNull(newCustomerLayout)) {
                continue;
            }

            CustomerLayoutHelper customerLayoutHelper = new CustomerLayoutHelper(newCustomerLayout);
            List<JSONObject> components = customerLayoutHelper.getComponents();

            List<JSONObject> newComponents = components.stream().map(x -> {
                if (x.getString(CustomerLayoutField.type).equals(ComponentConstant.NAVIGATE)) {
                    return navigateMenuIconToFormat(enterpriseAccount, employeeId, x);
                } else if (x.getString(CustomerLayoutField.type).equals(ComponentConstant.slideImage)) {
                    return slideImageToFormat(enterpriseAccount, employeeId, x);
                } else {
                    return x;
                }
            }).collect(Collectors.toList());
            customerLayoutHelper.setComponent(newComponents);
            if (i == 0) {
                homePageLayout.setCustomerLayout(customerLayoutHelper.getCustomerLayout());
            } else {
                newCustomerLayoutList.add(customerLayoutHelper.getCustomerLayout());
            }
        }
        homePageLayout.setCustomerLayoutList(newCustomerLayoutList);
    }

    private void updateWidgetsTranslate(HomePageLayoutTO homePageLayoutTO, int tenantId, Locale locale) {
        if (StringUtils.isBlank(homePageLayoutTO.getAppId())) {
            return;
        }
        String model = PageTemplateUtils.getTemplateModel(homePageLayoutTO);
        String source = TempleType.WEB;
        List<JSONObject> list = new ArrayList<>();
        String ei = String.valueOf(tenantId);
        if (CollectionUtils.isNotEmpty(homePageLayoutTO.getCustomerLayoutList()) && homePageLayoutTO.getPageMultiType() == 1) {
            list = homePageLayoutTO.getCustomerLayoutList();
        } else {
            list.add(homePageLayoutTO.getCustomerLayout());
        }
        String layoutId = homePageLayoutTO.getLayoutId();
        List<I18nTrans.TransArg> i18nTranArgList = new ArrayList<>();
        List<I18nTrans.TransArg> tabTransArgList = new ArrayList<>();
        List<Localization> localizations = Lists.newArrayList();
        list.forEach(layout -> {
            if (null == layout) {
                return;
            }
            JSONArray components = new JSONArray();
            try {
                components = layout.getJSONArray(CustomerLayoutField.components);
            } catch (Exception e) {
                log.error("updateWidgetsTranslate error{}, layoutInfo{}", e, layout);
                return;
            }
            if (CollectionUtils.isNotEmpty(components)) {
                components.forEach(component -> {
                    JSONObject jsonObject = (JSONObject) component;
                    //组件内部节点同步多语
                    List<I18nInfo> i18nInfoList = ComponentExt.of(jsonObject).getI18nInfoList(ei, layoutId, ComponentExt.of(jsonObject).getApiName());
                    if (CollectionUtils.isNotEmpty(i18nInfoList)) {
                        List<Localization> localizationList = i18nInfoList.stream()
                                .map(x -> x.toLocalization(ei))
                                .filter(Objects::nonNull).collect(Collectors.toList());
                        localizations.addAll(localizationList);
                    }
                    // 页签容器的页签名称
                    if (CustomerLayoutField.TAB.equals(jsonObject.get(CustomerLayoutField.type))) {
                        JSONArray tabs = jsonObject.getJSONArray(CustomerLayoutField.tabCollection);
                        tabs.forEach(tab -> {
                            JSONObject tabObject = JSONObject.parseObject(JSONObject.toJSONString(tab));
                            String oldKey = TranslateI18nUtils.getWebTemplateWidgetsNameKey(homePageLayoutTO.getLayoutId(),
                                    tabObject.getString(CustomerLayoutField.apiName));
                            String tabsNameKey = ComponentExt.getTabTransKey(ei, homePageLayoutTO.getLayoutId(),
                                    ComponentExt.of(jsonObject).getApiName(), tabObject.getString(CustomerLayoutField.apiName));
                            String header = tabObject.getString(CustomerLayoutField.header);
                            I18nTrans.TransArg arg = TranslateI18nUtils.convertToTransArg(TranslateI18nUtils.delEiInKey(ei, tabsNameKey),
                                    Lists.newArrayList(oldKey), Lists.newArrayList(), header);
                            tabTransArgList.add(arg);
                        });
                    }
                    ComponentExt componentExt = ComponentExt.of(jsonObject);
                    String newHeader = org.apache.commons.lang3.StringUtils.firstNonBlank(componentExt.getNewHeader(),
                            componentExt.getTitleName(),
                            componentExt.getTitle(),
                            componentExt.getHeader());
                    String i18Key = TranslateI18nUtils.getWebTemplateWidgetsNameKey(homePageLayoutTO.getLayoutId(),
                            jsonObject.getString(CustomerLayoutField.apiName));
                    i18nTranArgList.add(I18nTrans.TransArg.builder()
                            .name(newHeader)
                            .customKey(TranslateI18nUtils.delEiInKey(ei, i18Key))
                            .preKeyList(homePageBaseService.buildPreKeyList(jsonObject, ei, layoutId, source, model))
                            .build());
                });
            }
        });
        if (WebPageGraySwitch.isGetTabsTransValueByOldKeyGrayEi(ei)) {
            localizations.addAll(i18nService.buildSyncParamV2(tenantId, tabTransArgList, locale.toLanguageTag(), true));
        } else {
            i18nTranArgList.addAll(tabTransArgList);
        }
        localizations.addAll(i18nService.buildSyncParam(tenantId, i18nTranArgList, locale.toLanguageTag(), false));
        i18nService.syncTransValue(tenantId, localizations);
    }

    private I18nTrans.TransArg buildTransArgByComponentType(JSONObject jsonObject, String layoutId, String ei) {
        String newHeader = jsonObject.getString(CustomerLayoutField.newHeader);
        if (StringUtils.isBlank(newHeader)) {
            return null;
        }
        String i18Key = TranslateI18nUtils.getWebTemplateWidgetsNameKey(layoutId, jsonObject.getString(CustomerLayoutField.apiName));
        String i18KeyWithoutEi = TranslateI18nUtils.delEiInKey(ei, i18Key);
        List<String> preKeys = Lists.newArrayList(i18Key);
        // BI组件
        if (isBICard(jsonObject)) {
            if (jsonObject.containsKey(CustomerLayoutField.itemI18nKeys)) {
                preKeys.addAll(jsonObject.getJSONArray(CustomerLayoutField.itemI18nKeys).toJavaList(String.class));
                return I18nTrans.TransArg.builder()
                        .name(newHeader)
                        .customKey(i18KeyWithoutEi)
                        .preKeyList(preKeys)
                        .build();
            }
        }
        // 场景组件名称是嵌套的, 925不把 对象/场景 作为预置翻译
        if (isScene(jsonObject)) {
            return I18nTrans.TransArg.builder()
                    .name(newHeader)
                    .customKey(i18KeyWithoutEi)
                    .preKeyList(preKeys)
                    .build();
        }
        String nameI18nKey = jsonObject.getString(CustomerLayoutField.nameI18nKey);
        if (StringUtils.isEmpty(nameI18nKey)) {
            // 查组件定义找预置key
            Widget widget = widgetsConfig.getWidgetByCardIdOrId(org.apache.commons.lang3.StringUtils.firstNonBlank
                            (jsonObject.getString(CustomerLayoutField.id), jsonObject.getString(CustomerLayoutField.type)),
                    jsonObject.getString(CustomerLayoutField.cardId));
            if (Objects.nonNull(widget)) {
                nameI18nKey = widget.getNameI18nKey();
                jsonObject.put(CustomerLayoutField.nameI18nKey, nameI18nKey);
            }
        }
        preKeys.add(nameI18nKey);
        return I18nTrans.TransArg.builder()
                .name(newHeader)
                .customKey(i18KeyWithoutEi)
                .preKeyList(preKeys)
                .build();
    }

    private JSONObject slideImageToFormat(String enterpriseAccount, int employeeId, JSONObject slideImageJSONObject) {
        JSONArray imgsArray = slideImageJSONObject.getJSONArray(CustomerLayoutField.imgs);

        List<String> slideImageTempFilePath = imgsArray.stream().map(x -> JSONObject.parseObject(String.valueOf(x)).getString(CustomerLayoutField.img)).filter(x -> StringUtils.isNotBlank(x)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(slideImageTempFilePath)) {
            throw new WebPageException(InterErrorCode.TN_IMG_NOT_FOUND);
        }

        Map<String, String> newSslideImageMap = tempFileToFormalFile.tempFileToFormalFile(enterpriseAccount, employeeId, slideImageTempFilePath, false);
        JSONArray newImgsArray = new JSONArray();
        imgsArray.forEach(x -> {
            JSONObject img = JSONObject.parseObject(String.valueOf(x));
            img.put(CustomerLayoutField.img, newSslideImageMap.get(img.getString(CustomerLayoutField.img)));
            newImgsArray.add(img);
        });
        slideImageJSONObject.put(CustomerLayoutField.imgs, newImgsArray);
        return slideImageJSONObject;
    }

    private JSONObject navigateMenuIconToFormat(String enterpriseAccount, int employeeId, JSONObject navigateJSONObject) {

        String menusString = navigateJSONObject.getString(CustomerLayoutField.menus);
        Boolean dataSourceEnv = navigateJSONObject.getBoolean(CustomerLayoutField.dataSourceEnv);
        if (dataSourceEnv == null) {
            dataSourceEnv = DataSourceEnv.CROSS.isType();
        }
        if (StringUtils.isEmpty(menusString)) {
            return navigateJSONObject;
        }
        JSONArray menus = JSONObject.parseArray(menusString);
        if (CollectionUtils.isEmpty(menus)) {
            return navigateJSONObject;
        }
        List<String> newIconTempFilePath = getNewIconTempFilePath(menus);
        Map<String, String> newIconMap = tempFileToFormalFile.tempFileToFormalFile(enterpriseAccount, employeeId, newIconTempFilePath, dataSourceEnv);

        List<JSONObject> newMenuJSONObject = menus.stream().map(x -> {
            JSONObject menuJSONObject = JSONObject.parseObject(JSONObject.toJSONString(x));
            String newIcon = menuJSONObject.getString(CustomerLayoutField.newIcon);
            if (StringUtils.isNotEmpty(newIcon) && newIcon.startsWith("TN_")) {
                menuJSONObject.put(CustomerLayoutField.newIcon, newIconMap.get(newIcon));
            }
            return menuJSONObject;
        }).collect(Collectors.toList());
        navigateJSONObject.put(CustomerLayoutField.menus, newMenuJSONObject);
        return navigateJSONObject;
    }

    private List<String> getNewIconTempFilePath(JSONArray menus) {

        if (CollectionUtils.isEmpty(menus)) {
            return Lists.newArrayList();
        }
        return menus.stream().
                map(jsonObject -> {
                    JSONObject menuJSONObject = JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                    String newIcon = menuJSONObject.getString(CustomerLayoutField.newIcon);
                    if (StringUtils.isNotEmpty(newIcon) && newIcon.startsWith("TN_")) {
                        return newIcon;
                    }
                    return null;
                }).
                filter(Objects::nonNull).
                collect(Collectors.toList());
    }

    @Override
    @RequestMapping(value = "/GetHomePageLayoutList", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_INDEXMANAGE)
    public GetHomePageLayoutListResult getHomePageLayoutList(@FSUserInfo UserInfo userInfo,
                                                             @FSClientInfo ClientInfo clientInfo,
                                                             @RequestBody(required = false) GetHomePageListArg arg) {
        try {
            RequestContextManager.initContextForIsFromManage(true);
            List<HomePageLayoutTO> homePageLayoutList = Lists.newArrayList();
            int enterpriseId = userInfo.getEnterpriseId();
            String appId;
            int appType;
            if (arg == null || (StringUtils.isEmpty(arg.getApiName()) && arg.getAppType() == 0)) {
                appId = Constant.APP_CRM;
                appType = BizType.CRM.getType();
            } else {
                appType = arg.getAppType();
                appId = StringUtils.isEmpty(arg.getApiName()) ? arg.getAppId() : arg.getApiName();
            }
            if (appType == BizType.BI.getType() && !BIHomePageGraySwitch.isAllowByBusiness(BIHomePageGraySwitch.BI_HOMEPAGE_ENTERPRISEACCOUNT, userInfo.getEnterpriseAccount())) {
                return new GetHomePageLayoutListResult();
            }
            homePageLayoutList = homePageBaseService.getHomePageLayoutList(enterpriseId, appType, appId, arg.getApplyType(), clientInfo.getLocale());
            if (appType == BizType.CUSTOMER.getType()) {
                boolean checkGoNewCRM = checkService.checkGoNewCRM(userInfo.getEnterpriseId());
                if (!checkGoNewCRM) {
                    homePageLayoutList = homePageLayoutList.stream().filter(x -> !x.getFromOldCrmHomePage() || "PortalPage".equals(x.getAppId())).collect(Collectors.toList());
                }
            }
            GetHomePageLayoutListResult result = new GetHomePageLayoutListResult();
            if (CollectionUtils.isEmpty(homePageLayoutList)) {
                result.setHomePageLayoutList(Lists.newArrayList());
            } else {
                List<HomePageLayoutTO> homePageLayoutTOList = buildHomePageLayoutList(userInfo.getEnterpriseId(), appType, appId, homePageLayoutList);
                homePageLayoutTOList = homePageLayoutTOList.stream().filter(x -> !SourceType.synFromOldCrmView.equals(x.getSourceType())).collect(Collectors.toList());
                result.setHomePageLayoutList(homePageLayoutTOList);
            }

            if (CollectionUtils.isNotEmpty(result.getHomePageLayoutList()) && null != arg.getApplyType() && arg.getApplyType() == 1) {
                List<SimpleLinkAppResult> upSimpleLinkApps = remoteCrossService.getUpSimpleLinkApp(userInfo.getEnterpriseId(), clientInfo.getLocale());
                List<SimpleLinkAppResult> dirtyDataList = upSimpleLinkApps.stream()
                        .filter(x -> StringUtils.isEmpty(x.getAppName()) || StringUtils.isEmpty(x.getAppId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(dirtyDataList)) {
                    log.warn("upSimpleLinkApps appName or appId empty! dirtyDataList list:{}", JSON.toJSONString(dirtyDataList));
                }
                Map<String, String> map = upSimpleLinkApps.stream()
                        .filter(Objects::nonNull)
                        .filter(x -> StringUtils.isNotEmpty(x.getAppId()) && StringUtils.isNotEmpty(x.getAppName()))
                        .collect(Collectors.toMap(SimpleLinkAppResult::getAppId, SimpleLinkAppResult::getAppName));
                //将老得互联web自定义页面数据里的  appId==CrossPortalPage 替换成  渠道门户真正的appId
                result.getHomePageLayoutList().forEach(x -> {
                    if (StringUtils.isNotBlank(x.getAppId()) && BizType.CROSSCUSTOMER.getDefaultAppId().equals(x.getAppId())) {
                        x.setAppId(defaultTenantConfig.getQudaomenhuAppId());
                    }
                    x.setAppName(null != map && null != map.get(x.getAppId()) ? map.get(x.getAppId()) : x.getAppName());
                });
            }
            return result;
        } finally {
            RequestContextManager.removeContext();
        }
    }

    @Override
    @RequestMapping(value = "/SetHomePageLayoutStatus", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public SetHomePageLayoutStatusResult setHomePageLayoutStatus(@FSUserInfo UserInfo userInfo, @RequestBody SetHomePageLayoutStatusArg arg) {
        boolean updateHomePageStatus = homePageBaseService.updateHomePageStatus(userInfo.getEnterpriseId(), arg.getAppType(), arg.getLayoutId(), arg.getLayoutApiName(), arg.getStatus(), userInfo.getEmployeeId());
        SetHomePageLayoutStatusResult result = new SetHomePageLayoutStatusResult();
        result.setSuccess(updateHomePageStatus);
        return result;
    }

    @Override
    @RequestMapping(value = "/DeleteHomePageLayout", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public DeleteHomePageLayoutResult deleteHomePageLayout(@FSUserInfo UserInfo userInfo, @RequestBody DeleteHomePageLayoutArg arg) {
        boolean deleteHomePageLayout = homePageBaseService.deleteHomePageLayout(userInfo.getEnterpriseId(), arg.getLayoutId(), userInfo.getEmployeeId());
        DeleteHomePageLayoutResult result = new DeleteHomePageLayoutResult();
        result.setSuccess(deleteHomePageLayout);
        return result;
    }

    @Override
    @RequestMapping(value = "/CheckCanAddHomePageLayout", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public CheckCanAddHomePageLayoutResult checkCanAddHomePageLayout(@FSUserInfo UserInfo userInfo, @RequestBody CheckCanAddHomePageLayoutArg arg) {
        String appId = arg.getAppId();
        CheckCanAddHomePageLayoutResult result = new CheckCanAddHomePageLayoutResult();
        result.setCanAddHomePageLayout(true);
        if (StringUtils.isNotEmpty(appId) && !Constant.APP_CRM.equals(appId)) {
            return result;
        }
        checkAddHomePage(userInfo, Constant.APP_CRM, arg.getLayoutType());
        return result;
    }

    @Override
    @RequestMapping(value = "/GetEmployeeHomePageLayoutList", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetEmployeeHomePageLayoutListResult getEmployeeHomePageLayoutList(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo) {

        if (Objects.isNull(userInfo.getEnterpriseId()) || Objects.isNull(userInfo.getEmployeeId())) {
            log.warn("GetEmployeeHomePageLayoutList param error: userInfo:{}", userInfo);
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        Integer enterpriseId = userInfo.getEnterpriseId();
        Boolean personPageConfig = homePageBaseService.queryEnablePersonPageConfig(enterpriseId);
        List<HomePageLayoutTO> employeeHomePageLayoutList = homePageBaseService.getEmployeeHomePageLayoutList(enterpriseId, userInfo.getEmployeeId(), BizType.CRM.getType(), Constant.APP_CRM, clientInfo.getLocale(), personPageConfig);
        //旧版crm，需要过滤掉新版crm的视图中的首页，因为此时该企业还没有确认迁移
        employeeHomePageLayoutList = employeeHomePageLayoutList.stream().filter(homePageLayoutTO -> !homePageLayoutTO.getFromOldCrmHomePage()).collect(Collectors.toList());
        GetEmployeeHomePageLayoutListResult result = new GetEmployeeHomePageLayoutListResult();
        result.setEnablePersonPageConfig(personPageConfig);
        result.setHomePageLayoutTOList(employeeHomePageLayoutList);
        result.setMd5Version(getMd5Version(employeeHomePageLayoutList, personPageConfig));
        return result;
    }

    private String getMd5Version(List<HomePageLayoutTO> homePageLayoutTOList, Boolean personPageConfig) {
        try {
            if (CollectionUtils.isEmpty(homePageLayoutTOList) && personPageConfig == null) {
                return null;
            }
            StringBuffer stringBuffer = new StringBuffer();
            if (CollectionUtils.isNotEmpty(homePageLayoutTOList)) {
                stringBuffer.append(homePageLayoutTOList);
            }
            if (personPageConfig != null) {
                stringBuffer.append(personPageConfig);
            }
            String md5Hex = DigestUtils.md5Hex(stringBuffer.toString());
            return md5Hex;
        } catch (Exception e) {
            logger.error("getMd5Version error by homePageLayoutTOList:{}, personPageConfig:{}", homePageLayoutTOList, personPageConfig, e);
        }
        return null;
    }

    @Override
    @RequestMapping(value = "/SetEmployeeCurrentHomePageLayout", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public SetEmployeeCurrentHomePageLayoutResult setEmployeeCurrentHomePageLayout(@FSUserInfo UserInfo userInfo, @RequestBody SetEmployeeCurrentHomePageLayoutArg arg) {
        arg.valid();
        String appId;
        if (arg == null || StringUtils.isEmpty(arg.getApiName())) {
            appId = Constant.APP_CRM;
        } else {
            appId = arg.getApiName();
        }
        boolean updateEmployeeCurrentHomePageLayout = userHomePageBaseService.updateEmployeeCurrentHomePageLayout(
                arg.getLayoutId(),
                userInfo.getEnterpriseId(),
                userInfo.getEmployeeId(),
                appId,
                HomePageConstant.web);
        SetEmployeeCurrentHomePageLayoutResult result = new SetEmployeeCurrentHomePageLayoutResult();
        result.setSuccess(updateEmployeeCurrentHomePageLayout);

        return result;
    }

    @Override
    @RequestMapping(value = "/ModifyVendorHomePageLayout", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.ENTERPRISE_INTERCONNECT_SETTING)
    public ModifyVendorHomePageResult modifyVendorHomePage(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody ModifyVendorHomePageArg arg) {
        HomePageLayoutTO homePageLayout = arg.getHomePageLayout();
        List<HomePageLayoutCard> homePageLayouts = buildHomePageCardOrder(homePageLayout.getHomePageLayouts());
        if (homePageLayouts.size() > VENDORMAX) {
            throw new WebPageException(InterErrorCode.MAX_CHART_CARDS);
        }
        checkTitle(homePageLayouts);    //判断卡片标题
        String appId = Constant.VENDOR_APPID;
        int enterpriseId = userInfo.getEnterpriseId();
        int employeeId = userInfo.getEmployeeId();
        String layoutId = homePageLayout.getLayoutId();
        HomePageLayoutTO homePageLayoutTO;
        if (!Strings.isNullOrEmpty(layoutId)) {
            homePageLayoutTO = homePageBaseService.getHomePageLayoutByApiName(userInfo, layoutId, homePageLayout.getApiName(), BizType.APP.getType(), clientInfo, true);
            if (homePageLayoutTO == null) {
                throw new WebPageException(InterErrorCode.HOME_DOES_NOT_EXIST);
            }
            homePageLayoutTO = homePageBaseService.updateHomePageLayout(BizType.APP.getType(), appId, enterpriseId, employeeId, arg.getLayoutApiName(), null, homePageLayout);
        } else {
            checkAddHomePage(userInfo, appId, homePageLayout.getLayoutType());
            homePageLayoutTO = homePageBaseService.insertHomePageLayout(appId, BizType.APP.getType(), enterpriseId, employeeId, arg.getLayoutApiName(), null, homePageLayout);
        }
        ModifyVendorHomePageResult result = new ModifyVendorHomePageResult();
        result.setLayoutId(homePageLayoutTO.getLayoutId());

        return result;
    }

    @Override
    @RequestMapping(value = {"/getVendorHomePageById"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.ENTERPRISE_INTERCONNECT_SETTING)
    public GetVendorHomePageByIdResult getVendorHomePageById(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody GetVendorHomePageByIdArg arg) {
        arg.valid();
        HomePageLayoutTO homePageLayoutTO = homePageBaseService.getHomePageLayoutByApiName(userInfo, arg.getLayoutId(), arg.getLayoutApiName(), BizType.APP.getType(), clientInfo, true);
        if (homePageLayoutTO == null) {
            throw new WebPageException(InterErrorCode.HOME_DOES_NOT_EXIST);
        }
        homePageLayoutTO.getHomePageLayouts().stream().forEach(homePageLayoutCard -> {
            String url = biUrlUtil.buildUrl(homePageLayoutCard.getCardId(), homePageLayoutCard.getType());
            homePageLayoutCard.setUrl(url);
        });
        GetVendorHomePageByIdResult result = new GetVendorHomePageByIdResult();
        result.setHomePageLayout(homePageLayoutTO);
        logger.debug("getHomePageLayoutById result {}", result);
        return result;
    }

    @Override
    @RequestMapping(value = {"/getVendorComponents"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.ENTERPRISE_INTERCONNECT_SETTING)
    public GetVendorComponentsResult getVendorComponents(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo) {
        List<HomePageComponent> homePageComponent = getHomePageComponent(BizType.APP.getType(), userInfo.getEnterpriseId(), Constant.VENDOR_APPID, clientInfo.getLocale());
        GetVendorComponentsResult result = new GetVendorComponentsResult();
        result.setComponents(homePageComponent);
        return result;
    }

    @Override
    @RequestMapping(value = {"/getHomePageComponentList"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetHomePageComponentsResult getHomePageComponentList(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody GetHomePageComponentsArg arg) {
        List<HomePageComponent> homePageComponent = getHomePageComponent(BizType.APP.getType(), userInfo.getEnterpriseId(), arg.getAppId(), clientInfo.getLocale());
        GetHomePageComponentsResult result = new GetHomePageComponentsResult();
        result.setComponents(homePageComponent);
        return result;
    }

    @Override
    @RequestMapping(value = {"/getObjectList"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetObjectListResult getObjectList(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody GetObjectListArg arg) {
        List<ObjectVO> objectList = objectService.getObjectList(arg.getAppId(), userInfo.getEnterpriseId(), userInfo.getEnterpriseAccount(), clientInfo.getLocale());
        GetObjectListResult result = new GetObjectListResult();
        result.setObjectList(objectList);
        return result;
    }

    @Override
    @RequestMapping(value = {"/getOutTemplate"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public Object getOutTemplate(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody GetOutTemplateArg arg) {
        return sceneService.findOuterTenantSceneList(userInfo.getEnterpriseId(), arg.getApiName(), clientInfo.getLocale());
    }

    @Override
    @RequestMapping(value = {"/getInnerObjectList"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetInnerObjectListResult getInnerObjectList(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo) {
        int tenantId = userInfo.getEnterpriseId();
        List<ObjectVO> innerObjectList = objectService.getInnerObjectList(tenantId, clientInfo.getLocale());
        GetInnerObjectListResult result = new GetInnerObjectListResult();
        result.setInnerObjectList(innerObjectList);
        return result;
    }

    private String getBizId(int appType, String argAppId) {
        if (StringUtils.isBlank(argAppId)) {
            return argAppId;
        }
        if (appType == BizType.CROSSCUSTOMER.getType()
                && StringUtils.isNotEmpty(argAppId)
                && !BizType.CUSTOMER.getDefaultAppId().equals(argAppId)
                && !argAppId.contains("Object")) {
            return BizType.CROSSCUSTOMER.getDefaultAppId();
        }
        if (BizType.isWebsite(appType)) {
            BizType bizTypeValue = BizType.getBizTypeValue(appType);
            if (bizTypeValue != null) {
                return bizTypeValue.getDefaultAppId();
            }
        }
        return argAppId;
    }

    @Override
    @RequestMapping(value = {"/getDropListItems"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetDropListItemsResult getDropListItems(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody GetDropListItemsArg arg) {
        int appType = arg.getAppType();
        String appId = getBizId(appType, arg.getAppId());
        Pair<List<DropListItem>, List<DropListItem>> pageDropListResult =
                customerDropListService.getPageDropList(userInfo, appType, appId, clientInfo.getLocale(), arg.getAppId(), arg.isPreviewNewCrmFlag());
        GetDropListItemsResult result = new GetDropListItemsResult();
        List<DropListItem> webDropListItems = pageDropListResult.getLeft();
        List<DropListItem> appDropListItems = pageDropListResult.getRight();    // app获取dropList 目前在 userExt中
        if (CollectionUtils.isNotEmpty(webDropListItems)) { // 设置页面组件限额
            String finalAppId = appId;
            webDropListItems.forEach(x -> {
                if (ComponentConstant.slideImage.equals(x.getId()) && !WebPageGraySwitch.isAllowWebpageAndUserextGrayEi(userInfo.getEnterpriseId())) {
                    x.getComponent().put(ComponentConstant.LIMIT, 1);
                }
                if (ComponentConstant.biCustomer.equals(x.getId()) && Constant.APP_CRM.equals(finalAppId)) {
                    x.getComponent().put(ComponentConstant.LIMIT, homePageMaxConfig.getChartMaxCount(userInfo.getEnterpriseAccount()));
                }
            });
            result.setDropListItemList(webDropListItems);
        }
        if (CollectionUtils.isNotEmpty(appDropListItems)) {
            result.setAppDropListItemList(appDropListItems);
        }

        String configAppId = TempleIdUtil.getPrefixByAppId(appType, appId);
        if (ObjectLayoutTypeEnum.ObjectDetailPage.getWebPageKey().equals(configAppId)
                || ObjectLayoutTypeEnum.ObjectFlowTaskListPage.getWebPageKey().equals(configAppId)
                || ObjectLayoutTypeEnum.ObjectWhatListPage.getWebPageKey().equals(configAppId)
                || ObjectLayoutTypeEnum.BigObjectDetailPage.getWebPageKey().equals(configAppId)) {
            Set<String> appShowCusComponents = appDropListItems.stream().
                    filter(x -> ComponentConstant.CUSTOM_COMP.equals(x.getComponent().getString("type"))).
                    map(x -> x.getId()).collect(Collectors.toSet());
            List<String> appShowComponents = componentConfig.getAppShowComponents();
            appShowCusComponents.addAll(appShowComponents);
            result.setAppShowComponents(new ArrayList<>(appShowCusComponents));
        }

        if("CRM".equals(appId) && BooleanUtils.isNotTrue(arg.getFromManage())){    // 个人级视图屏蔽掉功能入口(只有CRM有个人级视图编辑能力)
            CollectionUtils.emptyIfNull(result.getDropListItemList())
                    .removeIf(x -> functionEnter.equals(x.getId()));
        }

        return result;
    }

    @Override
    @RequestMapping(value = {"/getComponentList"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetObjectLayoutComponentsResult getComponentList(@FSUserInfo UserInfo userInfo,
                                                            @FSClientInfo ClientInfo clientInfo,
                                                            @RequestBody GetObjectLayoutComponentsArg arg) {
        int bizType = arg.getBizType();
        String bizId = arg.getBizId();
        String appId = arg.getAppId();
        if (!crmPassCacheManager.supportAppLayered(String.valueOf(userInfo.getEnterpriseId()),
                arg.getDescribeApiName(), appId)) {
            appId = null;
        }
        Pair<List<DropListItem>, List<DropListItem>> pageDropListResult = customerDropListService.getPageDropList(userInfo,
                bizType, bizId + ComponentConstant.SEPARATOR + arg.getDescribeApiName(), clientInfo.getLocale(), appId, arg.isPreviewNewCrmFlag());

        GetObjectLayoutComponentsResult result = new GetObjectLayoutComponentsResult();
        List<DropListItem> webDropListItems = getWebDropListItems(userInfo, arg.getBizIdByBizType(), pageDropListResult.getLeft());
        if (CollectionUtils.isNotEmpty(webDropListItems)) {
            result.setDropListItemList(webDropListItems);
        }
        List<DropListItem> appDropListItems = pageDropListResult.getRight();
        if (CollectionUtils.isNotEmpty(appDropListItems)) {
            result.setAppDropListItemList(appDropListItems);
        }
        List<String> appShowComponents = getAppShowComponents(bizId, appDropListItems);
        if (CollectionUtils.isNotEmpty(appShowComponents)) {
            result.setAppShowComponents(appShowComponents);
        }
        return result;
    }

    @Override
    @RequestMapping(value = {"/supportAppLayered"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public SupportAppLayeredResult supportAppLayered(@FSUserInfo UserInfo userInfo,
                                                     @FSClientInfo ClientInfo clientInfo,
                                                     @RequestBody SupportAppLayeredArg arg) {
        boolean supportAppLayered = crmPassCacheManager.supportAppLayered(String.valueOf(userInfo.getEnterpriseId()),
                arg.getDescribeApiName(), arg.getAppId());
        SupportAppLayeredResult result = new SupportAppLayeredResult();
        result.setSupportAppLayered(supportAppLayered);
        return result;
    }

    private List<String> getAppShowComponents(String bizId, List<DropListItem> appDropListItems) {
        if (ObjectLayoutTypeEnum.ObjectDetailPage.getWebPageKey().equals(bizId)
                || ObjectLayoutTypeEnum.ObjectFlowTaskListPage.getWebPageKey().equals(bizId)
                || ObjectLayoutTypeEnum.ObjectWhatListPage.getWebPageKey().equals(bizId)
                || ObjectLayoutTypeEnum.BigObjectDetailPage.getWebPageKey().equals(bizId)) {
            Set<String> appShowCusComponents = appDropListItems.stream()
                    .filter(x -> ComponentConstant.CUSTOM_COMP.equals(x.getComponent().getString("type")))
                    .map(DropListItem::getId)
                    .collect(Collectors.toSet());
            List<String> appShowComponents = componentConfig.getAppShowComponents();
            appShowCusComponents.addAll(appShowComponents);
            return new ArrayList<>(appShowCusComponents);
        }
        return Collections.emptyList();
    }

    private List<DropListItem> getWebDropListItems(UserInfo userInfo, String bizId, List<DropListItem> webDropListItems) {
        if (CollectionUtils.isNotEmpty(webDropListItems)) {
            webDropListItems.forEach(x -> {
                if (ComponentConstant.slideImage.equals(x.getId()) && !WebPageGraySwitch.isAllowWebpageAndUserextGrayEi(userInfo.getEnterpriseId())) {
                    x.getComponent().put(ComponentConstant.LIMIT, 1);
                }
                if (ComponentConstant.biCustomer.equals(x.getId()) && Constant.APP_CRM.equals(bizId)) {
                    x.getComponent().put(ComponentConstant.LIMIT, homePageMaxConfig.getChartMaxCount(userInfo.getEnterpriseAccount()));
                }
            });
        }
        return webDropListItems;
    }

    @Override
    @RequestMapping(value = {"/enablePersonPage"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public EnablePersonPageResult enablePersonPage(@FSUserInfo UserInfo userInfo) {
        homePageBaseService.updatePersonPageConfig(userInfo.getEnterpriseId(), true);
        EnablePersonPageResult result = new EnablePersonPageResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    @RequestMapping(value = {"/disablePersonPage"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public DisablePersonPageResult disablePersonPage(@FSUserInfo UserInfo userInfo) {
        homePageBaseService.updatePersonPageConfig(userInfo.getEnterpriseId(), false);
        DisablePersonPageResult result = new DisablePersonPageResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    @RequestMapping(value = {"/queryPersonPageConfig"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public QueryPersonPageConfigResult queryPersonPageConfig(@FSUserInfo UserInfo userInfo) {
        Boolean personPageConfig = homePageBaseService.queryEnablePersonPageConfig(userInfo.getEnterpriseId());
        QueryPersonPageConfigResult result = new QueryPersonPageConfigResult();
        result.setEnablePersonPage(personPageConfig);
        return result;
    }

    @Override
    @RequestMapping(value = {"/checkHasLicenseModule"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public HasLicenseModuleResult checkHasLicenseModule(@FSUserInfo UserInfo userInfo, @RequestBody HasLicenseModuleArg arg) {
        arg.valid();
        List<String> versionAndPackages = remoteService.getVersionAndPackages(userInfo.getEnterpriseId());
        HasLicenseModuleResult result = new HasLicenseModuleResult();
        Map<String, Boolean> hasLicenseModuleMap = Maps.newHashMap();
        arg.getLicenseModuleKeys().stream().forEach(x -> {
            hasLicenseModuleMap.put(x, versionAndPackages.contains(x));
        });
        result.setHasLicenseModuleMap(hasLicenseModuleMap);
        return result;
    }

    @RequestMapping(value = {"/findObjectPageComponentList"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public FindObjectPageComponentList findObjectPageComponentList(@FSUserInfo UserInfo userInfo, @RequestBody FindCustomerWidgetArg arg) {
        List<ObjectPageComponent> componentList = widgetsConfig.getAllWidgetsNoCopy().stream()
                .filter(it -> filter(arg.includeEmptyFunctionCode(), it))
                .map(this::buildWidget)
                .collect(Collectors.toList());
        return FindObjectPageComponentList.builder()
                .objectPageComponents(componentList)
                .build();
    }

    private boolean filter(boolean includeEmptyFunctionCode, Widget widget) {
        if (includeEmptyFunctionCode) {
            return true;
        }
        return Optional.ofNullable(widget)
                .map(Widget::getTenantPrivilege)
                .map(TenantPrivilege::getFunctionCode)
                .map(StringUtils::isNotEmpty)
                .orElse(false);
    }

    private ObjectPageComponent buildWidget(Widget widget) {
        ObjectPageComponent objectPageComponent = ObjectPageComponent.builder()
                .apiName(widget.getApiName())
                .build();
        Optional.ofNullable(widget.getTenantPrivilege()).ifPresent(tenantPrivilege -> {
            objectPageComponent.setFunctionCode(tenantPrivilege.getFunctionCode());
        });

        objectPageComponent.setType(getType(widget));
        return objectPageComponent;
    }

    private String getType(Widget widget) {
        String type = widgetCollectionConfig.getWithDefineWidgets(widget.getId())
                .map(WidgetCollection::getId)
                .flatMap(componentListManager::getComponentByCollectionId)
                .filter(component -> Objects.equals(ComponentTypConst.WIDGET_COLLECTION_TYPE, component.getComponentType()))
                .map(component -> componentNameConfig.getComponentName(StringUtils.isEmpty(widget.getCardId()) ? widget.getId() : widget.getCardId()))
                .orElse(null);

        if (StringUtils.isNotEmpty(type)) {
            return type;
        }
        return componentListManager.getComponentById(widget.getId())
                .filter(component -> Objects.equals(ComponentTypConst.WIDGET_TYPE, component.getComponentType()))
                .map(component -> componentNameConfig.getComponentName(StringUtils.isEmpty(widget.getCardId()) ? widget.getId() : widget.getCardId()))
                .orElse(null);
    }

    private List<HomePageLayoutTO> buildHomePageLayoutList(int tenantId, int appType, String appId, List<HomePageLayoutTO> homePageLayoutTOList) {
        if (CollectionUtils.isEmpty(homePageLayoutTOList)) {
            return Lists.newArrayList();
        }

        List<Scope> scopeList = Lists.newArrayList();
        homePageLayoutTOList.stream().forEach(homePageLayoutTO -> {
            if (CollectionUtils.isNotEmpty(homePageLayoutTO.getScopes())) {
                scopeList.addAll(homePageLayoutTO.getScopes());
            }
        });

        if (BizType.BI.getType() == appType || appType == BizType.CUSTOMER.getType()) {
            appId = Constant.APP_CRM;
        }

        Map<Integer, Object> scopeNameMap = organizationCommonService.getScopeName(tenantId, appId, scopeList, null);

        for (HomePageLayoutTO homePageLayoutTO : homePageLayoutTOList) {
            String scopeNames = ScopesUtil.getLayoutScopeName(scopeNameMap, homePageLayoutTO.getScopes());
            homePageLayoutTO.setLayoutScopeName(scopeNames);
        }
        return homePageLayoutTOList;
    }


    private List<HomePageLayoutCard> buildHomePageCardOrder(List<HomePageLayoutCard> homePageLayoutCardList) {

        if (CollectionUtils.isEmpty(homePageLayoutCardList)) {
            return Lists.newArrayList();
        }

        List<HomePageLayoutCard> pageLayoutCards = homePageLayoutCardList.stream().
                sorted(Comparator.comparing(HomePageLayoutCard::getOrder)).
                collect(Collectors.toList());
        int count = 1;
        for (HomePageLayoutCard homePageLayoutCard : homePageLayoutCardList) {
            homePageLayoutCard.setOrder(count++);
        }
        return pageLayoutCards;
    }

    private void checkTitle(List<HomePageLayoutCard> homePageLayoutCardList) {
        if (CollectionUtils.isEmpty(homePageLayoutCardList)) {
            return;
        }
        for (HomePageLayoutCard homePageLayoutCard : homePageLayoutCardList) {
            if (Strings.isNullOrEmpty(homePageLayoutCard.getTitle())) {
                throw new WebPageException(InterErrorCode.TITLE_CANNOT_BE_BLANK);
            }
        }
    }

    private List<HomePageComponent> getHomePageComponent(int appType, int tenantId, String appId, Locale locale) {

        List<HomePageComponent> homePageComponentList = Lists.newArrayList();
        List<ComponentDto> componentDtoList = componentListManager.getComponentDtoListByAppId(appId);

        Map<String, String> componentLanguage = languageService.queryComponentLanguage(tenantId, componentDtoList, locale);

        List<String> versionAndPackages = remoteService.getVersionAndPackages(tenantId);

        for (ComponentDto componentDto : componentDtoList) {
            if (!defaultTenant(tenantId)) {
                boolean checkPrivilege = componentService.checkPrivilege(appType, appId, componentDto, Sets.newHashSet(), versionAndPackages, false, Lists.newArrayList());
                if (!checkPrivilege) {
                    continue;
                }
            }

            HomePageComponent homePageComponent = new HomePageComponent();
            Widget widget = componentDto.getWidget();
            homePageComponent.setId(componentDto.getId());
            homePageComponent.setCardId(widget == null ? "" : widget.getCardId());
            homePageComponent.setType(widget == null ? 0 : widget.getWidgetType());
            homePageComponent.setParentId(componentDto.getParentId());
            int componentType = componentDto.getComponentType();
            if (componentType == 3) {
                componentType = 1;
            } else if (componentType == 4) {
                componentType = 2;
            }
            homePageComponent.setComponentType(componentType);
            homePageComponent.setAppId(componentDto.getTenantPrivilege() == null ? "" : componentDto.getTenantPrivilege().getAppId());
            homePageComponent.setUrl(widget == null ? "" : biUrlUtil.buildUrl(widget.getCardId(), widget.getWidgetType()));
            if (widget == null) {
                homePageComponent.setTitle(StringUtils.isEmpty(componentLanguage.get(componentDto.getTitleI18nKey())) ? componentDto.getTitle() : componentLanguage.get(componentDto.getTitleI18nKey()));
            } else {
                homePageComponent.setTitle(StringUtils.isEmpty(componentLanguage.get(widget.getNameI18nKey())) ? widget.getName() : componentLanguage.get(widget.getNameI18nKey()));
            }
            homePageComponentList.add(homePageComponent);
        }

        return homePageComponentList;
    }


    private boolean defaultTenant(int tenantId) {
        List<String> defaultTenantIds = this.defaultTenantConfig.getDefaultTenantConfig();
        return defaultTenantIds.contains(String.valueOf(tenantId));
    }

    private boolean checkAddHomePage(UserInfo userInfo, String appId, int layoutType) {
        String enterpriseAccount = userInfo.getEnterpriseAccount();
        int num = homePageMaxConfig.getNum(enterpriseAccount, layoutType, ChecMaxPageNumEnum.HOME_PAGE.getPageType());
        if (num == 0) {
            return true;
        }
        long count = homePageBaseService.getPageCount(appId, userInfo.getEnterpriseId(), layoutType, userInfo.getEmployeeId());
        if (count >= num) {
            throw new WebPageException(InterErrorCode.MAX_HOMEPAGELAYOUT);
        }
        return true;
    }

    private int getAppType(Integer appType, String appId) {
        if ((appType == 0 || appType == null || appType == 1) && (Constant.APP_CRM.equals(appId) || StringUtils.isEmpty(appId))) {
            return BizType.CRM.getType();
        } else if ((StringUtils.isNotEmpty(appId) && appType == 0) || appType == BizType.APP.getType()) {
            return BizType.APP.getType();
        }
        if (appType != null) {
            return appType;
        }

        return 0;
    }

    private String getAppId(int appType, String appId) {
        if (StringUtils.isEmpty(appId) && appType != BizType.CUSTOMER.getType()) {
            return Constant.APP_CRM;
        } else {
            return appId;
        }
    }

    private void doCheckHomePage(UserInfo userInfo, int appType, String appId, HomePageLayoutTO homePageLayout) {
        HomePageHelper homePageHelper = homePageHelperMap.get(appType);
        if (homePageHelper == null) {
            return;
        }
        String enterpriseAccount = userInfo.getEnterpriseAccount();
        int enterpriseId = userInfo.getEnterpriseId();
        int employeeId = userInfo.getEmployeeId();

        int layoutType = homePageLayout.getLayoutType();

        homePageHelper.checkCrmManagerPermission(enterpriseId, employeeId, layoutType, homePageLayout.getScopes());
//        homePageHelper.checkHomePageName(enterpriseId, employeeId, layoutType, homePageLayout.getLayoutId(), appId, homePageLayout.getName());
//        homePageHelper.checkHomePageScope(homePageLayout.getScopes());
        homePageHelper.checkHomePageLayout(homePageLayout.getDataVersion(), homePageLayout.getHomePageLayouts(), homePageLayout.getCustomerLayout());
        List<JSONObject> customerLayoutList = new ArrayList<>();
        customerLayoutList.add(homePageLayout.getCustomerLayout());
        if (homePageLayout.getPageMultiType() == 1 && CollectionUtils.isNotEmpty(homePageLayout.getCustomerLayoutList())) {
            customerLayoutList.addAll(homePageLayout.getCustomerLayoutList());
        }
        customerLayoutList.forEach(customerLayout -> {
            homePageHelper.checkComponentLimit(enterpriseAccount, homePageLayout.getDataVersion(), homePageLayout.getHomePageLayouts(), customerLayout);
        });
//        homePageHelper.checkAddHomePage(enterpriseAccount, enterpriseId, employeeId, homePageLayout.getLayoutId(), appId, layoutType);
//        homePageHelper.checkFirstHomePage(enterpriseId, appId, homePageLayout.getScopes());
    }

}
