package com.facishare.webpage.customer.designer.model;

import com.facishare.webpage.customer.core.model.DropListItem;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface StandGetDropList {

    @Data
    @SuperBuilder(toBuilder = true)
    class Arg implements Serializable {
        private String bizId;
    }

    @Data
    @SuperBuilder(toBuilder = true)
    class Result implements Serializable {
        private List<DropListItem> dropListItemList;
    }

}
