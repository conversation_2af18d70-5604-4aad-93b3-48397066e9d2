package com.facishare.webpage.customer.api.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/14.
 */
@Data
public class UserMenuItem implements Serializable {

    @JSONField(name = "M1")
    private String id;

    @JSONField(name = "M2")
    private String pid;

    @JSONField(name = "M3")
    private String type;

    @JSONField(name = "M4")
    private String url;

    //菜单项id
    @JSONField(name = "M5")
    private String menuItemId;

    @JSONField(name = "M7")
    private String displayName;

    @J<PERSON>NField(name = "M8")
    private String referenceApiname;

    @JSONField(name = "M9")
    private Integer iconIndex;

    @JSONField(name = "M10")
    private String iconPathHome;

    @JSONField(name = "M11")
    private List<String> privilegeAction = Lists.newArrayList();

    @J<PERSON><PERSON>ield(name = "M12")
    private Boolean isHidden;

    @J<PERSON><PERSON>ield(name = "M13")
    private Integer number;

    @J<PERSON><PERSON>ield(name = "M14")
    private String iconPathMenu;

    @JSONField(name = "M15")
    private MobileConfig mobileConfig;

    @JSONField(name = "M16")
    private Boolean useDefaultUrl = false;

    @JSONField(name = "M17")
    private List<String> searchWords;

    @JSONField(name = "M18")
    private String appId;

    @JSONField(name = "M19")
    private String webGoJumpUrl;

    /**
     * web菜单 点击是否新开窗口  不为空即为true
     */
    @JSONField(name = "M20")
    private String target;
    @JSONField(name = "M21")
    private String fxIcon;

    @JSONField(name = "M22")
    private Integer iconSlot;

    @JSONField(name = "M23")
    private List<String> i18nKeyList;

}
