package com.facishare.webpage.customer.api.model.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 预置互联应用的预置角色关联表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeleteLinkAppRoleAra {
    private Integer tenantId;

    /**
     * 企业互联应用Id
     */
    private String linkAppId;


    private String roleId;
    private List<String> roleIds;
    private List<String> linkAppIds;

    /**
     * 1：预置应用开通时绑定的角色  2：企业自己分配的角色
     */
    private Integer type;

    /**
     * 角色类型，1：基础角色，2：预置角色，3：自定义角色
     */
    private Integer roleType;
}
