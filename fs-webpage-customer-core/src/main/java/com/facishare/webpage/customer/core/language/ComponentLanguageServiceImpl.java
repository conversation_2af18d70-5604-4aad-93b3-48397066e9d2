package com.facishare.webpage.customer.core.language;

import com.facishare.qixin.i18n.QixinI18nService;
import com.facishare.qixin.i18n.model.Key;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.core.model.ComponentDto;
import com.facishare.webpage.customer.core.model.Widget;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR> Yu
 */
public class ComponentLanguageServiceImpl implements ComponentLanguageService {

    private static final String GROUP_I18N_KEY_PRE = "webpage.group";
    private static final String WIDGET_I18N_KEY_PRE = "webpage.widget";
    private static final String MENU_I18N_KEY_PRE = "webpage.menu";

    @Resource
    private QixinI18nService qixinI18nService;


    @Override
    public Map<String, String> queryComponentLanguage(int tenantId, List<ComponentDto> componentDtoList, Locale locale) {
        if (CollectionUtils.isEmpty(componentDtoList)) {
            return Maps.newHashMap();
        }
        List<Key> keyCollection = Lists.newArrayList();
        componentDtoList.stream().forEach(componentDto -> {
            if (StringUtils.isNotEmpty(componentDto.getTitleI18nKey())) {
                Key componentDtoKey = new Key(componentDto.getTitleI18nKey(), componentDto.getTitle());
                keyCollection.add(componentDtoKey);
            } else {
                String i18nKey = String.join("_", GROUP_I18N_KEY_PRE, componentDto.getId());
                Key componentDtoKey = new Key(i18nKey, componentDto.getTitle());
                keyCollection.add(componentDtoKey);
                componentDto.setTitleI18nKey(i18nKey);
            }

            Widget widget = componentDto.getWidget();
            if (widget != null) {
                if (StringUtils.isNotEmpty(widget.getNameI18nKey())) {
                    Key widgetKey = new Key(widget.getNameI18nKey(), widget.getName());
                    keyCollection.add(widgetKey);
                } else {
                    String i18nKey = String.join("_", WIDGET_I18N_KEY_PRE, widget.getId());
                    Key widgetKey = new Key(i18nKey, widget.getName());
                    keyCollection.add(widgetKey);
                    widget.setNameI18nKey(i18nKey);
                }
            }

            if (CollectionUtils.isNotEmpty(componentDto.getMenus())) {
                List<Menu> menus = componentDto.getMenus();
                menus.stream().forEach(menu -> {
                    if (StringUtils.isNotEmpty(menu.getNameI18nKey())) {
                        Key menuKey = new Key(menu.getNameI18nKey(), menu.getName());
                        keyCollection.add(menuKey);
                    } else {
                        String i18nKey = String.join("_", MENU_I18N_KEY_PRE, menu.getId());
                        Key menuKey = new Key(i18nKey, menu.getName());
                        keyCollection.add(menuKey);
                        menu.setNameI18nKey(i18nKey);
                    }
                });
            }
        });
        Map<String, String> i118Result = qixinI18nService.getMultiI18nValueDefault(tenantId, keyCollection, locale);
        return i118Result;
    }
}
