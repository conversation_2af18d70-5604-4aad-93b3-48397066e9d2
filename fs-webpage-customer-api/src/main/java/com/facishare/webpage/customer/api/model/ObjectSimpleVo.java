package com.facishare.webpage.customer.api.model;

import lombok.*;

import java.io.Serializable;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ObjectSimpleVo implements Serializable {
    private static final long serialVersionUID = -7640711336168346666L;
    private String apiName;
    private String ownerAllocateRule;

    private String label;
    private String masterObjApiName;
    private String originalDescribeApiName;

    private Boolean masterDetailObj = Boolean.FALSE;
    private boolean deleted;
    private boolean disabled;
}
