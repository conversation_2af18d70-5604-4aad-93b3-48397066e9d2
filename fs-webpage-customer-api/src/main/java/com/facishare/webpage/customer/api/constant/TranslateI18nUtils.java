package com.facishare.webpage.customer.api.constant;

import com.facishare.paas.I18N;
import com.facishare.webpage.customer.api.model.I18nTrans;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.*;

@Slf4j
public class TranslateI18nUtils {

    /**
     * 老版CRM菜单在翻译工作台的key前缀
     * crmMenuName. + 菜单id
     */
    public static final String CRM_MENU_NAME_PRE = "crmMenuName.";

    /**
     * App自定义页面里的菜单在翻译工作台的key前缀
     * appCustomerPageMenuName. + 页面id + .菜单apiName
     */
    public static final String APP_NAME = "appName.";
    public static final String APP_INTRODUCE = "appIntroduce.";
    public static final String TEMPLATE_NAME = "templateName.";
    public static final String TEMPLATE_WIDGET_NAME = "webTemplateWidgetsName.";
    public static final String TEMPLATE_TABS_NAME = "webTemplateTabsName.";
    public static final String LABEL_PAGE_NAME = "labelPageName.";
    public static final String TOOL_BAR_TOOL_NAME = "toolBar.name.";

    public static String getCrmMenuNametranslateKey(String menuId) {
        return TranslateI18nUtils.CRM_MENU_NAME_PRE + menuId;
    }

    public static String getAppNametranslateKey(String appId) {
        return TranslateI18nUtils.APP_NAME + appId;
    }

    public static I18nTrans.TransArg buildAppNameTranslateKey(String appIntroduce, String appId, List<String> preKeyList) {
        return I18nTrans.TransArg.builder()
                .name(appIntroduce)
                .customKey(getAppNametranslateKey(appId))
                .preKeyList(preKeyList)
                .build();
    }

    public static String getAppIntroduceTranslateKey(String appId) {
        return TranslateI18nUtils.APP_INTRODUCE + appId;
    }

    public static I18nTrans.TransArg buildAppIntroduceTranslateKey(String appIntroduce, String appId) {
        return I18nTrans.TransArg.builder()
                .name(appIntroduce)
                .customKey(getAppIntroduceTranslateKey(appId))
                .build();
    }

    public static I18nTrans.TransArg buildAppIntroduceTranslateKey(String appIntroduce, String appId, List<String> preKeyList) {
        return I18nTrans.TransArg.builder()
                .name(appIntroduce)
                .customKey(getAppIntroduceTranslateKey(appId))
                .preKeyList(preKeyList)
                .build();
    }

    public static String getTemplateNameKey(String pageTemplateId) {
        return TranslateI18nUtils.TEMPLATE_NAME + pageTemplateId;
    }

    public static String getWebTemplateWidgetsNameKey(String layoutId, String widgetId) {
        return TranslateI18nUtils.TEMPLATE_WIDGET_NAME + layoutId + widgetId;
    }

    public static String getWebTemplateTabsNameKey(String layoutId, String widgetId, String tabApiName) {
        return TranslateI18nUtils.TEMPLATE_TABS_NAME + layoutId + "." + widgetId + "." + tabApiName;
    }

    public static String getToolBarToolNameKey(String toolBarId, String toolApiName) {
        return TranslateI18nUtils.TOOL_BAR_TOOL_NAME + toolBarId + "." + toolApiName;
    }

    public static String getLabelPageNameKey(String layoutId, String labelIndex) {
        return TranslateI18nUtils.LABEL_PAGE_NAME + layoutId + "." + labelIndex;
    }

    public static String getWebMainTemplateKey(String apiName) {
        return "web.menu.template." + apiName;
    }

    public static String getWebAppViewGroupName(String menuId, String groupId) {
        return "crmMenu.group." + menuId + "." + groupId;
    }

    public static Map<String, String> getTransValueByKeys(Integer enterpriseId, Locale locale, List<String> keys) {
        Map<String, String> result = Maps.newHashMap();
        for (String key : keys) {
            result.put(key, getI18nValue(enterpriseId, key, locale.toLanguageTag(), null));
        }
        return result;
    }

    public static String getI18nValue(Integer tenantId, String key, String lang, String defaultValue) {
        try {
            Localization localization = I18nClient.getInstance().get(key, tenantId);
            if (Objects.isNull(localization)) {
                return defaultValue;
            }
            return localization.get(lang, defaultValue);
        } catch (Exception e) {
            log.error("getI18nValue error: tenantId:{},key:{},lang:{},defaultValue:{}", tenantId, key, lang, defaultValue, e);
        }
        return defaultValue;
    }

    public static String text(String key) {
        return getOrDefault(key, key);
    }

    public static String text(String key, Object... placeHolder) {
        return getOrDefault(key, key, placeHolder);
    }

    public static String getOrDefault(String key, String defaultValue) {
        try {
            String result = I18N.text(key);
            if (Strings.isNullOrEmpty(result)) {
                return defaultValue;
            }
            return result;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static String getOrDefault(String key, String defaultValue, Object... placeHolder) {
        try {
            String result = I18N.text(key, placeHolder);
            if (Strings.isNullOrEmpty(result)) {
                return MessageFormat.format(defaultValue, placeHolder);
            }
            return result;
        } catch (Exception e) {
            return MessageFormat.format(defaultValue, placeHolder);
        }
    }


    public static String getTransValueByOrder(List<String> preKeyList, String lang, Integer tenantId) {
        for (String key : preKeyList) {
            String value = getI18nValue(tenantId, key, lang, null);
            if (StringUtils.isNotEmpty(value)) {
                return value;
            }
        }
        return null;
    }

    public static Localization getAllTransValueByOrder(List<String> preKeyList, Integer tenantId) {
        Map<Byte, String> data = Maps.newHashMap();

        for (String key : preKeyList) {
            Localization localization = I18nClient.getInstance().get(key, tenantId);
            if (Objects.isNull(localization)) {
                continue;
            }
            localization.getData().forEach((k, value) -> {
                String dataMapValue = data.get(k);
                if (StringUtils.isBlank(dataMapValue)) {
                    data.put(k, value);
                }
            });
        }
        Localization localization = new Localization();
        localization.setData(data);
        return localization;
    }

    public static Localization getAllTransValueByOrderByKeyToLocalization(List<String> preKeyList, Integer tenantId, Map<String, Localization> keyToLocalization) {
        Map<Byte, String> data = Maps.newHashMap();
        Localization result = new Localization();
        result.setData(data);
        boolean localizationEmpty = Objects.isNull(keyToLocalization) || keyToLocalization.isEmpty();
        for (String key : preKeyList) {
            Localization localization;
            if (localizationEmpty) {
                localization = I18nClient.getInstance().get(key, tenantId);
            } else {
                localization = keyToLocalization.get(key);
            }
            if (Objects.isNull(localization)) {
                continue;
            }
            localization.getData().forEach(data::putIfAbsent);
        }
        return result;
    }

    public static Localization getAllTransValueByOrderByKeyToLocalizationV2(List<String> preKeyList, List<String> oldKeyList,
                                                                            Integer tenantId, Map<String, Localization> keyToLocalization) {
        Map<Byte, String> data = Maps.newHashMap();
        Localization result = new Localization();
        result.setData(data);
        mergeMultiLanguage(tenantId, oldKeyList, keyToLocalization, data);
        mergeMultiLanguage(0, preKeyList, null, data);
        return result;
    }

    private static void mergeMultiLanguage(Integer tenantId, List<String> keyList, Map<String, Localization> keyToLocalization, Map<Byte, String> data) {
        boolean localizationEmpty = Objects.isNull(keyToLocalization) || keyToLocalization.isEmpty();
        for (String key : keyList) {
            Localization localization;
            if (localizationEmpty) {
                //todo:替换元数据新方法
                localization = I18nClient.getInstance().get(key, tenantId);
            } else {
                localization = keyToLocalization.get(key);
            }
            if (Objects.isNull(localization)) {
                continue;
            }
            localization.getData().forEach(data::putIfAbsent);
        }
    }

    public static Localization getTransLocalizationByOrder(List<String> preKeyList, Integer tenantId) {
        for (String key : preKeyList) {
            if (Objects.nonNull(I18nClient.getInstance().get(key, tenantId))) {
                return I18nClient.getInstance().get(key, tenantId);
            }
        }
        return null;
    }

    public static String getCrmFrontPageKey(String layoutId) {
        return CRM_MENU_NAME_PRE + layoutId + ".frontPageName";
    }

    // 自定义web页面
    public static String getWebPageNameKey(String layoutApiName) {
        return "page." + layoutApiName + ".name";
    }

    public static String getWebPageDescriptionKey(String layoutApiName) {
        return "page." + layoutApiName + ".description";
    }

    public static I18nTrans.TransArg buildWebPageDescriptionTranslateKey(String description, String layoutApiName) {
        return I18nTrans.TransArg.builder()
                .name(description)
                .customKey(getWebPageDescriptionKey(layoutApiName))
                .build();
    }

    /**
     * {@link com.facishare.webpage.customer.api.constant.UIPaaSI18NKey#getUIPaasI18NValue}
     * {@link com.facishare.webpage.customer.api.constant.UIPaaSI18NKey#getLinkAppI18nValue}
     *
     * @param appId
     * @return
     */
    public static PreAndDefaultTranslateKey getAppNamePreTranslateKey(String appId) {
        String preTranslateKey = String.format(UIPaaSI18NKey.PaaS_App_Name, appId);
        return buildPreAndDefaultTranslateKey(preTranslateKey);
    }

    public static PreAndDefaultTranslateKey getTemplateNamePreTranslateKey(int tenantId, String id) {
        String key = String.format(UIPaaSI18NKey.PaaS_App_Template_Name, removeTenantIdById(tenantId, id));
        return buildPreAndDefaultTranslateKey(key);
    }

    private static PreAndDefaultTranslateKey buildPreAndDefaultTranslateKey(String preKey) {
        return PreAndDefaultTranslateKey.builder()
                .preTranslateKeys(Lists.newArrayList(preKey))
                .defaultTranslateKey(UIPaaSI18NKey.System_Default_PREFIX + preKey)
                .build();
    }

    public static Map<String, Localization> getLocalizationByKey(Integer tenantId, List<String> keyList) {
        try {
            log.info("get cache multiLang! tenantId:{},keyList:{}", tenantId, keyList);
            return I18nClient.getInstance().get(keyList, tenantId);
        } catch (Exception e) {
            log.error("get cache language error! tenantId:{},keyList:{}", tenantId, keyList);
            return Maps.newHashMap();
        }
    }

    public static String getTransValueByLocalization(Map<String, Localization> languageMap, String i18Key, String languageTag) {
        Localization translateInfo = languageMap.get(i18Key);
        if (Objects.nonNull(translateInfo) && StringUtils.isNotEmpty(translateInfo.get(languageTag, null))) {   // 区分country字段, 否则部分tw和cn
            return translateInfo.get(languageTag, null);
        }
        return null;
    }

    public static I18nTrans.TransArg formatTransKey(Integer tenantId, I18nTrans.TransArg transArg) {
        String customKey = transArg.getCustomKey();
        transArg.setOriginalCustomKey(customKey);
        List<String> newPreKeyList = formtKeyList(tenantId, customKey, transArg, transArg.getPreKeyList());
        transArg.setPreKeyList(newPreKeyList);
        return transArg;
    }


    public static I18nTrans.TransArg formatTransKeyV2(Integer tenantId, I18nTrans.TransArg transArg) {
        String customKey = transArg.getCustomKey();
        transArg.setOriginalCustomKey(customKey);
        List<String> keyList = formtKeyList(tenantId, customKey, transArg, transArg.getOldKeyList());
        transArg.setOldKeyList(keyList);
        return transArg;
    }

    private static List<String> formtKeyList(Integer tenantId, String customKey, I18nTrans.TransArg transArg, List<String> origianlKeyList) {
        List<String> keyList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(customKey) && (customKey.contains(tenantId + "_") || customKey.contains(tenantId + "-"))) {
            keyList.add(customKey);
            transArg.setCustomKey(customKey.replace(tenantId + "_", "").replace(tenantId + "-", ""));
        }
        if (CollectionUtils.isNotEmpty(origianlKeyList)) {
            for (String oldKey : origianlKeyList) {
                if (StringUtils.isEmpty(oldKey)) {
                    continue;
                }
                if (oldKey.contains(tenantId + "_") || oldKey.contains(tenantId + "-")) {
                    String newKey = oldKey.replace(tenantId + "_", "").replace(tenantId + "-", "");
                    keyList.add(newKey);
                }
                keyList.add(oldKey);
            }
        }
        return keyList;
    }

    public static String removeTenantIdById(int tenantId, String id) {
        return id.replace(tenantId + "_", "");
    }

    @Data
    @Builder
    public static class PreAndDefaultTranslateKey {
        private String defaultTranslateKey;
        private List<String> preTranslateKeys;

        public I18nTrans.TransArg convertToTransKeyInfo(String customKey) {
            return I18nTrans.TransArg.builder()
                    .preKeyList(preTranslateKeys)
                    .customKey(customKey)
                    .build();
        }

        public I18nTrans.TransArg convertToTransArg(String customKey, String name) {
            return I18nTrans.TransArg.builder()
                    .name(name)
                    .customKey(customKey)
                    .preKeyList(preTranslateKeys)
                    .build();
        }
    }

    public static I18nTrans.TransArg convertToTransArgWithoutEi(Integer ei, String keyWithTenantId, String value) {
        return convertToTransArgWithoutEi(ei, keyWithTenantId, Lists.newArrayList(), value);
    }

    public static I18nTrans.TransArg convertToTransArgWithoutEi(Integer ei, String customKey, List<String> preKeyList, String value) {
        I18nTrans.TransArg transKeyInfo = I18nTrans.TransArg.builder()
                .customKey(customKey)
                .preKeyList(preKeyList)
                .build();
        I18nTrans.TransArg newTransKeyInfo = formatTransKey(ei, transKeyInfo);

        return I18nTrans.TransArg.builder()
                .name(value)
                .customKey(newTransKeyInfo.getCustomKey())
                .preKeyList(newTransKeyInfo.getPreKeyList())
                .build();
    }

    public static I18nTrans.TransArg convertToTransArgWithoutEiV2(Integer ei, String customKey, List<String> preKeyList,
                                                                  List<String> oldKeyList, String value) {
        I18nTrans.TransArg transKeyInfo = I18nTrans.TransArg.builder()
                .customKey(customKey)
                .preKeyList(preKeyList)
                .oldKeyList(oldKeyList)
                .build();
        I18nTrans.TransArg newTransKeyInfo = formatTransKeyV2(ei, transKeyInfo);

        return I18nTrans.TransArg.builder()
                .name(value)
                .customKey(newTransKeyInfo.getCustomKey())
                .oldKeyList(oldKeyList)
                .preKeyList(newTransKeyInfo.getPreKeyList())
                .build();
    }


    public static I18nTrans.TransArg convertToTransArg(String customKey, String value) {
        return convertToTransArg(customKey, Lists.newArrayList(), value);
    }

    public static I18nTrans.TransArg convertToTransArg(String customKey, List<String> preKeyList, String value) {
        return I18nTrans.TransArg.builder()
                .name(value)
                .customKey(customKey)
                .preKeyList(preKeyList)
                .build();
    }


    public static I18nTrans.TransArg convertToTransArg(String customKey, List<String> oldKeyList, List<String> preKeyList, String value) {
        return I18nTrans.TransArg.builder()
                .name(value)
                .customKey(customKey)
                .oldKeyList(oldKeyList)
                .preKeyList(preKeyList)
                .build();
    }

    /*
     * @Description: 使用带ei的key构建TransArg, 之后需要统一迁移, 方便批量更换
     */
    public static I18nTrans.TransArg buildArgKeyWithEi(String ei, String key, String value) {
        return convertToTransArg(key, Lists.newArrayList(), value);
    }

    /*
     * @Description: 使用不带ei的key构建TransArg, 原来的项目中大量这种使用, 统一入口
     */
    public static I18nTrans.TransArg buildArgKeyWithoutEi(String ei, String key, String value) {
        return convertToTransArg(delEiInKey(ei, key), Lists.newArrayList(key), value);
    }

    public static String delEiInKey(String ei, String key) {
        if (StringUtils.isBlank(key) || StringUtils.isBlank(ei)) {
            return "";
        }
        return key.replace(ei + "_", "").replace(ei + "-", "");
    }

    /*
     * @Description: crm视图有key是: 67724_71574_109f1597efbc4396b639de316da34612, 这种ei都不知道是啥, 只能直接del
     */
    public static String delAllEi(String key) {
        if (StringUtils.isBlank(key)) {
            return "";
        }
        return Arrays.stream(key.split("_")).reduce((first, second) -> second).orElse("");
    }

}
