package com.facishare.webpage.customer.util;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


public enum ReferenceTargetType {

    FILE("file", "文件"),

    I18N("i18n", "多语");

    @Getter
    final String code;
    final String desc;

    ReferenceTargetType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<String> getCodes() {
        return Arrays.stream(values()).map(ReferenceTargetType::getCode).collect(Collectors.toList());
    }
}
