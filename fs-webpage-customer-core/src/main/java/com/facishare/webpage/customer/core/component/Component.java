package com.facishare.webpage.customer.core.component;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.core.model.DropListItem;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2020/4/1
 */
@Data
public abstract class Component implements Serializable {

    public abstract String getId();

    public abstract String getPId();

    public abstract String getName();

    public abstract String getDropItemType();

    public abstract String getApiName();

    public abstract String getType();

    public abstract int getLimit();

    public abstract int getGrayLimit();

    public abstract JSONObject getProps();

    public String getIcon(){
        return null;
    }

    public String getGroupType() {
        return null;
    }



    public void setCompFields(JSONObject ret) {

    }

    public JSONObject buildComponent() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("api_name", getApiName());
        jsonObject.put("type", getType());
        jsonObject.put("props", getProps());
        jsonObject.put("limit", getLimit());
        jsonObject.put("grayLimit", getGrayLimit());
        setCompFields(jsonObject);
        return jsonObject;
    }

    public DropListItem buildDropListItem(){
        DropListItem dropListItem = new DropListItem();
        dropListItem.setId(getId());
        dropListItem.setParentId(getPId());
        dropListItem.setName(getName());
        dropListItem.setType(getDropItemType());
        dropListItem.setComponent(buildComponent());
        dropListItem.setIcon(getIcon());
        dropListItem.setGroupType(getGroupType());

        return dropListItem;
    }

}
