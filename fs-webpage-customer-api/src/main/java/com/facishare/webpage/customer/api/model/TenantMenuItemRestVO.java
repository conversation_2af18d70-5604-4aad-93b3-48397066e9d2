package com.facishare.webpage.customer.api.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/14.
 */
@Data
public class TenantMenuItemRestVO implements Serializable {
    private String id;
    private String pid;
    private String appId;
    private String type;
    private String displayName;
    private String referenceApiname;
    private Boolean isHidden;
    private Integer number;
    private boolean canDelete = false;
    private List<String> searchWords;
    private List<String> preTranslateKeys;
    private List<String> oldTranslateKeys;
    private String translateKey;
}
