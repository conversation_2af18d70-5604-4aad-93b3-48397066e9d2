package com.facishare.webpage.customer.api.model.arg;

import com.facishare.webpage.customer.api.model.SimpleLinkAppRoleVO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class BathCreateOrUpdatelinkAppRoleArg implements Serializable {
    private static final long serialVersionUID = 1L;
    private List<SimpleLinkAppRoleVO> appRoleVOList = new ArrayList<>();
    private Integer type;



}
