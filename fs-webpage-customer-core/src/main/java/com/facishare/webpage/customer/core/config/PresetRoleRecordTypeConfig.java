package com.facishare.webpage.customer.core.config;

import com.facishare.webpage.customer.core.model.TenantEnvInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2024/12/4
 * @Description : 标记不可编辑的 互联应用的预置角色-对象业务类型
 */
@Component
@Slf4j
public class PresetRoleRecordTypeConfig {
    ObjectMapper objectMapper = new ObjectMapper();
    TypeReference<List<PresetRoleRecordType>> typeRef = new TypeReference<List<PresetRoleRecordType>>() {};
    public static List<PresetRoleRecordType> presetRoleRecordTypes = Lists.newArrayList();

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PresetRoleRecordType{
        private String appId;
        private String roleCode;
        private Map<String, List<String>> readOnlyRecordTypes;  // objApi -> recordTypes
        private TenantEnvInfo.ManualTenantInfo manualTenantInfo;  // 手动配置企业是否命中灰度
        private List<TenantEnvInfo> tenantEnvInfos; // 按照云环境配置的企业ei起止范围
    }

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-webpage-er-preset-role-record-type", iConfig -> {
            try {
                String json = iConfig.getString();
                presetRoleRecordTypes = objectMapper.readValue(json, typeRef);
                log.info("load PresetRoleRecordTypeConfig:{}", json);
            } catch (Exception e){
                log.warn("load PresetRoleRecordTypeConfig: error", e);
            }
        });
    }

    public List<String> getRolePresetObjRecordTypes(String ei, String appId, String roleId, String objectApiName) {
        if(StringUtils.isBlank(ei) || StringUtils.isBlank(appId) || StringUtils.isBlank(roleId) || StringUtils.isBlank(objectApiName)){
            return Lists.newArrayList();
        }

        PresetRoleRecordType presetRoleRecordType = presetRoleRecordTypes.stream()
                .filter( x -> roleId.equals(x.getRoleCode()) && appId.equals(x.getAppId()))
                .findFirst().orElse(null);
        if(Objects.isNull(presetRoleRecordType) || !TenantEnvInfo.isManualEiOrNewTenant(ei, presetRoleRecordType.getManualTenantInfo(), presetRoleRecordType.getTenantEnvInfos())){
            return Lists.newArrayList();
        }
        return presetRoleRecordType.getReadOnlyRecordTypes().getOrDefault(objectApiName, Lists.newArrayList());
    }
}
