package com.facishare.webpage.customer.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.qixin.common.monitor.SlowLog;
import com.facishare.webpage.customer.api.model.DataSourceEnv;
import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity;
import com.facishare.webpage.customer.model.PageData;
import com.facishare.webpage.customer.model.UserHomePageLayoutAO;

import java.util.List;
import java.util.Locale;

/**
 * Created by zhangyu on 2019/9/12
 */
public interface HomePageBaseService {

    HomePageLayoutTO getHomePageLayoutByLayoutId(int tenantId, Integer employeeId, OuterUserInfo outerUserInfo, String layoutId, String apiName, int appType, Locale locale, SlowLog stopWatch, boolean manager);

    HomePageLayoutEntity getCrmHomePageLayoutEntity(String layoutId);

    List<HomePageLayoutEntity> getOldCrmHomePageLayoutEntityList(String tenantId, List<Integer> layoutType, List<Integer> status, Locale locale, boolean isTrans);

    HomePageLayoutTO getHomePageLayoutByApiName(UserInfo userInfo, String layoutId, String apiName, int appType, ClientInfo clientInfo, boolean translateFlag);

    HomePageLayoutTO getUserHomePageLayoutByApiName(UserInfo userInfo, OuterUserInfo outerUserInfo, String layoutId, String apiName, int appType, Locale locale);

    HomePageLayoutTO insertHomePageLayout(String appId, int appType, int tenantId, int employeeId, String apiName, Integer applyType, HomePageLayoutTO homePageLayoutTO);

    HomePageLayoutTO updateHomePageLayout(int appType, String appId, int tenantId, int employeeId, String apiName, Integer applyType, HomePageLayoutTO homePageLayoutTO);

    List<HomePageLayoutTO> getHomePageLayoutList(int tenantId, int appType, String appId, Integer applyType, Locale locale);

    List<HomePageLayoutTO> getEmployeeHomePageLayoutList(int tenantId, int employeeId, int appType, String appId, Locale locale, Boolean enablePersonPageConfig);

    List<HomePageLayoutEntity> queryPreCustomPageListByConfig(int tenantId);

    List<HomePageLayoutTO> queryPreCustomPageListByConfigConvertDTO(int tenantId,Locale locale);

    long getPageCount(String appId, int tenantId, int layoutType, int employeeId);

    boolean deleteHomePageLayout(int tenantId, String layoutId, int employeeId);

    boolean makeHomePageFormal(String layoutId, int employeeId, String templeId, int appType);

    HomePageLayoutTO getHomePageLayoutByName(int tenantId, int employeeId, String appId, String name, int layoutType);

    boolean updateHomePageStatus(int tenantId, int appType, String layoutId, String apiName, int status, int employeeId);

    int getCreatorId(String layoutId, String apiName, int tenantId, int appType);

    List<HomePageLayoutEntity> copyHomePage(int fromTenantId, int toTenantId, int appType);

    int destroyHomePage(int tenantId);

    List<PageData> getPageData(DataSourceEnv env, int tenantId, String appId);

    void deleteHomePageLayouts(int tenantId, int appType, String appId);

    Boolean queryEnablePersonPageConfig(int tenantId);

    void updatePersonPageConfig(int tenantId, boolean value);

    /**
     * 获取启用的页面数据
     *
     * @param tenantId
     * @param appType
     * @param appId
     * @return
     */
    List<HomePageLayoutTO> queryEnableHomePageLayouts(int tenantId, int appType, String appId, int applyType);

    /**
     * 根据sourceId查询个人级页面数据
     *
     * @param enterpriseId
     * @param layoutId
     * @return
     */
    UserHomePageLayoutAO queryUserHomePageLayoutBySourceId(int enterpriseId, String layoutId);

    /**
     * 根据sourceId获取页面数据
     *
     * @param userInfo
     * @param outerUserInfo
     * @param layoutId
     * @param locale
     * @return
     */
    HomePageLayoutTO getUserHomePageLayoutBySourceId(UserInfo userInfo, OuterUserInfo outerUserInfo, String layoutId, Locale locale);

    /**
     * 保存个人级数据
     *
     * @param userInfo
     * @param userHomePageLayoutAO
     */
    String saveUserHomePageLayout(UserInfo userInfo, UserHomePageLayoutAO userHomePageLayoutAO);

    List<String> buildPreKeyList(JSONObject component, String tenantId, String layoutId, String source, String model);
}

