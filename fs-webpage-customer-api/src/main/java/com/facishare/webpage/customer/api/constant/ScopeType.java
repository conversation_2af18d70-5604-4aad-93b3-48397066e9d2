package com.facishare.webpage.customer.api.constant;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/10/31
 */
public enum ScopeType {
    /**
     * 员工
     */
    Employee(1, "E"),
    /**
     * 部门
     */
    Department(2, "D"),
    /**
     * 用户组
     */
    Group(3, "G"),
    /**
     * 角色
     */
    Role(4, "R"),
    /**
     * 互联角色（互联场景）
     */
    OutRole(5, "OR"),
    /**
     * 对接人（互联场景）
     */
    OutUId(6, "OU"),
    /**
     * 对接企业（互联场景）
     */
    OutTenant(7, "OT"),
    /**
     * 企业组（互联场景）
     */
    TenantGroup(8, "TG");

    private int type;

    private String value;

    ScopeType(int type, String value) {
        this.type = type;
        this.value = value;
    }

    public int getType() {
        return type;
    }

    public String getValue() {
        return value;
    }

    public static String getScopeValue(int type) {

        ScopeType[] values = ScopeType.values();

        for (ScopeType scopeType : values) {
            if (scopeType.type == type) {
                return scopeType.getValue();
            }
        }

        return "";
    }

    public static int getScopeType(String value) {
        ScopeType[] values = ScopeType.values();

        for (ScopeType scopeType : values) {
            if (scopeType.value.equals(value)) {
                return scopeType.getType();
            }
        }
        return 0;
    }
    public static ScopeType getScopeByType(int type) {
        ScopeType[] values = ScopeType.values();

        for (ScopeType scopeType : values) {
            if (scopeType.type == type) {
                return scopeType;
            }
        }

        return null;
    }

    public static void main(String[] args) {
        System.out.println(ScopeType.getScopeType("E"));
    }
}
