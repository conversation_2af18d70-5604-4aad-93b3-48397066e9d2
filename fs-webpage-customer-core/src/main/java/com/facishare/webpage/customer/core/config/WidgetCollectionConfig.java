package com.facishare.webpage.customer.core.config;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.core.model.WidgetCollection;
import com.github.autoconf.ConfigFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by she<PERSON> on 19/12/11.
 */
public class WidgetCollectionConfig {

    private Map<String, WidgetCollection> widgetCollections;

    public void init() {
        ConfigFactory.getInstance().getConfig("fs-webpage-customer-widgets-collection", iConfig -> {
            List<WidgetCollection> collections = JSONObject.parseArray(iConfig.getString(), WidgetCollection.class);
            widgetCollections = collections.stream().collect(Collectors.toMap(WidgetCollection::getId, Function.identity(), (key1, key2) -> key2));
        });
    }

    public WidgetCollection getWidgetCollection(String id) {
        if(StringUtils.isEmpty(id)){
            return null;
        }
        return widgetCollections.get(id);
    }

    public Optional<WidgetCollection> getWithDefineWidgets(String widgetId) {
        return widgetCollections.values().stream()
                .filter(it -> CollectionUtils.emptyIfNull(it.getWidgets()).contains(widgetId))
                .findFirst();
    }
}
