package com.facishare.webpage.customer.api.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Setter
@Getter
@ToString
@EqualsAndHashCode
public class OuterRoleVo implements Serializable {
    private static final long serialVersionUID = 7325315730494444537L;
    private String roleId;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 角色描述
     */
    private String description;
    /**
     * 角色类型
     */
    private Integer roleType;
    /**
     * 是否能编辑角色权限
     */
    private Boolean isModifiable;
    /**
     * 是否能分配人员
     */
    private Boolean isAssignable = false;
    /**
     * 是否能被删除
     */
    private Boolean isDeletable;

    public OuterRoleVo() {
    }

    public OuterRoleVo(String roleId, String roleName) {
        this.roleId = roleId;
        this.roleName = roleName;
    }

    public OuterRoleVo(String roleId, String roleName, Integer roleType) {
        this.roleId = roleId;
        this.roleName = roleName;
        this.roleType = roleType;
    }

    public OuterRoleVo(String roleId, String roleName, Integer roleType, Boolean isModifiable) {
        this.roleId = roleId;
        this.roleName = roleName;
        this.roleType = roleType;
        this.isModifiable = isModifiable;
    }
}
