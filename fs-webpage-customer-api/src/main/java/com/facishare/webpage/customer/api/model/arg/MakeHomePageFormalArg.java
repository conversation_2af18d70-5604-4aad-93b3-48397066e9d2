package com.facishare.webpage.customer.api.model.arg;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.google.common.base.Strings;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2019/9/29
 */
@Data
public class MakeHomePageFormalArg implements Serializable {

    private String layoutId;

    private int employeeId;

    private String templeId;

    private int appType;

    public void valid() {
        if (Strings.isNullOrEmpty(layoutId) || Strings.isNullOrEmpty(templeId) || employeeId < 0) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }

}
