package com.facishare.webpage.customer.core.util;

import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.api.model.core.TenantPrivilege;
import com.facishare.webpage.customer.core.model.ComponentDto;
import com.facishare.webpage.customer.core.model.Widget;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by zhangyu on 2020/6/4
 */
public class DropListUtil {

    public static String getDropListName(ComponentDto componentDto, Map<String, String> componentLanguage) {

        Widget widget = componentDto.getWidget();

        if (widget == null) {
            return StringUtils.isEmpty(componentLanguage.get(componentDto.getTitleI18nKey())) ? componentDto.getTitle() : componentLanguage.get(componentDto.getTitleI18nKey());
        } else {
            return StringUtils.isEmpty(componentLanguage.get(widget.getNameI18nKey())) ? widget.getName() : componentLanguage.get(widget.getNameI18nKey());
        }
    }

    public static Set<String> getBizConfKeys(List<ComponentDto> componentDtoList) {
        if (CollectionUtils.isEmpty(componentDtoList)) {
            return Sets.newHashSet();
        }
        Set<String> bizConfKeys = Sets.newHashSet();
        componentDtoList.forEach(x -> {
            Set<String> tenantPrivilegeBizConfKeys = getTenantPrivilegeBizConfKeys(x.getTenantPrivilege());
            bizConfKeys.addAll(tenantPrivilegeBizConfKeys);
            Widget widget = x.getWidget();
            if (widget != null) {
                bizConfKeys.addAll(getTenantPrivilegeBizConfKeys(widget.getTenantPrivilege()));
            }
            List<Menu> menus = x.getMenus();
            if (CollectionUtils.isNotEmpty(menus)) {
                menus.forEach(y -> bizConfKeys.addAll(getTenantPrivilegeBizConfKeys(y.getTenantPrivilege())));
            }
        });
        return bizConfKeys;
    }

    private static Set<String> getTenantPrivilegeBizConfKeys(TenantPrivilege tenantPrivilege) {
        if (tenantPrivilege == null) {
            return Sets.newHashSet();
        }
        if (CollectionUtils.isEmpty(tenantPrivilege.getBizConfKeys())) {
            return Sets.newHashSet();
        }
        return Sets.newHashSet(tenantPrivilege.getBizConfKeys());
    }

}
