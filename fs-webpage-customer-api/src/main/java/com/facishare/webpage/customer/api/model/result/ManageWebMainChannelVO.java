package com.facishare.webpage.customer.api.model.result;

import com.facishare.webpage.customer.api.model.MainChannelMenuVO;
import com.facishare.webpage.customer.api.model.Scope;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ManageWebMainChannelVO implements Serializable {

    /**
     * 主导航的apiName
     */
    private String apiName;
    /**
     * 主导航名称
     */
    private String name;
    /**
     * 适用范围
     */
    private List<Scope> scopeList;
    /**
     * 适用范围名称
     */
    private String scopeName;
    /**
     * 优先级,数字越大，优先级越高
     */
    private Integer priorityLevel;
    /**
     * 主导航的应用
     */
    private List<MainChannelMenuVO> mainChannelMenuVOList;
    /**
     * 是否显示应用名称
     */
    private Boolean showAppName;
    /**
     * 是否显示更多应用入口
     */
    private Boolean showMoreAppEntry;
    /**
     * 是否可以自定义
     */
    private Boolean canCustom;
    /**
     * 应用名称集合
     */
    private String appNames;
    /**
     * 版本
     */
    private Integer version;
    /**
     * 是否是系统预置
     */
    private String sourceType;
    /**
     * 创建人Id
     */
    private int creatorId;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Long createTime;


}