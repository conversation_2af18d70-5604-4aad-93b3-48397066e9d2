package com.facishare.webpage.customer.api.model.arg;

import com.facishare.webpage.customer.api.constant.ClientType;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by zhouwr on 2024/12/19.
 */
@Data
public class FindSiteConfigArg {
    private String upstreamEa;
    private String siteId;
    private String pageApiName;
    private String clientType = ClientType.web.getValue();


    public void validateAndFix() {
        clientType = StringUtils.firstNonBlank(clientType, ClientType.web.getValue());
    }
}
