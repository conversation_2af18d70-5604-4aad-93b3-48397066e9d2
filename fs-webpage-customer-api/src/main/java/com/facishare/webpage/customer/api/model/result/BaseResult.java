package com.facishare.webpage.customer.api.model.result;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/7/3.
 */
@Setter
@Getter
public class BaseResult implements Serializable {
    @JsonProperty("Result")
    public ResultInfo resultInfo;

    @Setter
    @Getter
    public static class ResultInfo implements Serializable {
        @JsonProperty("StatusCode")
        private int StatusCode;
        @JsonProperty("FailureCode")
        private int FailureCode;
        @JsonProperty("FailureMessage")
        private String FailureMessage;
    }

}
