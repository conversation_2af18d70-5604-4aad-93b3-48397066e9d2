package com.facishare.webpage.customer.api.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.webpage.customer.api.model.CommonlyUseMenuItem;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by zhangyi on 2019/12/19.
 */
@Data
public class QueryLicenseRestResult extends BaseResult {
    private List<String> versionAndPackages;
    private Set<String> licenseModule;
}
