package com.facishare.webpage.customer.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PageWidgetVO {
    private static final long serialVersionUID = -5443071370305004974L;
    private String layoutId;
    private String transLateKey;
    private List<String> preTransLateKeys;
    private String widgetName;
    private String widgetId;
    private String widgetType;
    private List<I18nInfo> i18nInfo;
}
