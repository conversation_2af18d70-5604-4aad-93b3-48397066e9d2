package com.facishare.webpage.customer.core.service.impl;

import com.facishare.paas.license.arg.JudgeModuleArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.common.Result;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.JudgeModulePojo;
import com.facishare.webpage.customer.core.service.UIPaasLicenseService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service("uIPaasLicenseService")
public class UIPaasLicenseServiceImpl implements UIPaasLicenseService {

    @Resource
    private LicenseClient licenseClient;

    private static final String  MULTI_LANGUAGE_APP ="multi_language_app";

    @Override
    public Map<String, Boolean> existModule(Integer tenantId, Set<String> productCodes) {

        if (Objects.isNull(tenantId) || org.apache.commons.collections4.CollectionUtils.isEmpty(productCodes)) {
            return Maps.newHashMap();
        }
        List<String> codes = Lists.newArrayList(productCodes);
        JudgeModuleArg arg = new JudgeModuleArg();
        arg.setContext(buildLicenseContext(tenantId));
        arg.setModuleCodes(codes);
        Result<JudgeModulePojo> result = licenseClient.judgeModule(arg);

        Map<String, Boolean> map = codes.stream().collect(Collectors.toMap(a -> a, b -> false));
        if (Objects.nonNull(result.getResult())) {
            org.apache.commons.collections4.CollectionUtils.emptyIfNull(result.getResult().getModuleFlags()).forEach(a -> map.put(a.getModuleCode(), a.isFlag()));
        }
        return map;
    }

    @Override
    public boolean existMultiLanguageModule(Integer tenantId) {
        Map<String, Boolean> multiLanguageApp = existModule(tenantId, Sets.newHashSet(MULTI_LANGUAGE_APP));
        return multiLanguageApp.get(MULTI_LANGUAGE_APP);
    }


    private LicenseContext buildLicenseContext(int tenantId) {
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(String.valueOf(tenantId));
        licenseContext.setUserId("-10000");
        return licenseContext;
    }

}
