package com.facishare.webpage.customer.api.model.core;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/12/18.
 */
@Data
public class PreObject implements Serializable{
    @JSONField(ordinal = 1)
    private String apiName;
    @JSONField(ordinal = 2)
    private Icon icon;
    @JSONField(ordinal = 3)
    private List<String> hideAddDeviceTypes = Lists.newArrayList();     //快速新建隐藏的端
    @JSONField(ordinal = 4)
    private boolean isShow = true;      //预置对象是否要在菜单侧展示
    @JSONField(ordinal = 5)
    private boolean hidden = false;    //预置对象菜单默认隐藏
    @JSONField(ordinal = 6)
    private Url grayUrl;        //灰度对象url
    @JSONField(ordinal = 7)
    private List<String> showDeviceTypes;     //对象隐藏的端

    @JSONField
    private Integer iconIndex;
}
