package com.facishare.webpage.customer.api.model.arg;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2019/11/19
 */
@Data
public class GetEmployeeConfigValueByApiNameArg implements Serializable {
    @SerializedName(value = "type", alternate = {"TYPE"})
    private int type;
    @SerializedName(value = "apiName", alternate = {"ApiName"})
    private String apiName;
    @SerializedName(value = "keys", alternate = {"Keys"})
    private List<Integer> keys;

    private int tenantId;
    private int userId;
}
