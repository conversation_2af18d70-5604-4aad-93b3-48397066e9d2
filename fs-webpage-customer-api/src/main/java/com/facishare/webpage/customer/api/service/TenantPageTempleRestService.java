package com.facishare.webpage.customer.api.service;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * Created by zhangyu on 2020/11/26
 */
@RestResource(
        value = "WebPageResource",
        desc = "应用视图",//ignoreI18n
        contentType = "application/json"
)
public interface TenantPageTempleRestService {

    @POST(value = "/webPage/tenantPageTempleRestService/checkUserPermission", desc = "")
    BaseApiResult checkUserPermission(@HeaderParam("x-fs-ei") String tenantId, @Body CheckUserPermissionApiArg apiArg);

    @POST(value = "/webPage/tenantPageTempleRestService/getPageTemplates", desc = "")
    GetPageTemplatesResult getPageTemplates(@HeaderParam("x-fs-ei") String tenantId, @Body GetPageTemplatesArg arg);

    @POST(value = "/webPage/tenantPageTempleRestService/checkUserPermissionV2", desc = "")
    CheckUserPermissionResult checkUserPermissionV2(@HeaderParam("x-fs-ei") String tenantId, @Body CheckUserPermissionApiArg arg);

    @POST(value = "/webPage/tenantPageTempleRestService/getPageTemplateById", desc = "")
    GetPageTemplateByIdRestResult getPageTemplateById(@HeaderParam("x-fs-ei") String tenantId, @Body GetPageTemplateByIdRestArg arg);

    @POST(value = "/webPage/tenantPageTempleRestService/getToolBarById")
    GetToolBarById.Result getToolBarById(@HeaderParam("x-fs-ei") String tenantId, @Body GetToolBarById.Arg arg);

    @POST(value = "/webPage/tenantPageTempleRestService/queryScopeList", desc = "")
    QueryScopeListRestResult queryScopeList(@HeaderParam("x-fs-ei") String tenantId, @Body QueryScopeListRestArg arg);

    @POST(value = "/webPage/tenantPageTempleRestService/updateScopeList",desc = "")
     UpdatePageTempleScopeListRestResult updateScopeList(@HeaderParam("x-fs-ei") String tenantId, @Body UpdatePagetempleScopeListRestArg arg);
    /**
     * 获取首页筛选器
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @POST(value = "/webPage/tenantPageTempleRestService/getPageTemplesByTypeAndAppIds", desc = "使用type和appIds查询应用视图信息")//ignoreI18n
    GetPageTemplesByTypeAndAppIdsResult getPageTemplesByTypeAndAppIds(@HeaderParam("x-fs-ei") String tenantId, @Body GetPageTemplesByTypeAndAppIdsArg arg);

    @POST(value = "/webPage/tenantPageTempleRestService/changeSetAfterAction", desc = "更改集复制后动作")//ignoreI18n
    PageTempleChangeSetAfterActionResult changeSetAfterAction(@HeaderParam("x-fs-ei") String tenantId, @Body PageTempleChangeSetAfterActionArg arg);


}
