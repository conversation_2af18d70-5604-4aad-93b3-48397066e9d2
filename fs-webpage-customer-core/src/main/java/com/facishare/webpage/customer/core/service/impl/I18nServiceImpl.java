package com.facishare.webpage.customer.core.service.impl;

import com.facishare.qixin.i18n.QixinI18nService;
import com.facishare.qixin.i18n.resource.OnTimeTranslateValueResource;
import com.facishare.qixin.i18n.resource.model.OnTimeTranslate;
import com.facishare.webpage.customer.api.constant.TranslateI18nUtils;
import com.facishare.webpage.customer.api.model.I18nTrans;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.core.business.ComponentListManager;
import com.facishare.webpage.customer.core.config.AllWidgetsConfig;
import com.facishare.webpage.customer.core.config.WidgetCollectionConfig;
import com.facishare.webpage.customer.core.model.Component;
import com.facishare.webpage.customer.core.model.Widget;
import com.facishare.webpage.customer.core.service.I18nService;
import com.facishare.webpage.customer.core.service.UIPaasLicenseService;
import com.facishare.webpage.customer.core.util.CollectionUtils;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service("i18nService")
public class I18nServiceImpl implements I18nService {
    private static final String MULTI_LANGUAGE_APP = "multi_language_app";

    @Resource
    private OnTimeTranslateValueResource onTimeTranslateValueResource;

    @Resource
    private UIPaasLicenseService uiPaasLicenseService;

    @Resource
    private QixinI18nService qixinI18nService;

    @Autowired
    private ComponentListManager componentListManager;

    @Autowired
    private WidgetCollectionConfig widgetCollectionConfig;

    @Autowired
    private AllWidgetsConfig allWidgetsConfig;

    @Override
    @Deprecated
    public String getCurrentI18nValue(Integer tenantId, String key, String defaultValue, boolean isOnTime, String lang, boolean isNeedValidateLicense) {
        if (StringUtils.isEmpty(key) || (isNeedValidateLicense && !uiPaasLicenseService.existMultiLanguageModule(tenantId))) {
            return defaultValue;
        }
        if (!isOnTime) {
            return TranslateI18nUtils.getI18nValue(tenantId, key, lang, defaultValue);
        }
        recordErrorKeyLog(tenantId, Lists.newArrayList(key));

        OnTimeTranslate.Arg arg = new OnTimeTranslate.Arg(key, String.valueOf(tenantId));
        OnTimeTranslate.Result onTimeTransValue;
        try {
            onTimeTransValue = onTimeTranslateValueResource.load(Lists.newArrayList(arg));
            if (WebPageGraySwitch.isAllowForEi(WebPageGraySwitch.PRINT_MULTI_LANG_LOG, tenantId)) {
                log.info("get onTime multiLang! tenantId:{},key:{},onTimeTransValue:{}", tenantId, key, onTimeTransValue);
            }
        } catch (Exception e) {
            log.error("get onTime trans value throw exception! tenantId:{},key:{}", tenantId, key, e);
            return TranslateI18nUtils.getI18nValue(tenantId, key, lang, defaultValue);
        }
        if (!"0".equals(onTimeTransValue.getErrorCode())) {
            log.warn("get onTime trans value fail! tenantId:{},key:{},result:{}", tenantId, key, onTimeTransValue);
            return TranslateI18nUtils.getI18nValue(tenantId, key, lang, defaultValue);
        }
        Map<String, OnTimeTranslate.TranslateInfo> result = Objects.isNull(onTimeTransValue.getResult()) ? Maps.newHashMap() : onTimeTransValue.getResult();
        OnTimeTranslate.TranslateInfo translateInfo = result.get(key);
        if (Objects.isNull(translateInfo)) {
            return defaultValue;
        }
        String transValue = translateInfo.toLocalization().get(lang, null);
        return StringUtils.isBlank(transValue) ? defaultValue : transValue;
    }

    private void recordErrorKeyLog(Integer tenantId, List<String> keys) {
        List<String> hasNullKeys = keys.stream()
                .filter(StringUtils::isNotBlank)
                .filter(x -> x.contains("null"))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(hasNullKeys)) {
            log.warn("tenantId:{},hasNullKeys:{}", tenantId, hasNullKeys);
        }
    }

    private Map<String, Localization> getOnTimeValueIncludeCache(Integer tenantId, List<String> keyList, boolean isNeedValidateLicense) {
        if (CollectionUtils.empty(keyList) || (isNeedValidateLicense && !uiPaasLicenseService.existMultiLanguageModule(tenantId))) {
            return Maps.newHashMap();
        }
        keyList = keyList.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        recordErrorKeyLog(tenantId, keyList);
        List<OnTimeTranslate.Arg> args = keyList.stream()
                .map(x -> new OnTimeTranslate.Arg(x, String.valueOf(tenantId)))
                .collect(Collectors.toList());
        OnTimeTranslate.Result onTimeTransValue;
        try {
            onTimeTransValue = onTimeTranslateValueResource.load(args);
            if (WebPageGraySwitch.isAllowForEi(WebPageGraySwitch.PRINT_MULTI_LANG_LOG, tenantId)) {
                log.info("get onTime multiLang! tenantId:{},key:{},onTimeTransValue:{}", tenantId, keyList, onTimeTransValue);
            }
        } catch (Exception e) {
            log.error("getOnTimeValueIncludeCache throw exception:tenantId:{},KeyList:{}", tenantId, keyList, e);
            return TranslateI18nUtils.getLocalizationByKey(tenantId, keyList);
        }
        if (!"0".equals(onTimeTransValue.getErrorCode())) {
            log.warn("getOnTimeValueIncludeCache Error:tenantId:{},KeyList:{},result:{}", tenantId, keyList, onTimeTransValue);
            return TranslateI18nUtils.getLocalizationByKey(tenantId, keyList);
        }
        Map<String, OnTimeTranslate.TranslateInfo> onTimeTransValueResult = onTimeTransValue.getResult();
        if (CollectionUtils.empty(onTimeTransValueResult)) {
            return TranslateI18nUtils.getLocalizationByKey(tenantId, keyList);
        }
        Map<String, Localization> result = Maps.newHashMap();
        onTimeTransValueResult.forEach((k, v) -> {
            if (Objects.isNull(v) || Objects.isNull(v.toLocalization())) {
                return;
            }
            result.put(k, v.toLocalization());
        });
        return result;
    }

    private Map<String, Localization> getOnTimeTransValue(Integer tenantId, List<String> keyList, boolean isNeedValidateLicense) {
        if (CollectionUtils.empty(keyList) || (isNeedValidateLicense && !uiPaasLicenseService.existMultiLanguageModule(tenantId))) {
            return Maps.newHashMap();
        }
        recordErrorKeyLog(tenantId, keyList);

        List<OnTimeTranslate.Arg> args = keyList.stream().filter(StringUtils::isNotEmpty)
                .map(x -> new OnTimeTranslate.Arg(x, String.valueOf(tenantId)))
                .collect(Collectors.toList());
        OnTimeTranslate.Result onTimeTransValue;
        try {
            onTimeTransValue = onTimeTranslateValueResource.load(args);
            if (WebPageGraySwitch.isAllowForEi(WebPageGraySwitch.PRINT_MULTI_LANG_LOG, tenantId)) {
                log.info("get onTime multiLang! tenantId:{},key:{},onTimeTransValue:{}", tenantId, keyList, onTimeTransValue);
            }
        } catch (Exception e) {
            log.error("getOnTimeTransValue throw exception:tenantId:{},KeyList:{}", tenantId, keyList, e);
            return Maps.newHashMap();
        }
        if (!"0".equals(onTimeTransValue.getErrorCode())) {
            log.warn("getOnTimeTransValue Error:tenantId:{},KeyList:{},result:{}", tenantId, keyList, onTimeTransValue);
            return Maps.newHashMap();
        }
        Map<String, OnTimeTranslate.TranslateInfo> onTimeTransValueResult = onTimeTransValue.getResult();
        if (CollectionUtils.empty(onTimeTransValueResult)) {
            return Maps.newHashMap();
        }
        Map<String, Localization> result = Maps.newHashMap();
        onTimeTransValueResult.forEach((k, v) -> {
            if (Objects.isNull(v) || Objects.isNull(v.toLocalization())) {
                return;
            }
            result.put(k, v.toLocalization());
        });
        return result;
    }

    public Map<String, Localization> getLocalizationByKey(Integer tenantId, List<String> keyList, boolean isNeedValidateLicense, boolean isOnTime) {
        if (CollectionUtils.empty(keyList) || (isNeedValidateLicense && !uiPaasLicenseService.existMultiLanguageModule(tenantId))) {
            return Maps.newHashMap();
        }
        if (!RequestContextManager.isFromRest() && (RequestContextManager.isFromManager() || isOnTime)) {
            return getTransValue(tenantId, keyList);
        }
        return TranslateI18nUtils.getLocalizationByKey(tenantId, keyList);
    }

    @Override
    public Map<String, Localization> getTransValue(Integer tenantId, List<String> keyList) {
        recordErrorKeyLog(tenantId, keyList);
        return getTransValue(tenantId, keyList, true);
    }

    private Map<String, Localization> getTransValue(Integer tenantId, List<String> keyList, boolean isNeedValidateLicense) {
        Map<String, Localization> result = Maps.newHashMap();
        if (CollectionUtils.empty(keyList) || (isNeedValidateLicense && !uiPaasLicenseService.existMultiLanguageModule(tenantId))) {
            return result;
        }
        if (RequestContextManager.isFromManager() && !RequestContextManager.isFromRest()) {
            return getOnTimeValueIncludeCache(tenantId, keyList, isNeedValidateLicense);
        }
        recordErrorKeyLog(tenantId, keyList);
        Map<String, Localization> localizationMap;
        try {
            localizationMap = TranslateI18nUtils.getLocalizationByKey(tenantId, keyList);
        } catch (Exception e) {
            log.error("I18nClient.getInstance().get() error! tenantId:{},keyList:{}", tenantId, keyList, e);
            localizationMap = Maps.newHashMap();
        }
        if (Objects.isNull(localizationMap)) {
            localizationMap = Maps.newHashMap();
        }
        Set<String> keys = Sets.newHashSet();
        localizationMap.forEach((k, v) -> {
            if (Objects.isNull(v)) {
                keys.add(k);
            }
        });
        keys.forEach(localizationMap::remove);
        result.putAll(localizationMap);
        return result;
    }

    @Override
    public Map<String, String> getTransValue(Integer tenantId, List<String> keyList, String lang) {
        return getTransValue(tenantId, keyList, lang, true);
    }

    public Map<String, String> getTransValue(Integer tenantId, List<String> keyList, String lang, boolean isNeedValidateLicense) {
        if (CollectionUtils.empty(keyList)) {
            return Maps.newHashMap();
        }
        Map<String, Localization> transValueMap = getTransValue(tenantId, keyList, isNeedValidateLicense);
        Map<String, String> result = Maps.newHashMap();
        keyList.forEach(x -> {
            Localization translateInfo = transValueMap.get(x);
            if (Objects.isNull(translateInfo)) {
                return;
            }
            String transValue = translateInfo.get(lang, null);
            if (StringUtils.isEmpty(transValue)) {
                return;
            }
            result.put(x, transValue);
        });
        return result;
    }

    @Deprecated
    @Override
    public Map<String, String> getOnTimeTransValue(Integer tenantId, List<String> keyList, String lang, boolean isOnTime, boolean isNeedValidateLicense) {
        if (CollectionUtils.empty(keyList) || (isNeedValidateLicense && !uiPaasLicenseService.existMultiLanguageModule(tenantId))) {
            return Maps.newHashMap();
        }
        Map<String, String> result = Maps.newHashMap();
        if (!isOnTime) {
            keyList.forEach(x -> {
                String value = TranslateI18nUtils.getI18nValue(tenantId, x, lang, null);
                if (StringUtils.isNotEmpty(value)) {
                    result.put(x, value);
                }
            });
            return result;
        }
        Map<String, Localization> onTimeTransValue = getOnTimeValueIncludeCache(tenantId, keyList, false);
        keyList.forEach(x -> {
            Localization localization = onTimeTransValue.get(x);
            if (Objects.isNull(localization)) {
                return;
            }
            String transValue = localization.get(lang, null);
            if (StringUtils.isNotEmpty(transValue)) {
                result.put(x, transValue);
            }
        });
        return result;
    }

    @Override
    public void syncTransValue(Integer tenantId, Map<String, String> keyToNewName, String lang) {
        try {
            List<Localization> localizations = buildSyncParam(tenantId, keyToNewName, lang);
            qixinI18nService.save4Translate(localizations, tenantId, false);
        } catch (Exception e) {
            log.error("syncTransValue error! tenantId:{},keyToNewName:{},lang:{}", tenantId, keyToNewName, lang, e);
        }
    }

    @Override
    public void syncTransValueIncludePreKey(Integer tenantId, List<I18nTrans.TransArg> transArg, String lang) {
        List<Localization> localizations = buildSyncParam(tenantId, transArg, lang);
        if (CollectionUtils.empty(localizations)) {
            return;
        }
        //同步多语
        syncTransValue(tenantId, localizations);
    }

    @Override
    public void syncTransValueIncludePreKeyV2(Integer tenantId, List<I18nTrans.TransArg> transArg, String lang) {
        List<Localization> localizations = buildSyncParamV2(tenantId, transArg, lang, true);

        if (CollectionUtils.empty(localizations)) {
            return;
        }
        //同步多语
        syncTransValue(tenantId, localizations);
    }

    @Override
    public List<Localization> buildSyncParam(Integer tenantId, List<I18nTrans.TransArg> transArgList, String lang) {
        return buildSyncParam(tenantId, transArgList, lang, true);
    }

    private void formatTransArg(List<I18nTrans.TransArg> transArgList) {
        if (CollectionUtils.empty(transArgList)) {
            return;
        }
        transArgList.removeIf(Objects::isNull);
        transArgList.removeIf(x -> StringUtils.isEmpty(x.getCustomKey()) || StringUtils.isEmpty(x.getName()));
        for (I18nTrans.TransArg transArg : transArgList) {
            transArg.setPreKeyList(CollectionUtils.nullToEmpty(transArg.getPreKeyList()).stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
        }
    }

    private void formatTransArgV2(List<I18nTrans.TransArg> transArgList) {
        if (CollectionUtils.empty(transArgList)) {
            return;
        }
        transArgList.removeIf(Objects::isNull);
        transArgList.removeIf(x -> StringUtils.isEmpty(x.getCustomKey()) || StringUtils.isEmpty(x.getName()));
        for (I18nTrans.TransArg transArg : transArgList) {
            transArg.setPreKeyList(CollectionUtils.nullToEmpty(transArg.getPreKeyList()).stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
            transArg.setOldKeyList(CollectionUtils.nullToEmpty(transArg.getOldKeyList()).stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
        }
    }

    @Override
    public List<Localization> buildSyncParam(Integer tenantId, List<I18nTrans.TransArg> transArgList, String lang, boolean isReplace) {
        if (CollectionUtils.empty(transArgList) || tenantId == null || StringUtils.isEmpty(lang)) {
            return Lists.newArrayList();
        }
        if (!uiPaasLicenseService.existMultiLanguageModule(tenantId)) {
            return Lists.newArrayList();
        }
        formatTransArg(transArgList);
        if (isReplace) {
            transArgList = transArgList.stream().map(x -> TranslateI18nUtils.formatTransKey(tenantId, x)).collect(Collectors.toList());
        }

        Set<String> keyList = Sets.newHashSet();
        transArgList.forEach(x -> keyList.add(x.getCustomKey())); // 翻译工作台key
        transArgList.stream()
                .filter(x -> CollectionUtils.notEmpty(x.getPreKeyList()))
                .forEach(x -> keyList.addAll(x.getPreKeyList().stream().filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toSet()))); // 翻译工作台key

        if (CollectionUtils.empty(keyList)) {
            return Lists.newArrayList();
        }
        Map<String, Localization> keyToLocalization = getOnTimeTransValue(tenantId, Lists.newArrayList(keyList), false);    // 同步逻辑在后台, 实时没关系
        List<Localization> localizations = Lists.newArrayList();
        for (I18nTrans.TransArg transArg : transArgList) {
            String customKey = transArg.getCustomKey();
            Localization customLocalization = keyToLocalization.get(customKey);
            //有自定义翻译的时候，只要不一样，不管当前预设多语是否有值，都会同步
            if (Objects.nonNull(customLocalization)) {
                String customValue = customLocalization.get(lang, null);
                if (!StringUtils.equals(customValue, transArg.getName())) {
                    localizations.add(buildLocalization(tenantId, customKey, transArg.getName(), lang, Lists.newArrayList("server")));
                    continue;
                }
            }
            //没有自定义翻译时，取预设多语，当前value与预设多语不一致时，则往自定义多语上同步value
            Localization localization = TranslateI18nUtils.getAllTransValueByOrderByKeyToLocalization(transArg.getPreKeyList(), tenantId, keyToLocalization);
            String preTransValue = localization.get(lang, null);
            if (CollectionUtils.notEmpty(localization.getData()) && CollectionUtils.notEmpty(localization.getData().values()) && !StringUtils.equals(preTransValue, transArg.getName())) {
                Map<Byte, String> newData = Maps.newHashMap(localization.getData());
                Localization localizationParam = buildLocalization(tenantId, customKey, transArg.getName(), lang, Lists.newArrayList("server"));
                localizationParam.setData(newData);
                localizationParam.set(lang, transArg.getName());
                localizations.add(localizationParam);
            }
        }
        return localizations;
    }


    @Override
    public List<Localization> buildSyncParamV2(Integer tenantId, List<I18nTrans.TransArg> transArgList, String lang, boolean isReplace) {
        if (!WebPageGraySwitch.isAllowForEi(WebPageGraySwitch.getTransValueByOldKeyGrayEi, tenantId)) {
            transArgList.forEach(x -> x.getPreKeyList().addAll(0,
                    x.getOldKeyList().stream().filter(oldKey -> !x.getPreKeyList().contains(oldKey)).collect(Collectors.toList())));
            return buildSyncParam(tenantId, transArgList, lang, isReplace);
        }
        if (CollectionUtils.empty(transArgList) || tenantId == null || StringUtils.isEmpty(lang)) {
            return Lists.newArrayList();
        }
        if (!uiPaasLicenseService.existMultiLanguageModule(tenantId)) {
            return Lists.newArrayList();
        }
        formatTransArgV2(transArgList);
        if (isReplace) {
            transArgList = transArgList.stream().map(x -> TranslateI18nUtils.formatTransKeyV2(tenantId, x)).collect(Collectors.toList());
        }

        Set<String> keyList = Sets.newHashSet();
        transArgList.forEach(x -> keyList.add(x.getCustomKey())); // 翻译工作台key
        transArgList.forEach(x -> keyList.addAll(x.getOldKeyList())); // 翻译工作台key

        if (CollectionUtils.empty(keyList)) {
            return Lists.newArrayList();
        }
        Map<String, Localization> keyToLocalization = getOnTimeTransValue(tenantId, Lists.newArrayList(keyList), false);    // 同步逻辑在后台, 实时没关系
        List<Localization> localizations = Lists.newArrayList();
        for (I18nTrans.TransArg transArg : transArgList) {
            String customKey = transArg.getCustomKey();
            Localization customLocalization = keyToLocalization.get(customKey);
            //有自定义翻译的时候，只要不一样，不管当前预设多语是否有值，都会同步
            if (Objects.nonNull(customLocalization)) {
                String customValue = customLocalization.get(lang, null);
                if (!StringUtils.equals(customValue, transArg.getName())) {
                    localizations.add(buildLocalization(tenantId, customKey, transArg.getName(), lang, Lists.newArrayList("server")));
                }
            } else {
                // 没有自定义翻译时，取预设多语，当前value与预设多语不一致时，则往自定义多语上同步value
                Localization localization = TranslateI18nUtils.getAllTransValueByOrderByKeyToLocalizationV2(transArg.getPreKeyList(), transArg.getOldKeyList(),
                        tenantId, keyToLocalization);
                String preTransValue = localization.get(lang, null);
                if (CollectionUtils.notEmpty(localization.getData())
                        && CollectionUtils.notEmpty(localization.getData().values())
                        && !StringUtils.equals(preTransValue, transArg.getName())) {
                    Map<Byte, String> newData = Maps.newHashMap(localization.getData());
                    Localization localizationParam = buildLocalization(tenantId, customKey, transArg.getName(), lang, Lists.newArrayList("server"));
                    localizationParam.setData(newData);
                    localizationParam.set(lang, transArg.getName());
                    localizations.add(localizationParam);
                }
            }
        }
        return localizations;
    }

    public List<Localization> buildSyncParam(Integer tenantId, Map<String, String> keyToNewName, String lang) {
        if (CollectionUtils.empty(keyToNewName)) {
            return Lists.newArrayList();
        }
        Map<String, Boolean> multiLanguageApp = uiPaasLicenseService.existModule(tenantId, Sets.newHashSet(MULTI_LANGUAGE_APP));
        if (!multiLanguageApp.get(MULTI_LANGUAGE_APP)) {    // 没有多语license
            return Lists.newArrayList();
        }
        Set<String> keys = keyToNewName.keySet();
        Map<String, Localization> onTimeTransValue = getOnTimeTransValue(tenantId, Lists.newArrayList(keys), false);
        List<Localization> localizations = Lists.newArrayList();
        keyToNewName.forEach((key, value) -> {
            Localization translateInfo = onTimeTransValue.get(key);
            if (Objects.isNull(translateInfo)) {
                return;
            }
            String transValue = translateInfo.get(lang, null);
            if (!StringUtils.equals(transValue, value)) {
                localizations.add(buildLocalization(tenantId, key, value, lang, Lists.newArrayList("server")));
            }
        });

        return localizations;
    }

    public void syncTransValue(Integer tenantId, List<Localization> localizations) {
        try {
            I18nClient.getInstance().save4Translate(tenantId, localizations, false);
        } catch (Exception e) {
            log.error("I18nClient.getInstance().save4Translate error! tenantId:{},localizations:{}", tenantId, localizations, e);
        }
    }

    /**
     * 菜单组件 走这个方法
     *
     * @param tenantId
     * @param transArgList
     * @param lang
     * @return
     */
    @Override
    public Map<String, String> getTransValueIncludePreKey(Integer tenantId, List<I18nTrans.TransArg> transArgList, String lang) {
        if (CollectionUtils.empty(transArgList) || tenantId == null || StringUtils.isEmpty(lang)) {
            return Maps.newHashMap();
        }
        formatTransArg(transArgList);
        transArgList = transArgList.stream().map(x -> TranslateI18nUtils.formatTransKey(tenantId, x)).collect(Collectors.toList());
        List<String> customKeyList = transArgList.stream().map(I18nTrans.TransArg::getCustomKey).collect(Collectors.toList());
        Map<String, String> customTransValueMap;
        if (RequestContextManager.isFromManager() && !RequestContextManager.isFromRest()) {
            customTransValueMap = getTransValue(tenantId, customKeyList, lang);
        } else {
            customTransValueMap = TranslateI18nUtils.getTransValueByKeys(tenantId, Locale.forLanguageTag(lang), customKeyList);
        }

        Map<String, String> result = Maps.newHashMap();
        for (I18nTrans.TransArg transArg : transArgList) {
            String customValue = customTransValueMap.get(transArg.getCustomKey());
            if (StringUtils.isNotEmpty(customValue)) {
                result.put(transArg.getOriginalOrCustomKey(), customValue);
                continue;
            }
            String realName = getRealNameByPreKey(tenantId, transArg.getPreKeyList(), transArg.getName(), lang);
            result.put(transArg.getOriginalOrCustomKey(), realName);
        }
        return result;
    }


    @Override
    public Map<String, String> getTransValueIncludePreKeyV2(Integer tenantId, List<I18nTrans.TransArg> transArgList, String lang) {
        Map<String, String> result = Maps.newHashMap();
        if (CollectionUtils.empty(transArgList) || tenantId == null || StringUtils.isEmpty(lang)) {
            return result;
        }
        if (!WebPageGraySwitch.isAllowForEi(WebPageGraySwitch.getTransValueByOldKeyGrayEi, tenantId)) {
            transArgList.forEach(x -> x.getPreKeyList().addAll(0,
                    x.getOldKeyList().stream().filter(oldKey -> !x.getPreKeyList().contains(oldKey)).collect(Collectors.toList())));
            return getTransValueIncludePreKey(tenantId, transArgList, lang);
        }

        formatTransArgV2(transArgList);
        transArgList = transArgList.stream().map(x -> TranslateI18nUtils.formatTransKeyV2(tenantId, x)).collect(Collectors.toList());
        List<String> customKeyList = transArgList.stream().map(I18nTrans.TransArg::getCustomKey).collect(Collectors.toList());
        Map<String, String> customTransValueMap;
        //todo 确认翻译工作台走着方法是否会有问题（晚上刷数据||导出）
        if (RequestContextManager.isFromManager() && !RequestContextManager.isFromRest()) {
            customTransValueMap = getTransValue(tenantId, customKeyList, lang);
        } else {
            customTransValueMap = TranslateI18nUtils.getTransValueByKeys(tenantId, Locale.forLanguageTag(lang), customKeyList);
        }

        for (I18nTrans.TransArg transArg : transArgList) {
            String customValue = customTransValueMap.get(transArg.getCustomKey());
            if (StringUtils.isNotEmpty(customValue)) {
                result.put(transArg.getOriginalOrCustomKey(), customValue);
                continue;
            }
            String oldKeyValue = I18nClient.getInstance().getFirstTenantCustomTranslationByOrder(transArg.getOldKeyList(), tenantId, lang);
            if (StringUtils.isNotEmpty(oldKeyValue)) {
                result.put(transArg.getOriginalOrCustomKey(), oldKeyValue);
                continue;
            }
            String realName = getRealNameByPreKey(0, transArg.getPreKeyList(), transArg.getName(), lang);
            result.put(transArg.getOriginalOrCustomKey(), realName);
        }
        return result;
    }

    private String getRealNameByPreKey(Integer tenantId, List<String> preKeyList, String name, String lang) {
        Localization allTransValue = TranslateI18nUtils.getAllTransValueByOrder(preKeyList, tenantId);
        // 如果存在则代表没有改过
        if (allTransValue.getData().containsValue(name)) {
            return allTransValue.get(lang, name);
        } else {
            return name;
        }
    }

    @Override
    public Map<String, String> batchGetRealNameByNoneLicense(Integer tenantId, List<I18nTrans.TransArg> transArgList, String lang) {
        if (CollectionUtils.empty(transArgList) || tenantId == null || StringUtils.isEmpty(lang)) {
            return null;
        }
        formatTransArg(transArgList);
        Map<String, String> result = Maps.newHashMap();

        for (I18nTrans.TransArg transArg : transArgList) {
            String realName = getRealNameByPreKey(0, transArg.getPreKeyList(), transArg.getName(), lang);
            result.put(transArg.getCustomKey(), realName);
        }
        return result;
    }

    private Localization buildLocalization(Integer tenantId, String key, String value, String
            lang, List<String> tags) {
        Localization localization = new Localization();
        localization.set(lang, value);
        localization.setKey(key);
        localization.setTenantId(tenantId);
        localization.setTags(tags);
        return localization;
    }

    @Override
    public Map<String, String> getWebComponentPreTranslate(Integer tenantId, String appId, String lang) {
        Map result = Maps.newHashMap();
        List<Component> componentList = componentListManager.getComponentListByAppId(appId);

        List<String> i18nKeys = Lists.newLinkedList();
        List<String> collectionComponentIds = Lists.newLinkedList();
        componentList.forEach(componentDto -> {
            if (widgetCollectionConfig.getWidgetCollection(componentDto.getCollectionId()) != null) {
                collectionComponentIds.addAll(widgetCollectionConfig.getWidgetCollection(componentDto.getCollectionId()).getWidgets());
            }
        });

        List<Widget> collectionWidgets = allWidgetsConfig.getWidgetsByIds(collectionComponentIds);
        collectionWidgets.forEach(widget -> {
            i18nKeys.add(widget.getNameI18nKey());
        });

        Map<String, String> translateResultMap = getTransValue(tenantId, i18nKeys, lang);

        componentList.forEach(componentDto -> {
            String translateValue = new String();
            if (translateResultMap.containsKey(componentDto.getTitleI18nKey())) {
                translateValue = translateResultMap.get(componentDto.getTitleI18nKey());
            }
            result.put(componentDto.getId(), translateValue);
        });

        collectionWidgets.forEach(widget -> {
            String translateValue = new String();
            if (translateResultMap.containsKey(widget.getNameI18nKey())) {
                translateValue = translateResultMap.get(widget.getNameI18nKey());
            }
            result.put(widget.getId(), translateValue);
        });
        return result;
    }

    @Override
    public Map<String, String> getAppComponentPreTranslate(Integer tenantId, String appId, String lang) {
        Map<String, String> result = Maps.newHashMap();
        List<Component> componentList = componentListManager.getComponentListByAppId(appId);

        List<String> i18nKeys = Lists.newLinkedList();
        List<String> collectionComponentIds = Lists.newLinkedList();
        componentList.forEach(componentDto -> {
            if (widgetCollectionConfig.getWidgetCollection(componentDto.getCollectionId()) != null) {
                collectionComponentIds.addAll(widgetCollectionConfig.getWidgetCollection(componentDto.getCollectionId()).getWidgets());
            }
        });

        List<Widget> collectionWidgets = allWidgetsConfig.getWidgetsByIds(collectionComponentIds);
        collectionWidgets.forEach(widget -> {
            i18nKeys.add(widget.getNameI18nKey());
        });

        Map<String, String> translateResultMap = getTransValue(tenantId, i18nKeys, lang);

        componentList.forEach(componentDto -> {
            String translateValue = new String();
            if (translateResultMap.containsKey(componentDto.getTitleI18nKey())) {
                translateValue = translateResultMap.get(componentDto.getTitleI18nKey());
            }
            result.put(componentDto.getId(), translateValue);
        });

        collectionWidgets.forEach(widget -> {
            String translateValue = new String();
            if (translateResultMap.containsKey(widget.getNameI18nKey())) {
                translateValue = translateResultMap.get(widget.getNameI18nKey());
            }
            result.put(widget.getId(), translateValue);
        });
        return result;
    }
}


