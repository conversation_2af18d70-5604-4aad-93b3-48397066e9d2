package com.facishare.webpage.customer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.uc.api.model.enterprise.EnterpriseInfoDto;
import com.facishare.uc.api.service.EnterpriseRemoteService;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.*;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.*;
import com.facishare.webpage.customer.api.model.arg.CreateLinkAppArg;
import com.facishare.webpage.customer.api.model.arg.GetlinkAppListArg;
import com.facishare.webpage.customer.api.model.arg.UpdateLinkAppArg;
import com.facishare.webpage.customer.api.model.result.GetLinkAppListResult;
import com.facishare.webpage.customer.api.model.result.GetUpLinkAppListResult;
import com.facishare.webpage.customer.api.utils.I18NKey;
import com.facishare.webpage.customer.api.utils.IconType;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.common.OrganizationCommonService;
import com.facishare.webpage.customer.config.DefaultTenantConfig;
import com.facishare.webpage.customer.config.PresetLinkAppConfig;
import com.facishare.webpage.customer.constant.AppTypeEnum;
import com.facishare.webpage.customer.constant.ComponentConstant;
import com.facishare.webpage.customer.controller.model.result.portal.SiteInfoVO;
import com.facishare.webpage.customer.core.service.I18nService;
import com.facishare.webpage.customer.core.service.UIPaasLicenseService;
import com.facishare.webpage.customer.core.util.ScopesUtil;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.facishare.webpage.customer.dao.PaaSAppDao;
import com.facishare.webpage.customer.dao.entity.PaaSAppEntity;
import com.facishare.webpage.customer.remote.CrossAppCacheService;
import com.facishare.webpage.customer.remote.TempFileToFormalFile;
import com.facishare.webpage.customer.service.*;
import com.fxiaoke.enterpriserelation2.arg.*;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.data.LinkAppPartialData;
import com.fxiaoke.enterpriserelation2.result.*;
import com.fxiaoke.enterpriserelation2.service.AppOuterRoleService;
import com.fxiaoke.enterpriserelation2.service.DownstreamService;
import com.fxiaoke.enterpriserelation2.service.UpstreamService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.lang.Void;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/11/12
 */
@Slf4j
@Component
public class LinkAppServiceImpl implements LinkAppService {
    @Autowired
    private PaaSAppService paaSAppService;
    @Resource
    private TenantMenuService tenantMenuService;
    @Resource
    private HomePageBaseService homePageBaseService;
    @Autowired
    private com.fxiaoke.enterpriserelation2.service.LinkAppService linkAppService;
    @Resource
    private TenantPageTempleBaseService tenantPageTempleBaseService;

    @Autowired
    private UpstreamService upstreamService;

    @Resource
    private TempFileToFormalFile tempFileToFormalFile;

    @Autowired
    private AppOuterRoleService appOuterRoleService;

    @Autowired
    private DownstreamService downstreamService;

    @Autowired
    private RemoteCrossService remoteCrossService;
    @Autowired
    private LicenseClient licenseClient;

    @Resource
    private OrganizationCommonService organizationCommonService;

    @Autowired
    private EnterpriseRemoteService enterpriseRemoteService;

    @Resource
    private EIEAConverter eieaConverter;

    @Autowired
    private DefaultTenantConfig defaultTenantConfig;

    @Autowired
    private PresetLinkAppConfig presetLinkAppConfig;

    @Autowired
    private PaaSAppDao paaSAppDao;

    @Autowired
    private CrossAppCacheService crossAppCacheService;

    @Autowired
    private LicenseManagerService licenseManagerService;

    @Autowired
    private I18nService i18nService;

    @Autowired
    private UIPaasLicenseService uiPaasLicenseService;

    @Autowired
    private SiteService siteService;

    private static final String WECHAT_SERVICE_URL = "https://{FXIAOKE_DOMAIN}/fs-er-biz/er/auth/connect?authType=4&linkType=1&fsAppId=${fs_appId}&resourceUrl=https%3A%2F%2F{FXIAOKE_DOMAIN}%2Fopen%2Fwechatconnect%2F&_hash=/&";


    private static final String CUSTOMER_LINK_APP_WEB_URL = "#iframeapp/%s/%s";

    private static final String CUSTOMER_LINK_APP_WEB_MANAGER_URL = "#paasapp/index/=/appId_%s";

    private static final String CUSTOMER_LINK_APP_APP_URL = "ava://uipaas_custom/pages/crosscustom/index?{\"upstreamEa\":\"%s\", \"appId\":\"%s\", \"fsAppId\":\"%s\"}";

    @Override
    public GetLinkAppListResult getLinkAppList(int enterpriseId, String enterpriseAccount, int employeeId, Locale locale) {
        GetLinkAppListResult result = new GetLinkAppListResult();
        if (WebPageGraySwitch.isAllowSynLinkAppGrayEaByShuxian(enterpriseAccount)) {
            newGetLinkAppListResult(enterpriseId, locale, result);
        } else {
            oldGetLinkAppListResult(enterpriseId, locale, result, employeeId);
        }
        return result;
    }

    private void oldGetLinkAppListResult(int enterpriseId, Locale locale, GetLinkAppListResult result, int employeeId) {
        List<LinkAppAuthVo> linkAppAuthVoList = getLinkAppAuthVos(enterpriseId, employeeId);
        if (CollectionUtils.isEmpty(linkAppAuthVoList)) {
            result.setCustomerLinkAppList(new ArrayList<>());
            result.setLinkAppList(new ArrayList<>());
            return;
        }
        //查询paas侧的互联应用列表，用于改名换图标
        GetlinkAppListArg arg = new GetlinkAppListArg();
        arg.setTenantId(enterpriseId);
        arg.setType(1);
        Map<String, PaaSAppVO> paasLinkAppMap = new HashMap<>();
        List<PaaSAppVO> paasLinkAppList = paaSAppService.getLinkAppVOList(arg, locale);
        paasLinkAppMap = paasLinkAppList.stream().collect(Collectors.toMap(PaaSAppVO::getAppId, x -> x));
        //过滤出预置互联应用
        List<LinkAppAuthVo> preLinkAppList = linkAppAuthVoList.stream().filter(x -> x.getLinkApp().getType() == Constant.LINK_APP_TYPE_PRE).collect(Collectors.toList());

        List<LinkAppVO> linkAppVOList = convert2linkAppVO(preLinkAppList, paasLinkAppMap);
        result.setLinkAppList(linkAppVOList);
        //过滤出自定义互联应用
        List<LinkAppAuthVo> customerlinkAppVos = linkAppAuthVoList.stream().filter(x -> x.getLinkApp().getType() == Constant.LINK_APP_TYPE_CUSTOMER).collect(Collectors.toList());
        List<String> paasAppIds = customerlinkAppVos.stream().map(x -> x.getLinkApp().getId()).collect(Collectors.toList());
        List<PaaSAppVO> paaSAppVOList = paaSAppService.getPaaSAppByAppIds(enterpriseId, paasAppIds, null, locale, true);
        result.setCustomerLinkAppList(paaSAppVOList);
    }


    private void newGetLinkAppListResult(int enterpriseId, Locale locale, GetLinkAppListResult result) {
        //查询paas侧的互联应用列表，用于改名换图标
        GetlinkAppListArg arg = new GetlinkAppListArg();
        arg.setTenantId(enterpriseId);
        List<PaaSAppVO> paasLinkAppList = paaSAppService.getLinkAppVOList(arg, locale);
        //过滤出预置互联应用
        List<PaaSAppVO> preLinkAppList = paasLinkAppList.stream().filter(x -> x.getAppType() == AppTypeEnum.LINK_APP.getAppType()).collect(Collectors.toList());
        List<String> preLinkAppIds = preLinkAppList.stream().map(PaaSAppVO::getAppId).collect(Collectors.toList());
        Map<String, LinkAppPartialData> map = remoteCrossService.getLinkAppPartialData(enterpriseId, preLinkAppIds);
        List<LinkAppVO> linkAppVOList = convertPaasApp2LinkApp(preLinkAppList, map);
        result.setLinkAppList(linkAppVOList);
        //过滤出自定义互联应用
        List<PaaSAppVO> customerlinkAppVos = paasLinkAppList.stream().filter(x -> x.getAppType() == AppTypeEnum.CUSTOMER_LINK_APP.getAppType() && x.getStatus() != PaaSAppStatus.DELETE && StringUtils.isNotBlank(x.getName())).collect(Collectors.toList());
        result.setCustomerLinkAppList(customerlinkAppVos);
    }

    private List<LinkAppVO> convertPaasApp2LinkApp(List<PaaSAppVO> preLinkAppList, Map<String, LinkAppPartialData> map) {
        List<LinkAppVO> linkAppVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(preLinkAppList)) {
            return linkAppVOList;
        }
        List<Integer> tenantIds = preLinkAppList.stream().map(PaaSAppVO::getTenantId).collect(Collectors.toList());
        Map<Integer, String> eieaMap = eieaConverter.enterpriseIdToAccount(tenantIds);
        List<String> preLinkAppIds = preLinkAppList.stream().map(PaaSAppVO::getAppId).collect(Collectors.toList());
        Map<String, Integer> availableTimeMap = licenseManagerService.batchGetLinkAppAvailableTime(eieaMap.getOrDefault(tenantIds.get(0), ""), preLinkAppIds);
        Map<String, SimpleLinkAppVO> preMap = new HashMap<>();
        List<SimpleLinkAppVO> presetLinkAppList = presetLinkAppConfig.getLinkAppVOListByLinkAppIds(preLinkAppIds);
        if (CollectionUtils.isNotEmpty(presetLinkAppList)) {
            preMap = presetLinkAppList.stream().collect(Collectors.toMap(SimpleLinkAppVO::getId, x -> x));
        }
        Map<String, SimpleLinkAppVO> finalPreMap = preMap;
        preLinkAppList.forEach(paaSAppVO -> {
            LinkAppVO linkAppVO = new LinkAppVO();
            SimpleLinkAppVO simpleLinkAppVO = finalPreMap.get(paaSAppVO.getAppId());
            LinkAppPartialData linkAppPartialData = map.get(paaSAppVO.getAppId());
            linkAppVO.setId(paaSAppVO.getAppId());
            linkAppVO.setName(paaSAppVO.getName());
            linkAppVO.setNameI18nKey(SourceType.SYSTEM.equals(paaSAppVO.getSourceType()) ? simpleLinkAppVO.getNameI18nKey() : "");
            linkAppVO.setIcon(paaSAppVO.getIcon());
            linkAppVO.setAppIcon(paaSAppVO.getIcon());
            linkAppVO.setManagerIcon(paaSAppVO.getIcon());
            linkAppVO.setIntroduction(paaSAppVO.getDescription());
            linkAppVO.setIntroductionI18nKey(null == simpleLinkAppVO ? "" : simpleLinkAppVO.getIntroductionI18nKey());
            linkAppVO.setAssociationCrmObject(null == simpleLinkAppVO ? true : simpleLinkAppVO.getAssociationCrmObject());
            linkAppVO.setWebUrl(SourceType.SYSTEM.equals(paaSAppVO.getSourceType()) ? simpleLinkAppVO.getWebUrl() : "");
            linkAppVO.setAppUrl(SourceType.SYSTEM.equals(paaSAppVO.getSourceType()) ? simpleLinkAppVO.getAppUrl() : "");
            linkAppVO.setWebManagerUrl(SourceType.SYSTEM.equals(paaSAppVO.getSourceType()) ? simpleLinkAppVO.getWebManagerUrl() : "");
            linkAppVO.setEnablePrompt(SourceType.SYSTEM.equals(paaSAppVO.getSourceType()) ? simpleLinkAppVO.getEnablePrompt() : "");
            linkAppVO.setDisablePrompt(SourceType.SYSTEM.equals(paaSAppVO.getSourceType()) ? simpleLinkAppVO.getDisablePrompt() : "");
            linkAppVO.setSupportExtendObject(SourceType.SYSTEM.equals(paaSAppVO.getSourceType()) ? simpleLinkAppVO.getSupportExtendObject() : true);
            linkAppVO.setOpenValidatePostUrl(!SourceType.SYSTEM.equals(paaSAppVO.getSourceType()) ? simpleLinkAppVO.getOpenValidatePostUrl() : "");
            linkAppVO.setShowPaasPage(simpleLinkAppVO.getShowPaasPage());
            linkAppVO.setType(AppTypeEnum.LINK_APP.getAppType() == paaSAppVO.getAppType() ? 1 : 2);
            linkAppVO.setStatus(paaSAppVO.getStatus());
            linkAppVO.setCreateTime(null == paaSAppVO.getCreateTime() ? new Date().getTime() : paaSAppVO.getCreateTime());
            linkAppVO.setUpdateTime(null == paaSAppVO.getUpdateTime() ? new Date().getTime() : paaSAppVO.getUpdateTime());
            linkAppVO.setHasLinkAppAdmin(null == linkAppPartialData ? false : linkAppPartialData.getHasLinkAppAdmin());
            linkAppVO.setDataAuthType(null == linkAppPartialData ? null : linkAppPartialData.getDataAuthType());
            linkAppVO.setSupportNoFsAccount(null == linkAppPartialData ? false : linkAppPartialData.getSupportNoFsAccount());
            linkAppVO.setIsExpired(null == linkAppPartialData ? false : linkAppPartialData.getIsExpired());
            linkAppVO.setAuthedRoleNum(CollectionUtils.isNotEmpty(paaSAppVO.getScopeListForCross()) ? paaSAppVO.getScopeListForCross().size() : 0);
            linkAppVO.setAvailableTime(availableTimeMap.get(paaSAppVO.getAppId()));
            //重新赋值url
            linkAppVO.setWebManagerUrl(urlReplaceUpstreamEa(linkAppVO.getWebManagerUrl(), eieaMap.getOrDefault(paaSAppVO.getTenantId(), ""), linkAppVO.getId()));
            linkAppVO.setWebUrl(urlReplaceUpstreamEa(linkAppVO.getWebUrl(), eieaMap.getOrDefault(paaSAppVO.getTenantId(), ""), linkAppVO.getId()));
            linkAppVO.setAppUrl(urlReplaceUpstreamEa(linkAppVO.getAppUrl(), eieaMap.getOrDefault(paaSAppVO.getTenantId(), ""), linkAppVO.getId()));
            linkAppVO.setAppManagerUrl(urlReplaceUpstreamEa(linkAppVO.getAppManagerUrl(), eieaMap.getOrDefault(paaSAppVO.getTenantId(), ""), linkAppVO.getId()));

            linkAppVOList.add(linkAppVO);
        });
        return linkAppVOList;
    }

    private String urlReplaceUpstreamEa(String url, String upsteamEa, String appId) {
        if (StringUtils.isBlank(url) || StringUtils.isBlank(upsteamEa)) {
            return url;
        }
        String placeHolderStr = "${fs_upstream_ea}";
        String placeHolderAppIdStr = "${fs_appId}";
        String placeHolderAppIdEncodeStr = "${fs_appId_encode}";
        String placeHolderAppIdEncode2Str = "${fs_appId_encode_2}";
        String newUrl = url
                .replace(placeHolderStr, upsteamEa)
                .replace(placeHolderAppIdStr, appId)
                .replace(placeHolderAppIdEncodeStr, urlComponentEncode(appId))
                .replace(placeHolderAppIdEncode2Str, urlComponentEncode(urlComponentEncode(appId)));
        return newUrl;
    }

    public String urlComponentEncode(Object obj) {
        try {
            if (obj instanceof String) {
                return URLEncoder.encode((String) obj, "UTF-8");
            } else {
                return URLEncoder.encode(JsonUtil.toJson(obj), "UTF-8");
            }
        } catch (UnsupportedEncodingException use) {
            log.warn("Unsupport exception:{}", use);
            return null;
        }
    }

    private List<LinkAppAuthVo> getLinkAppAuthVos(int enterpriseId, int employeeId) {
        //查询互联应用
        HeaderObj headerObj = HeaderObj.newInstance(enterpriseId);
        UpstreamTenantIdOutArg listOpenedAppsOutArg = new UpstreamTenantIdOutArg();
        listOpenedAppsOutArg.setUpstreamTenantId(enterpriseId);
        listOpenedAppsOutArg.setFsUserIds(employeeId);
        log.info("upstreamService.listEnterpriseLinkApps.header={},arg={}", JSONObject.toJSONString(headerObj), JSONObject.toJSONString(listOpenedAppsOutArg));
        List<LinkAppAuthVo> linkAppAuthVoList = upstreamService.listEnterpriseLinkApps(headerObj, listOpenedAppsOutArg).getData();
        log.info("upstreamService.listEnterpriseLinkApps.linkAppAuthVoList={},header={},arg={}", JSONObject.toJSONString(linkAppAuthVoList), JSONObject.toJSONString(headerObj), JSONObject.toJSONString(listOpenedAppsOutArg));
        if (CollectionUtils.isEmpty(linkAppAuthVoList)) {
            return new ArrayList<>();
        }
        return linkAppAuthVoList;
    }

    private List<LinkAppVO> convert2linkAppVO(List<LinkAppAuthVo> preLinkAppList, Map<String, PaaSAppVO> paasLinkAppMap) {
        List<LinkAppVO> linkAppVOList = new ArrayList<>();
        preLinkAppList.forEach(item -> {
            LinkAppVO linkAppVO = new LinkAppVO();
            BeanUtils.copyProperties(item.getLinkApp(), linkAppVO);
            linkAppVO.setHasLinkAppAdmin(item.getHasLinkAppAdmin());
            linkAppVO.setAuthedRoleNum(item.getAuthedRoleNum());
            if (null != paasLinkAppMap && null != paasLinkAppMap.get(linkAppVO.getId())) {
                PaaSAppVO paaSAppVO = paasLinkAppMap.get(item.getLinkApp().getId());
                linkAppVO.setIcon(paaSAppVO.getIcon());
                linkAppVO.setName(paaSAppVO.getName());
            }
            linkAppVOList.add(linkAppVO);
        });
        return linkAppVOList;
    }

    @Override
    public String createLinkApp(String enterpriseAccount, Integer enterpriseId, int employeeId, CreateLinkAppArg arg) {
        String fullPath = arg.getIcon();
        String uploadIcontype = null;
        if (Objects.nonNull(arg.getUploadIcon())) {
            arg.setIcon(arg.getUploadIcon().getPath());
            uploadIcontype = arg.getUploadIcon().getType();
            String aPathIcon = tempFileToFormalFile.tnPathToCPath(enterpriseAccount, employeeId, arg.getIcon(), uploadIcontype);
            arg.setIcon(aPathIcon);
            fullPath = String.format(defaultTenantConfig.getImgFullPathRule(), aPathIcon);
        }
        //先调用互联接口保存互联应用，并返回appId
        HeaderObj headerObj = HeaderObj.newInstance(enterpriseId);
        com.fxiaoke.enterpriserelation2.arg.CreateLinkAppArg createLinkAppArg = buildCreateLinkAppArg(arg, enterpriseAccount, fullPath, enterpriseId);
        log.info("linkAppService.createOrUpdateLinkApp.header={},arg={}", JSONObject.toJSONString(headerObj), JSONObject.toJSONString(createLinkAppArg));
        RestResult<String> restResult = linkAppService.createOrUpdateLinkApp(headerObj, createLinkAppArg);
        log.info("linkAppService.createOrUpdateLinkApp.restResult={},header={},arg={}", JSONObject.toJSONString(restResult), JSONObject.toJSONString(headerObj), JSONObject.toJSONString(createLinkAppArg));
        String appId = restResult.getData();

        //拼装paaSAppVO
        PaaSAppVO paaSAppVO = PaaSAppVO.builder().appId(appId).appType(AppTypeEnum.CUSTOMER_LINK_APP.getAppType()).name(arg.getName()).description(arg.getDescription()).scopeList(arg.getScopeList()).iconIndex(arg.getIconIndex()).icon(arg.getIcon()).uploadIconType(uploadIcontype).build();
        List<ScopeForCross> scopeForCrossList = arg.getScopeList().stream().map(x -> {
            ScopeForCross scopeForCross = new ScopeForCross();
            scopeForCross.setRoleId(x.getDataId());
            scopeForCross.setRoleType(2);
            scopeForCross.setUpdateTime(new Date().getTime());
            return scopeForCross;
        }).collect(Collectors.toList());
        paaSAppVO.setScopeListForCross(scopeForCrossList);
        //保存paas应用 TODO 等互联应用迁移完成后 就去掉写操作，完全依赖深研接口调用
        List<PaaSAppEntity> customerLinkAppList = paaSAppDao.getCustomerLinkAppsByAppIds(Lists.newArrayList(paaSAppVO.getAppId()));
        if (CollectionUtils.isEmpty(customerLinkAppList)) {
            paaSAppService.savePaaSApp(enterpriseId, employeeId, paaSAppVO);
        }
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        //为本企业开通自定义互联应用
        createEnterpriseLinkAppRelations(enterpriseAccount, enterpriseId, employeeId, appId);
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        //应用角色同步
        synRoles(enterpriseAccount, enterpriseId, appId, paaSAppVO.getScopeList());

        updateLinkAppUrl(enterpriseAccount, enterpriseId, employeeId, appId);
        paaSAppService.updateStatus(enterpriseId, employeeId, appId, PaaSStatus.disable);
        return appId;
    }

    /**
     * 为本企业 开通 自定义互联应用
     */
    private void createEnterpriseLinkAppRelations(String enterpriseAccount, Integer enterpriseId, int employeeId, String appId) {
        HeaderObj headerObj = HeaderObj.newInstance(enterpriseId);
        EnterpriseAppRelationArg enterpriseAppRelationArg = new EnterpriseAppRelationArg();
        enterpriseAppRelationArg.setLinkAppIds(Lists.newArrayList(appId));
        enterpriseAppRelationArg.setUpstreamEa(enterpriseAccount);
        enterpriseAppRelationArg.setOperatorFsUserId(employeeId);
        //启用=1 ，停用=2
        enterpriseAppRelationArg.setStatus(1);
        log.info("upstreamService.createEnterpriseLinkAppRelations.header={},arg={}", JSONObject.toJSONString(headerObj), JSONObject.toJSONString(enterpriseAppRelationArg));
        RestResult<String> restResult1 = upstreamService.createEnterpriseLinkAppRelations(headerObj, enterpriseAppRelationArg);
        log.info("upstreamService.createEnterpriseLinkAppRelations.restResult={},header={},arg={}", JSONObject.toJSONString(restResult1), JSONObject.toJSONString(headerObj), JSONObject.toJSONString(enterpriseAppRelationArg));
    }

    private com.fxiaoke.enterpriserelation2.arg.CreateLinkAppArg buildCreateLinkAppArg(CreateLinkAppArg arg, String enterpriseAccount, String fullPath, Integer enterpriseId) {
        com.fxiaoke.enterpriserelation2.arg.CreateLinkAppArg createLinkAppArg = new com.fxiaoke.enterpriserelation2.arg.CreateLinkAppArg();
        createLinkAppArg.setName(arg.getName());
        String icon = StringUtils.isNotBlank(fullPath) ? fullPath : arg.getIcon();
        createLinkAppArg.setIcon(icon);
        createLinkAppArg.setManagerIcon(icon);
        createLinkAppArg.setAppIcon(icon);
        createLinkAppArg.setAppSessionIcon(icon);
        createLinkAppArg.setType(Constant.LINK_APP_TYPE_CUSTOMER);
        //0 不展示paas入口， 1展示所有paas入口，2应用视图(web), 3应用视图(app),
        createLinkAppArg.setShowPaasPage(1);
        //应用内部使用权限提示
        createLinkAppArg.setInternalVisibleRangePrompt(I18N.text(I18NKey.INTERNAL_USE_ONLY_PERMISSION_PROMPT));
        //是否默认启用（1-是，2-否）
        createLinkAppArg.setEnabledByDefault(1);
        //应用停用提示
        createLinkAppArg.setDisablePrompt(I18N.text(I18NKey.NOTIFICATION_OF_APPLICATION_DEACTIVATION));
        //应用启用提示
        createLinkAppArg.setEnablePrompt(I18N.text(I18NKey.TRANSLATION_APP_ACTIVATED));
        //应用简介
        createLinkAppArg.setIntroduction(arg.getDescription());
        //不能设置管理员
        createLinkAppArg.setUnableSetManagerText(I18N.text(I18NKey.CANNOT_SET_THE_ADMINISTRATOR));
        //hasLinkAppAdmin为否时提示：没有权限。为true时跳转到本企业互联应用


        EnterpriseInfoDto.FindEIRequest findEIRequest = new EnterpriseInfoDto.FindEIRequest();
        findEIRequest.setEnterpriseId(enterpriseId);
        EnterpriseInfoDto.FindEIResult enterpriseInfo = enterpriseRemoteService.findEnterpriseByEI(findEIRequest);
        String domain = "\\{FXIAOKE_DOMAIN}";
        String wechatServiceUrl = WECHAT_SERVICE_URL
                .replaceAll(domain, enterpriseInfo.getEnterpriseInfo().getDomain().replaceAll("https://",""));
        createLinkAppArg.setWechatServiceUrl(wechatServiceUrl);

        createLinkAppArg.setWebManagerUrl(String.format(CUSTOMER_LINK_APP_WEB_MANAGER_URL, "appId"));
        //web端主导航url
        createLinkAppArg.setWebUrl(String.format(CUSTOMER_LINK_APP_WEB_URL, "appId", enterpriseAccount));

        //移动端用户态主导航/移动端应用列表 url
        createLinkAppArg.setAppUrl(String.format(CUSTOMER_LINK_APP_APP_URL, enterpriseAccount, "appId", "appId"));
        //
        createLinkAppArg.setAppManagerUrl(String.format(CUSTOMER_LINK_APP_APP_URL, enterpriseAccount, "appId", "appId"));
        return createLinkAppArg;


    }

    private void synRoles(String enterpriseAccount, Integer enterpriseId, String appId, List<Scope> scopeList) {
        //角色使用互联角色
        List<RoleInfo> roleInfoList = remoteCrossService.getOuterRoleInfosByAppId(enterpriseId, appId);
        List<String> oldScopeList = roleInfoList.stream().map(x -> x.getId()).collect(Collectors.toList());
        List<String> newScopeList = scopeList.stream().map(x -> x.getDataId()).collect(Collectors.toList());
        List<String> addScopeList = newScopeList.stream().filter(e -> {
            return !oldScopeList.contains(e);
        }).collect(Collectors.toList());

        List<String> deleteScopeList = oldScopeList.stream().filter(e -> {
            return !newScopeList.contains(e);
        }).collect(Collectors.toList());
        HeaderObj headerObj = HeaderObj.newInstance(enterpriseId);

        if (CollectionUtils.isNotEmpty(addScopeList)) {
            BatchAddAppRolesWithEaArg arg = new BatchAddAppRolesWithEaArg();
            arg.setAppId(appId);
            //角色类型，1：基础角色，2：预置角色',
            arg.setAppRoleType(2);
            arg.setFsEa(enterpriseAccount);
            List<BatchAddAppRolesWithEaArg.RoleVo> roleAddList = new ArrayList<>();
            addScopeList.forEach(x -> {
                BatchAddAppRolesWithEaArg.RoleVo roleVo = new BatchAddAppRolesWithEaArg().new RoleVo();
                roleVo.setRoleId(x);
                roleVo.setRoleType(2);
                roleAddList.add(roleVo);
            });
            arg.setRoleAddList(roleAddList);
            log.info("appOuterRoleService.batchAddAppRolesWithEa.header={},arg={}", JSONObject.toJSONString(headerObj), JSONObject.toJSONString(arg));
            RestResult<Void> result = appOuterRoleService.batchAddAppRolesWithEa(headerObj, arg);
            log.info("appOuterRoleService.batchAddAppRolesWithEa.result={},header={},arg={}", JSONObject.toJSONString(result), JSONObject.toJSONString(headerObj), JSONObject.toJSONString(arg));
        }
        if (CollectionUtils.isNotEmpty(deleteScopeList)) {
            BatchDeleteRoleAppsArg arg = new BatchDeleteRoleAppsArg();
            arg.setAppIds(Lists.newArrayList(appId));
            //角色类型，1：基础角色，2：预置角色',
            arg.setFsEa(enterpriseAccount);
            deleteScopeList.parallelStream().forEach(x -> {
                arg.setOuterRoleId(x);
                log.info("appOuterRoleService.batchDeleteRoleApps.header={},arg={}",
                        JSONObject.toJSONString(headerObj), JSONObject.toJSONString(arg));
                RestResult<Void> result = appOuterRoleService.batchDeleteRoleApps(headerObj, arg);
                log.info("appOuterRoleService.batchDeleteRoleApps.result={},header={},arg={}",
                        JSONObject.toJSONString(result), JSONObject.toJSONString(headerObj), JSONObject.toJSONString(arg));
            });
        }
    }

    @Override
    public void updateLinkApp(String enterpriseAccount, Integer enterpriseId, int employeeId, UpdateLinkAppArg arg, Locale locale) {
        //更新link应用
        String fullPath = arg.getIcon();
        String uploadIcontype = null;
        if (Objects.nonNull(arg.getUploadIcon())) {
            arg.setIcon(arg.getUploadIcon().getPath());
            uploadIcontype = arg.getUploadIcon().getType();
            String aPathIcon = tempFileToFormalFile.tnPathToCPath(enterpriseAccount, employeeId, arg.getIcon(), uploadIcontype);
            arg.setIcon(aPathIcon);
        }
        SimpleLinkAppVO simpleLinkAppVO = presetLinkAppConfig.getLinkAppVOByLinkAppId(arg.getAppId());
        List<ScopeForCross> scopeForCrossList = arg.getScopeList().stream().map(x -> {
            ScopeForCross scopeForCross = new ScopeForCross();
            scopeForCross.setRoleId(x.getDataId());
            scopeForCross.setRoleType(2);
            scopeForCross.setUpdateTime(new Date().getTime());
            return scopeForCross;
        }).collect(Collectors.toList());
        PaaSAppVO paaSAppVO = PaaSAppVO.builder()
                .appId(arg.getAppId())
                .appType(null == simpleLinkAppVO ? AppTypeEnum.CUSTOMER_LINK_APP.getAppType() : AppTypeEnum.LINK_APP.getAppType())
                .name(arg.getName())
                .description(arg.getDescription())
                .scopeList(arg.getScopeList())
                .scopeListForCross(scopeForCrossList)
                .iconIndex(arg.getIconIndex())
                .icon(arg.getIcon())
                .uploadIconType(uploadIcontype)
                .webUrl("#iframeapp/" + arg.getAppId() + "/" + enterpriseAccount)
                .webManagerUrl("#iframeapp/" + arg.getAppId() + "/" + enterpriseAccount)
                .useUserPageTempleFlag(arg.isUseUserPageTempleFlag())
                .customIcon(arg.getCustomIcon())
                .build();

        //调用互联接口更新自定义互联应用
        HeaderObj headerObj = HeaderObj.newInstance(enterpriseId);
        if (paaSAppVO.getAppType() == AppTypeEnum.CUSTOMER_LINK_APP.getAppType()) {
            //paas侧更新互联应用
            paaSAppService.updatePaaSApp(enterpriseId, employeeId, paaSAppVO, locale);
            updatelinkApp(enterpriseAccount, employeeId, arg, fullPath, simpleLinkAppVO, headerObj);
        }
        synRoles(enterpriseAccount, enterpriseId, arg.getAppId(), paaSAppVO.getScopeList());

        if (paaSAppVO.getAppType() == AppTypeEnum.LINK_APP.getAppType()) {
            //更新互联应用管理员
            UpdateEmployeeLinkAppRolesArg updateEmployeeLinkAppRolesArg = new UpdateEmployeeLinkAppRolesArg();
            updateEmployeeLinkAppRolesArg.setLinkAppIds(Lists.newArrayList(arg.getAppId()));
            if (CollectionUtils.isNotEmpty(arg.getAdmins())) {
                List<Integer> admins = arg.getAdmins().stream().map(x -> Integer.valueOf(x.getDataId())).collect(Collectors.toList());
                updateEmployeeLinkAppRolesArg.setFsUserIds(admins);
                updateEmployeeLinkAppRolesArg.setRole(1);
                updateEmployeeLinkAppRolesArg.setFsUserId(employeeId);
                updateEmployeeLinkAppRolesArg.setUpstreamEa(enterpriseAccount);
                log.info("upstreamService.updateEmployeeLinkAppRoles.header={},arg={}", JSONObject.toJSONString(headerObj), JSONObject.toJSONString(updateEmployeeLinkAppRolesArg));
                RestResult<String> result1 = upstreamService.updateEmployeeLinkAppRoles(headerObj, updateEmployeeLinkAppRolesArg);
                log.info("upstreamService.updateEmployeeLinkAppRoles.result={},header={},arg={}", JSONObject.toJSONString(result1), JSONObject.toJSONString(headerObj), JSONObject.toJSONString(updateEmployeeLinkAppRolesArg));
            }

        }
        //更新名称和图标
        PaaSAppEntity paaSAppEntity = new PaaSAppEntity();
        paaSAppEntity.setTenantId(enterpriseId);
        paaSAppEntity.setAppId(arg.getAppId());
        paaSAppEntity.setName(arg.getName());
        paaSAppEntity.setDescription(arg.getDescription());
        paaSAppEntity.setIconIndex(arg.getIconIndex());
        paaSAppEntity.setIcon(StringUtils.isNotBlank(fullPath) ? fullPath : arg.getIcon());
        paaSAppEntity.setCustomIcon(arg.getCustomIcon());
        if (null != arg.getUploadIcon()) {
            paaSAppEntity.setUploadImgType(arg.getUploadIcon().getType());
            paaSAppEntity.setIconIndex(null);
        }
        paaSAppDao.updateForLinkApp(paaSAppEntity);
        //清缓存
        crossAppCacheService.removeCacheByEnterpriseId(enterpriseId);

        if (uiPaasLicenseService.existMultiLanguageModule(enterpriseId)){
            List<I18nTrans.TransArg> transArg = Lists.newArrayList();
            List<String> appNamePreKeyList = Lists.newArrayList();
            List<String> appIntroductionPreKeyList = Lists.newArrayList();
            if (simpleLinkAppVO != null){
                appNamePreKeyList.add( simpleLinkAppVO.getNameI18nKey());
                appIntroductionPreKeyList.add( simpleLinkAppVO.getIntroductionI18nKey());
            }
            appIntroductionPreKeyList.add(String.format(UIPaaSI18NKey.PaaS_App_Des, arg.getAppId()));
            appIntroductionPreKeyList.add(UIPaaSI18NKey.System_Default_PREFIX + String.format(UIPaaSI18NKey.PaaS_App_Des, arg.getAppId()));
            appNamePreKeyList.add(UIPaaSI18NKey.System_Default_PREFIX + String.format(UIPaaSI18NKey.PaaS_App_Name, arg.getAppId()));
            transArg.add(TranslateI18nUtils.buildAppNameTranslateKey(arg.getName(), arg.getAppId(), appNamePreKeyList));
            if (StringUtils.isNotEmpty(arg.getDescription())){
                transArg.add(TranslateI18nUtils.buildAppIntroduceTranslateKey(arg.getDescription(), arg.getAppId(), appIntroductionPreKeyList));
            }
            i18nService.syncTransValueIncludePreKeyV2(enterpriseId, transArg, locale.toLanguageTag());
        }


        //paaSAppVO.setScopeList(new ArrayList<>());
        //paaSAppVO.setScopeListForCross(new ArrayList<>());
        // paaSAppService.updatePaaSApp(enterpriseId, employeeId, paaSAppVO);

    }

    private void updatelinkApp(String enterpriseAccount, int employeeId, UpdateLinkAppArg arg, String fullPath, SimpleLinkAppVO simpleLinkAppVO, HeaderObj headerObj) {
        com.fxiaoke.enterpriserelation2.arg.UpdateLinkAppArg updateLinkAppArg = buildUpdateLinkAppArg(arg, enterpriseAccount, employeeId, fullPath, simpleLinkAppVO);
        log.info("linkAppService.updateLinkApp.header={},arg={}", JSONObject.toJSONString(headerObj), JSONObject.toJSONString(updateLinkAppArg));
        RestResult<String> result = linkAppService.updateLinkApp(headerObj, updateLinkAppArg);
        log.info("linkAppService.updateLinkApp.header={},arg={},result={}", JSONObject.toJSONString(headerObj), JSONObject.toJSONString(updateLinkAppArg), JSONObject.toJSONString(result));
    }

    public LinkAppVoResult getLinkAppByIdFromCross(String appId, Integer tenantId) {
        HeaderObj headerObj = HeaderObj.newInstance(tenantId);
        GetLinkAppArg arg = new GetLinkAppArg();
        arg.setLinkAppId(appId);
        log.info("linkAppService.getLinkAppByIdFromCross.header={},arg={}", JSONObject.toJSONString(headerObj), JSONObject.toJSONString(arg));
        RestResult<LinkAppVoResult> result = linkAppService.getLinkApp(headerObj, arg);
        log.info("linkAppService.getLinkAppByIdFromCross.result={},header={},arg={}", JSONObject.toJSONString(result), JSONObject.toJSONString(headerObj), JSONObject.toJSONString(arg));
        return result.getData();
    }

    public void updateLinkAppUrl(String enterpriseAccount, Integer enterpriseId, int employeeId, String appId) {
        HeaderObj headerObj = HeaderObj.newInstance(enterpriseId);
        com.fxiaoke.enterpriserelation2.arg.UpdateLinkAppArg updateLinkAppArg = new com.fxiaoke.enterpriserelation2.arg.UpdateLinkAppArg();
        updateLinkAppArg.setLinkAppId(appId);
        updateLinkAppArg.setAuthContext(enterpriseAccount, employeeId);
        //
        updateLinkAppArg.setWebManagerUrl(String.format(CUSTOMER_LINK_APP_WEB_MANAGER_URL, appId));
        //web端主导航url
        updateLinkAppArg.setWebUrl(String.format(CUSTOMER_LINK_APP_WEB_URL, appId, enterpriseAccount));


        //移动端用户态主导航/移动端应用列表 url
        updateLinkAppArg.setAppUrl(String.format(CUSTOMER_LINK_APP_APP_URL, enterpriseAccount, appId, appId));
        //
        updateLinkAppArg.setAppManagerUrl(String.format(CUSTOMER_LINK_APP_APP_URL, enterpriseAccount, appId, appId));
        log.info("linkAppService.updateLinkApp.header={},arg={}", JSONObject.toJSONString(headerObj), JSONObject.toJSONString(updateLinkAppArg));
        RestResult<String> result = linkAppService.updateLinkApp(headerObj, updateLinkAppArg);
        log.info("linkAppService.updateLinkApp.header={},arg={},result={}", JSONObject.toJSONString(headerObj), JSONObject.toJSONString(updateLinkAppArg), JSONObject.toJSONString(result));
    }

    private com.fxiaoke.enterpriserelation2.arg.UpdateLinkAppArg buildUpdateLinkAppArg(UpdateLinkAppArg arg, String enterpriseAccount, int employeeId, String fullPath, SimpleLinkAppVO simpleLinkAppVO) {
        com.fxiaoke.enterpriserelation2.arg.UpdateLinkAppArg updateLinkAppArg = new com.fxiaoke.enterpriserelation2.arg.UpdateLinkAppArg();
        updateLinkAppArg.setLinkAppId(arg.getAppId());
        updateLinkAppArg.setLinkAppId(arg.getAppId());
        updateLinkAppArg.setName(arg.getName());
        String icon = StringUtils.isNotBlank(fullPath) ? fullPath : arg.getIcon();
        updateLinkAppArg.setIcon(icon);
        updateLinkAppArg.setManagerIcon(icon);
        updateLinkAppArg.setAppIcon(icon);
        updateLinkAppArg.setAppSessionIcon(icon);
        updateLinkAppArg.setType(simpleLinkAppVO == null ? Constant.LINK_APP_TYPE_CUSTOMER : Constant.LINK_APP_TYPE_PRE);
        return updateLinkAppArg;
    }

    @Override
    public PaaSAppVO queryLinkAppVOByName(int tenantId, String name, int appType) {
        return paaSAppService.queryPaaSAppVOByName(tenantId, name, appType);
    }

    @Override
    public PaaSAppVO getLinkAppByAppId(Integer enterpriseId, String appId, Integer employeeId, Locale locale) {
        PaaSAppVO paaSAppVO = paaSAppService.getPaaSAppByAppId(enterpriseId, appId, locale, RequestContextManager.isFromManager());
        //角色使用互联角色
        List<RoleInfo> roleInfoList = remoteCrossService.getOuterRoleInfosByAppId(enterpriseId, appId);
        List<Scope> scopeList = new ArrayList<>();
        List<String> scopeNameList = new ArrayList<>();
        roleInfoList.forEach(x -> {
            Scope scope = new Scope();
            scope.setDataId(x.getId());
            scope.setDataType(ScopeType.OutRole.getType());
            scopeList.add(scope);
            scopeNameList.add(x.getName());
        });
        paaSAppVO.setScopeNameList(scopeNameList);
        paaSAppVO.setScopeList(scopeList);
        paaSAppVO.setShowPaasPage(1);
        if (null != paaSAppVO.getIcon()) {
            paaSAppVO.setUploadIconType(paaSAppVO.getIcon().contains(".svg") ? IconType.IMAGE_SVG : IconType.IMG_PNG);
        }
        if (StringUtils.isNotBlank(paaSAppVO.getIconIndex())) {
            paaSAppVO.setUploadIconType(null);
        }
        paaSAppVO.setWebUrl("");
        paaSAppVO.setWebManagerUrl("");
        paaSAppVO.setHasLinkAppAdmin(true);
        paaSAppVO.setSupportExtendObject(true);
        paaSAppVO.setSupportNoFsAccount(true);
        paaSAppVO.setSupportIdentityTypes("");
        paaSAppVO.setManageMode(null);
        paaSAppVO.setAssociationCrmObject(true);
        paaSAppVO.setAuthedRoleNum(roleInfoList.size());
        paaSAppVO.setAvailableTime(licenseManagerService.getLinkAppAvailableTime(enterpriseId, appId));
        if (paaSAppVO.getAppType() == AppTypeEnum.LINK_APP.getAppType()) {
            List<LinkAppAuthVo> linkAppAuthVoList = getLinkAppAuthVos(enterpriseId, employeeId);
            linkAppAuthVoList.forEach(x -> {
                if (x.getLinkApp().getId().equals(appId)) {
                    paaSAppVO.setWebUrl(x.getLinkApp().getWebUrl());
                    paaSAppVO.setWebManagerUrl(x.getLinkApp().getWebManagerUrl());
                    paaSAppVO.setHasLinkAppAdmin(x.getHasLinkAppAdmin());
                    paaSAppVO.setSupportExtendObject(x.getLinkApp().getSupportExtendObject());
                    paaSAppVO.setSupportNoFsAccount(x.getLinkApp().getSupportNoFsAccount());
                    paaSAppVO.setSupportIdentityTypes(x.getLinkApp().getSupportIdentityTypes());
                    paaSAppVO.setManageMode(x.getAuthedRoleNum());
                    paaSAppVO.setUnableSetManagerText(x.getLinkApp().getUnableSetManagerText());
                    paaSAppVO.setAssociationCrmObject(x.getLinkApp().getAssociationCrmObject());
                    paaSAppVO.setShowPaasPage(x.getLinkApp().getShowPaasPage());
                }
            });
            if (StringUtils.isBlank(paaSAppVO.getUnableSetManagerText())) {
                //赋值管理员
                HeaderObj headerObj = HeaderObj.newInstance(enterpriseId);
                ListAdminsByLinkAppIdArg arg = new ListAdminsByLinkAppIdArg();
                arg.setLinkAppId(appId);
                arg.setUpstreamEa(eieaConverter.enterpriseIdToAccount(enterpriseId));
                log.info("upstreamService.listAdminsByLinkAppId.header={},arg={}", JSONObject.toJSONString(headerObj), JSONObject.toJSONString(arg));
                RestResult<List<ListAdminsByLinkAppIdResult>> result = upstreamService.listAdminsByLinkAppId(headerObj, arg);
                log.info("upstreamService.listAdminsByLinkAppId.result={},header={},arg={}", JSONObject.toJSONString(result), JSONObject.toJSONString(headerObj), JSONObject.toJSONString(arg));

                List<String> adminList = new ArrayList<>();
                if (null != result && CollectionUtils.isNotEmpty(result.getData())) {
                    adminList = result.getData().stream().map(x -> String.valueOf(x.getFsUserId())).collect(Collectors.toList());
                }
                List<Scope> adminScopeList = ScopesUtil.buildScope(ScopeType.Employee.getType(), adminList);
                paaSAppVO.setAdmins(adminScopeList);
            }
        }
        return paaSAppVO;
    }

    @Override
    public void updateStatus(String enterpriseAccount, Integer enterpriseId, Integer employeeId, String appId, int status) {
        HeaderObj headerObj = HeaderObj.newInstance(enterpriseId);
        if (status == PaaSStatus.enable || status == PaaSStatus.disable) {
            //调用互联接口 启用/停用 应用
            UpdateEnterpriseLinkAppRelationStatusArg arg = new UpdateEnterpriseLinkAppRelationStatusArg();
            //1：启用  2：停用
            arg.setStatus(status);
            arg.setLinkAppIds(Lists.newArrayList(appId));
            arg.setUpstreamEa(enterpriseAccount);
            arg.setFsUserId(employeeId);
            log.info("upstreamService.updateEnterpriseLinkAppRelationStatus.header={},arg={}", JSONObject.toJSONString(headerObj), JSONObject.toJSONString(arg));
            RestResult<String> result = upstreamService.updateEnterpriseLinkAppRelationStatus(headerObj, arg);
            log.info("upstreamService.updateEnterpriseLinkAppRelationStatus.result={},header={},arg={}", JSONObject.toJSONString(result), JSONObject.toJSONString(headerObj), JSONObject.toJSONString(arg));
        } else if (status == PaaSStatus.delete) {
            //调用互联接口删除应用
            DeleteIntelligentAppArg arg = new DeleteIntelligentAppArg();
            arg.setLinkAppId(appId);
            arg.setUpstreamEa(enterpriseAccount);
            log.info("linkAppService.deleteIntelligentApp.header={},arg={}", JSONObject.toJSONString(headerObj), JSONObject.toJSONString(arg));
            RestResult<String> result = linkAppService.deleteIntelligentApp(headerObj, arg);
            log.info("linkAppService.deleteIntelligentApp.result={},header={},arg={}", JSONObject.toJSONString(result), JSONObject.toJSONString(headerObj), JSONObject.toJSONString(arg));
        }
        paaSAppService.updateStatus(enterpriseId, employeeId, appId, status);
    }

    @Override
    public void deleteLinkApp(String enterpriseAccount, int enterpriseId, Integer employeeId, String appId, int status) {
        tenantMenuService.deleteTenantMenusByAppId(enterpriseId, BizType.PAAS.getType(), appId);
        homePageBaseService.deleteHomePageLayouts(enterpriseId, BizType.PAAS.getType(), appId);
        updateStatus(enterpriseAccount, enterpriseId, employeeId, appId, status);
    }

    @Override
    public boolean checkLinkApp(Integer enterpriseId, String appId) {
        PaaSAppVO paaSAppVO = paaSAppService.getPaaSAppByAppId(enterpriseId, appId, null);
        if (paaSAppVO == null) {
            return false;
        }
        if (PaaSStatus.enable == paaSAppVO.getStatus()) {
            return false;
        }

        List<PageTemplate> pageTemplates = tenantPageTempleBaseService.getCustomerPageTemples(enterpriseId, appId, null);
        List<SiteInfoVO> siteInfoVOList = siteService.findSitesByAppId(enterpriseId, appId);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(pageTemplates)) {
            pageTemplates = pageTemplates.stream().filter(x -> x.getStatus() == 0).collect(Collectors.toList());
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(pageTemplates) && CollectionUtils.isEmpty(siteInfoVOList)) {
            throw new WebPageException(InterErrorCode.PLEASE_CONFIGURE_TEMPLATE_VIEW);
        }
        return true;
    }

    //todo url
    @Override
    public GetUpLinkAppListResult getUpLinkAppList(int enterpriseId, String enterpriseAccount, int employeeId, Locale locale) {
        GetUpLinkAppListResult result = new GetUpLinkAppListResult();
        if (WebPageGraySwitch.isAllowSynLinkAppGrayEaByShuxian(enterpriseAccount)) {
            newGetUpLinkAppListResult(enterpriseId, employeeId, locale, result);
        } else {
            oldGetUpLinkAppListResult(enterpriseId, enterpriseAccount, locale, result);
        }
        return result;
    }

    private GetUpLinkAppListResult oldGetUpLinkAppListResult(int enterpriseId, String enterpriseAccount, Locale locale, GetUpLinkAppListResult result) {
        List<GetLinkAppListResult> linkAppListResultList = new ArrayList<>();
        result.setLinkAppList(linkAppListResultList);
        //查询互联应用
        HeaderObj headerObj = HeaderObj.newInstance(enterpriseId);
        ListAllUpstreamLinkAppsForAppArg arg = new ListAllUpstreamLinkAppsForAppArg();
        arg.setDownstreamEa(enterpriseAccount);
        log.info("downstreamService.listAllUpstreamEnterpriseAppsByDownstream.header={},arg={}", JSONObject.toJSONString(headerObj), JSONObject.toJSONString(arg));
        RestResult<List<UpstreamEnterpriseAppsVo>> restResult = downstreamService.listAllUpstreamEnterpriseAppsByDownstream(headerObj, arg);
        log.info("downstreamService.listAllUpstreamEnterpriseAppsByDownstream.restResult={},header={},arg={}", JSONObject.toJSONString(restResult), JSONObject.toJSONString(headerObj), JSONObject.toJSONString(arg));
        List<UpstreamEnterpriseAppsVo> upstreamEnterpriseAppsVoList = restResult.getData();
        if (CollectionUtils.isEmpty(upstreamEnterpriseAppsVoList)) {
            return result;
        }
        Map<String, List<PaaSAppVO>> map = new HashMap<>();
        upstreamEnterpriseAppsVoList.forEach(item -> {
            int upEi = eieaConverter.enterpriseAccountToId(item.getUpstreamEa());
            List<String> paasAppIds = item.getLinkAppVos().stream().map(x -> x.getId()).collect(Collectors.toList());
            List<PaaSAppVO> paaSAppVOList = paaSAppService.getPaaSAppByAppIds(upEi, paasAppIds.stream().distinct().collect(Collectors.toList()), null, locale);
            map.put(item.getUpstreamEa(), paaSAppVOList);
        });


        upstreamEnterpriseAppsVoList.forEach(item -> {
            List<PaaSAppVO> paaSAppVOList = map.get(item.getUpstreamEa());
            paaSAppVOList = paaSAppVOList.stream().filter(x -> x.getStatus() != PaaSStatus.disable).collect(Collectors.toList());
            Map<String, PaaSAppVO> paaSAppVOMap = paaSAppVOList.stream().collect(Collectors.toMap(PaaSAppVO::getAppId, x -> x, (x1, x2) -> x1));
            GetLinkAppListResult getLinkAppListResult = new GetLinkAppListResult();
            linkAppListResultList.add(getLinkAppListResult);
            //过滤出预置互联应用
            List<LinkAppData> preLinkAppList = item.getLinkAppVos().stream().filter(x -> x.getType() == Constant.LINK_APP_TYPE_PRE).collect(Collectors.toList());
            getLinkAppListResult.setManageMode(item.getManageMode());
            getLinkAppListResult.setUpstreamEa(item.getUpstreamEa());
            getLinkAppListResult.setName(item.getName());
            getLinkAppListResult.setShortName(item.getShortName());
            List<LinkAppVO> linkAppVOList = new ArrayList<>();
            preLinkAppList.forEach(item1 -> {
                LinkAppVO linkAppVO = new LinkAppVO();
                BeanUtils.copyProperties(item1, linkAppVO);
                if (null != paaSAppVOMap && null != paaSAppVOMap.get(item1.getId())) {
                    linkAppVO.setIcon(paaSAppVOMap.get(item1.getId()).getIcon());
                    linkAppVO.setName(paaSAppVOMap.get(item1.getId()).getName());
                }
                linkAppVOList.add(linkAppVO);
            });
            getLinkAppListResult.setLinkAppList(linkAppVOList);
            List<PaaSAppVO> paaSAppVOS = new ArrayList<>();
            getLinkAppListResult.setCustomerLinkAppList(paaSAppVOS);
            //过滤出自定义互联应用
            List<LinkAppData> customerlinkAppVos = item.getLinkAppVos().stream().filter(x -> x.getType() == Constant.LINK_APP_TYPE_CUSTOMER).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(customerlinkAppVos) && null != paaSAppVOMap) {
                customerlinkAppVos.forEach(item1 -> {
                    if (null != paaSAppVOMap.get(item1.getId())) {
                        PaaSAppVO paaSAppVO = paaSAppVOMap.get(item1.getId());
                        paaSAppVO.setWebUrl(item1.getWebUrl());
                        paaSAppVO.setWebManagerUrl(item1.getWebManagerUrl());
                        if (null != paaSAppVOMap && null != paaSAppVOMap.get(item1.getId())) {
                            paaSAppVO.setIcon(paaSAppVOMap.get(item1.getId()).getIcon());
                            paaSAppVO.setName(paaSAppVOMap.get(item1.getId()).getName());
                        }
                        paaSAppVOS.add(paaSAppVO);
                    }
                });
            }
        });
        return null;
    }


    private void newGetUpLinkAppListResult(int enterpriseId, int employeeId, Locale locale, GetUpLinkAppListResult result) {
        List<GetLinkAppListResult> linkAppListResultList = new ArrayList<>();
        result.setLinkAppList(linkAppListResultList);

        Map<String, Map<String, LinkAppPartialData>> map = remoteCrossService.listAllUpstreamOpenedLinkAppPartialDatas(enterpriseId, employeeId);
        if (null == map || map.size() == 0) {
            return;
        }
        map.forEach((k, v) -> {

            AtomicReference<Integer> manageMode = new AtomicReference<>(2);
            GetLinkAppListResult getLinkAppListResult = new GetLinkAppListResult();
            String upEaAndName = k;
            String[] split = upEaAndName.split(ComponentConstant.SEPARATOR_GROOT);
            String upEa = split[0];
            String name = split[1];
            int upEi = eieaConverter.enterpriseAccountToId(upEa);
            getLinkAppListResult.setUpstreamEa(upEa);
            getLinkAppListResult.setShortName(StringUtils.isNotBlank(name) ? name : upEa);
            getLinkAppListResult.setName(StringUtils.isNotBlank(name) ? name : upEa);
            List<PaaSAppVO> customerLinkAppList = new ArrayList<>();
            List<LinkAppVO> linkAppList = new ArrayList<>();
            List<String> customerLinkAppIds = new ArrayList<>();
            List<String> linkAppIds = new ArrayList<>();
            Map<String, LinkAppPartialData> linkAppPartialDataMap = v;
            linkAppPartialDataMap.forEach((x, y) -> {
                String appId = x;
                LinkAppPartialData linkAppPartialData = y;
                if (null != presetLinkAppConfig.getLinkAppVOByLinkAppId(appId)) {
                    //预置应用
                    linkAppIds.add(appId);
                } else {
                    //自定义应用
                    customerLinkAppIds.add(appId);
                }
            });
            GetlinkAppListArg arg = new GetlinkAppListArg();
            arg.setTenantId(upEi);
            Map<String, PaaSAppVO> linkappVosMap = paaSAppService.getLinkAppVOList(arg, locale).stream()
                    .collect(Collectors.toMap(PaaSAppVO::getAppId, x -> x, (x1, x2) -> x1));
            presetLinkAppConfig.getLinkAppVOListByLinkAppIds(linkAppIds).forEach(item -> {
                LinkAppVO linkAppVO = new LinkAppVO();
                BeanUtils.copyProperties(item, linkAppVO);
                linkAppVO.setIsExpired(linkAppPartialDataMap.get(item.getId()).getIsExpired());
                linkAppVO.setHasLinkAppAdmin(linkAppPartialDataMap.get(item.getId()).getHasLinkAppAdmin());
                linkAppVO.setSupportNoFsAccount(linkAppPartialDataMap.get(item.getId()).getSupportNoFsAccount());
                linkAppVO.setDataAuthType(linkAppPartialDataMap.get(item.getId()).getDataAuthType());
                if (null != linkappVosMap && null != linkappVosMap.get(item.getId())) {
                    linkAppVO.setIcon(linkappVosMap.get(item.getId()).getIcon());
                    linkAppVO.setName(linkappVosMap.get(item.getId()).getName());
                    linkAppVO.setIntroduction(linkappVosMap.get(item.getId()).getDescription());
                    //重新赋值url TODO
                    linkAppVO.setAppManagerUrl(urlReplaceUpstreamEa(linkAppVO.getAppManagerUrl(), upEa, linkAppVO.getId()));
                    linkAppVO.setAppUrl(urlReplaceUpstreamEa(linkAppVO.getAppUrl(), upEa, linkAppVO.getId()));
                    linkAppVO.setWebManagerUrl(urlReplaceUpstreamEa(linkAppVO.getWebManagerUrl(), upEa, linkAppVO.getId()));
                    linkAppVO.setWebUrl(urlReplaceUpstreamEa(linkAppVO.getWebUrl(), upEa, linkAppVO.getId()));
                }
                manageMode.set(linkAppPartialDataMap.get(item.getId()).getManageMode());
                linkAppList.add(linkAppVO);
            });
            getLinkAppListResult.setLinkAppList(linkAppList);
            getLinkAppListResult.setManageMode(manageMode.get());
            List<PaaSAppVO> customerlinkAppVos = paaSAppService.getPaaSAppByAppIds(upEi, customerLinkAppIds.stream().distinct().collect(Collectors.toList()), null, locale, true);
            customerLinkAppList = customerlinkAppVos.stream().filter(x -> x.getStatus() != PaaSStatus.disable).collect(Collectors.toList());
            getLinkAppListResult.setCustomerLinkAppList(customerLinkAppList);
            linkAppListResultList.add(getLinkAppListResult);
        });

    }
}
