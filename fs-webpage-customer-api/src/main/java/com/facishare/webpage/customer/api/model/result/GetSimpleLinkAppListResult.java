package com.facishare.webpage.customer.api.model.result;

import com.facishare.webpage.customer.api.model.SimpleLinkAppVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
/**
 * 仅对互联侧开放使用
 */
public class GetSimpleLinkAppListResult implements Serializable {

    private List<SimpleLinkAppVO> linkAppList;

    private int pageSize;
    private int pageNum;
    private int totalCount;

}
