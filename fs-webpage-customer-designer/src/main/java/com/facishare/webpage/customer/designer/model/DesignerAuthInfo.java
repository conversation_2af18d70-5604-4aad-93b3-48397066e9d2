package com.facishare.webpage.customer.designer.model;

import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Locale;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class DesignerAuthInfo implements Serializable {
    private UserInfo userInfo;
    private OuterUserInfo outerUserInfo;
    private Locale locale;

}
