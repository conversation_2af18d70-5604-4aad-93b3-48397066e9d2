package com.facishare.webpage.customer.controller.model.result.cms;

import com.facishare.webpage.customer.controller.model.vo.cms.WorkSpaceVO;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WorkSpaceListResult {
    @Builder.Default
    List<WorkSpaceVO> workspaceList = Lists.newArrayList();
    @Builder.Default
    Long totalNum = 0L;
    @Builder.Default
    Integer pageNum = 1;
    @Builder.Default
    Integer pageSize = 20;
}
