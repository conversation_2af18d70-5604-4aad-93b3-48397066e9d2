package com.facishare.webpage.customer.core.util;

import com.facishare.qixin.url.util.UrlParseUtil;
import com.facishare.webpage.customer.core.config.BIUrlConfig;
import com.google.common.base.Strings;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by zhangyu on 2019/12/26
 */
public class BIUrlUtil {

    @Resource
    private BIUrlConfig biUrlConfig;


    public static final String HomePage_BI_URL = "HomePage_BI_URL";
    public static final String HomePage_BI_BASE_URL = "HomePage_BI_BASE_URL";
    public static final String HomePage_BI_Rank_URL = "HomePage_BI_Rank_URL";
    public static final String HomePage_BI_SalesAssistant_URL = "HomePage_BI_SalesAssistant_URL";
    public static final String rank = "BI_Rank";
    public static final String salereport = "salereport";
    public static final String salesbrief = "salesbrief";
    public static String SalesAssistant = "salesassistant";

    public String buildUrl(String cardId, int type) {
        String url = "";
        if (Strings.isNullOrEmpty(cardId)) {
            return url;
        }
        String[] split = cardId.split("_");
        if (split.length != 2) {
            return url;
        } else {
            if (split[0].equals("BI")) {
                String key = split[1].toLowerCase();
                if (rank.equals(cardId)) {
                    url = biUrlConfig.getBIUrl(HomePage_BI_Rank_URL);
                }else if (salereport.equals(key)) {
                    Map<String, String> urlParameters = new HashMap<>();
                    urlParameters.put("id", salesbrief);
                    url = UrlParseUtil.parseUrl(biUrlConfig.getBIUrl(HomePage_BI_BASE_URL), urlParameters);
                } else if (SalesAssistant.equals(key)) {
                    url = biUrlConfig.getBIUrl(HomePage_BI_SalesAssistant_URL);
                } else {
                    Map<String, String> urlParameters = new HashMap<>();
                    urlParameters.put("id", cardId);
                    if (type == 3){
                        url = UrlParseUtil.parseUrl(biUrlConfig.getBIUrl(HomePage_BI_BASE_URL), urlParameters);
                    }else {
                        url = UrlParseUtil.parseUrl(biUrlConfig.getBIUrl(HomePage_BI_URL), urlParameters);
                    }
                }
            }
        }
        return url;
    }

}
