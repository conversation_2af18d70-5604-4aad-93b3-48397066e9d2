<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">


    <bean id="menusConfig" class="com.facishare.webpage.customer.core.config.MenusConfig" init-method="init"/>

    <bean id="menusCollectionConfig" class="com.facishare.webpage.customer.core.config.MenusCollectionConfig"
          init-method="init"/>

    <bean id="widgetsConfig" class="com.facishare.webpage.customer.core.config.WidgetsConfig" init-method="init"/>

    <bean id="widgetCollectionConfig" class="com.facishare.webpage.customer.core.config.WidgetCollectionConfig"
          init-method="init"/>

    <bean id="allWidgetsConfig" class="com.facishare.webpage.customer.core.config.AllWidgetsConfig"
          init-method="init"/>

    <bean id="componentListManager" class="com.facishare.webpage.customer.core.business.ComponentListManager"
          init-method="init">
        <property name="configName" value="fs-webpage-customer-web-component-list-map"/>
    </bean>

    <bean id="customerCoreConfig" class="com.facishare.webpage.customer.core.config.CustomerCoreConfig"/>

    <bean id="objectConfig" class="com.facishare.webpage.customer.core.config.ObjectConfig" init-method="init"/>

    <bean id="biUrlConfig" class="com.facishare.webpage.customer.core.config.BIUrlConfig" init-method="init"/>

    <bean id="biUrlUtil" class="com.facishare.webpage.customer.core.util.BIUrlUtil"/>

    <bean id="componentNameConfig" class="com.facishare.webpage.customer.core.config.ComponentNameConfig"
          init-method="init"/>

    <bean id="componentCovertService" class="com.facishare.webpage.customer.core.component.ComponentCovertService"/>

    <bean id="mainChannelIconConfig" class="com.facishare.webpage.customer.core.config.MainChannelIconConfig"
          init-method="init"/>

    <bean id="pageTemplateConfig" class="com.facishare.webpage.customer.core.config.PageTemplateConfig"
          init-method="init"/>

    <bean id="groupMetaConfig" class="com.facishare.webpage.customer.core.config.GroupMetaConfig" init-method="init"/>

    <bean id="filterUtils" class="com.facishare.webpage.customer.core.util.FilterUtils"/>

    <bean id="fileServiceConfig" class="com.facishare.webpage.customer.core.configuration.FileServiceConfig"/>


    <bean id="qixinI18nService" class="com.facishare.qixin.i18n.QixinI18nServiceImpl" init-method="init"
          destroy-method="destroy"/>

    <bean id="componentLanguageService"
          class="com.facishare.webpage.customer.core.language.ComponentLanguageServiceImpl"/>

    <bean id="iconPathConfig" class="com.facishare.webpage.customer.core.config.IconPathConfig" init-method="init"/>
    <!--- 纷享内部调用组织架构服务 非元数据团队建议使用该配置-->
    <import resource="classpath:spring/fs-organization-api-rest-client.xml"/>
    <import resource="classpath:/META-INF/spring/fs-qixin-i18n.xml"/>
    <import resource="classpath:/META-INF/spring/fs-webpage-customer-core-rest-api.xml"/>

    <context:component-scan base-package="com.facishare.webpage.customer.core.service"/>

    <!--fs-orgainzation-adapter-api -->
    <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-client.xml"/>
    <!--默认fs-organization-adapter-provider服务地址，支持client方指定provider指定地址-->
    <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-host-config.xml"/>


</beans>