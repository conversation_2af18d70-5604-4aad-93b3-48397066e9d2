package com.facishare.webpage.customer.api.model.arg;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2019/9/9
 */
@Data
public class ModifyHomePageLayoutArg implements Serializable {

    private String appId;

    private int appType;     //应用类型：1、表示CRM；2、表示应用；3、表示BI列表洞察; 4、自定义页面

    @SerializedName("HomePageLayout")
    private HomePageLayoutTO homePageLayout;
    /**
     * 自定义页面类型
     * 0：企业内
     * 1：互联
     */
    private Integer applyType = 0;

    private Integer enterpriseId;
    private String enterpriseAccount;
    private Integer employeeId;
    private ClientTypeEnum type;
    private String version;
    private String deviceId;
    private Locale locale;
    private String osVersion;
    private boolean manage = false;

    public void valid() {
        if (Objects.isNull(homePageLayout)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        List<JSONObject> customerLayoutList = Objects.isNull(homePageLayout.getCustomerLayoutList())
                ? Lists.newArrayList() : homePageLayout.getCustomerLayoutList().stream().filter(Objects::nonNull).collect(Collectors.toList());
        JSONObject customerLayout = homePageLayout.getCustomerLayout();
        if(!customerLayoutList.isEmpty() && Objects.nonNull(customerLayout)){
            return;
        }
        if(customerLayoutList.isEmpty() && Objects.isNull(customerLayout)){
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        if(Objects.isNull(customerLayout)){
            homePageLayout.setCustomerLayout(customerLayoutList.get(0));
        }
        if(customerLayoutList.isEmpty()){
            customerLayoutList.add(customerLayout);
        }

    }
}
