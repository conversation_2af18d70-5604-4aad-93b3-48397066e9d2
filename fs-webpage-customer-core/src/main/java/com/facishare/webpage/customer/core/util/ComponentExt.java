package com.facishare.webpage.customer.core.util;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.constant.CustomerLayoutField;
import com.facishare.webpage.customer.api.constant.TranslateI18nUtils;
import com.facishare.webpage.customer.api.model.I18nInfo;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class ComponentExt extends JSONObject {

    private static final String I18N_INFO_LIST = "i18nInfoList";
    private static final String I18N_INFO_MAP = "i18nInfoMap";


    private ComponentExt(JSONObject jsonObject) {
        super(jsonObject != null ? jsonObject : new JSONObject());
    }

    public static ComponentExt of(JSONObject jsonObject) {
        if (jsonObject instanceof ComponentExt) {
            return (ComponentExt) jsonObject;
        }
        return new ComponentExt(jsonObject);
    }

    private static String getTransKey(String tenantId, String layoutId, String componentApiName, String apiName) {
        String result = "bizComponent." + layoutId + "." + componentApiName + "." + apiName;
        return result.replace(tenantId + "-", "").replace(tenantId + "_", "");
    }

    public List<I18nInfo> getI18nInfoList(String tenantId, String layoutId, String componentApiName) {
        try {
            String i18nInfoStr = getI18nInfoList();
            if (StringUtils.isBlank(i18nInfoStr)) {
                return Lists.newArrayList();
            }
            List<I18nInfo> i18nInfos = CollectionUtils.nullToEmpty(JSONArray.parseArray(i18nInfoStr, I18nInfo.class));
            i18nInfos.stream().filter(x -> StringUtils.isBlank(x.getCustomKey()))
                    .forEach(x -> x.setCustomKey(getTransKey(tenantId, layoutId, componentApiName, x.getApiName())));
            return i18nInfos;
        } catch (Exception e) {
            log.error("getI18nInfoList error! layoutId:{},componentApiName:{}", layoutId, componentApiName, e);
            return Lists.newArrayList();
        }
    }

    public boolean isTabs() {
        return this.getString(CustomerLayoutField.type).equals("tabs");
    }


    public String getApiName() {
        return this.getString(CustomerLayoutField.apiName);
    }


    public void setI18nInfo(List<I18nInfo> i18nInfos) {
        if (CollectionUtils.empty(i18nInfos)) {
            return;
        }
        Map<String, I18nInfo> i18nInfoMap = i18nInfos.stream().collect(Collectors.toMap(I18nInfo::getApiName, it -> it));

        if (!RequestContextManager.isFromRest() && !RequestContextManager.isFromManager()) {
            this.put(I18N_INFO_MAP, i18nInfoMap);
        } else {
            this.put(I18N_INFO_LIST, i18nInfos);
        }

    }

    public List<String> getItemI18nKeys() {
        try {
            List<String> itemI18nKeyList = Lists.newArrayList();
            if (CollectionUtils.notEmpty(this.getJSONArray(CustomerLayoutField.itemI18nKeys))) {
                itemI18nKeyList = this.getJSONArray(CustomerLayoutField.itemI18nKeys).toJavaList(String.class);
            }
            if (CollectionUtils.empty(itemI18nKeyList)) {
                JSONObject jsonObject = this.getJSONObject("props");
                if (Objects.nonNull(jsonObject) && CollectionUtils.notEmpty(jsonObject.getJSONArray(CustomerLayoutField.itemI18nKeys))) {
                    itemI18nKeyList = jsonObject.getJSONArray(CustomerLayoutField.itemI18nKeys).toJavaList(String.class);
                }
            }
            return CollectionUtils.nullToEmpty(itemI18nKeyList);
        } catch (Exception e) {
            log.error("getItemI18nKeys error! componentApiName:{}", this.getString(CustomerLayoutField.apiName), e);
            return Lists.newArrayList();
        }
    }

    public String getNameI18nKey() {
        try {
            String nameI18nKey = this.getString(CustomerLayoutField.nameI18nKey);
            if (StringUtils.isEmpty(nameI18nKey)) {
                JSONObject jsonObject = this.getJSONObject("props");
                if (Objects.nonNull(jsonObject) && jsonObject.containsKey(CustomerLayoutField.nameI18nKey)) {
                    nameI18nKey = jsonObject.getString(CustomerLayoutField.nameI18nKey);
                }
            }
            return StringUtils.defaultString(nameI18nKey, "");
        } catch (Exception e) {
            log.error("getItemI18nKeys error! componentApiName:{}", this.getString(CustomerLayoutField.apiName), e);
            return "";
        }
    }

    public String getNewHeader() {
        try {
            String newHeader = this.getString(CustomerLayoutField.newHeader);
            if (StringUtils.isEmpty(newHeader)) {
                JSONObject jsonObject = this.getJSONObject("props");
                if (Objects.nonNull(jsonObject) && jsonObject.containsKey(CustomerLayoutField.newHeader)) {
                    newHeader = jsonObject.getString(CustomerLayoutField.newHeader);
                }
            }
            return StringUtils.defaultString(newHeader, "");
        } catch (Exception e) {
            log.error("getNewHeader error! componentApiName:{}", this.getString(CustomerLayoutField.apiName), e);
            return "";
        }
    }

    public String getTitleName() {
        try {
            String titleName = this.getString(CustomerLayoutField.titleName);
            if (StringUtils.isEmpty(titleName)) {
                JSONObject jsonObject = this.getJSONObject("props");
                if (Objects.nonNull(jsonObject) && jsonObject.containsKey(CustomerLayoutField.titleName)) {
                    titleName = jsonObject.getString(CustomerLayoutField.titleName);
                }
            }
            return StringUtils.defaultString(titleName, "");
        } catch (Exception e) {
            log.error("getTitleName error! componentApiName:{}", this.getString(CustomerLayoutField.apiName), e);
            return "";
        }
    }

    public String getTitle() {
        try {
            String title = this.getString(CustomerLayoutField.title);
            if (StringUtils.isEmpty(title)) {
                JSONObject jsonObject = this.getJSONObject("props");
                if (Objects.nonNull(jsonObject) && jsonObject.containsKey(CustomerLayoutField.title)) {
                    title = jsonObject.getString(CustomerLayoutField.title);
                }
            }
            return StringUtils.defaultString(title, "");
        } catch (Exception e) {
            log.error("getTitle error! componentApiName:{}", this.getString(CustomerLayoutField.apiName), e);
            return "";
        }
    }


    public String getHeader() {
        try {
            String header = this.getString(CustomerLayoutField.header);
            if (StringUtils.isEmpty(header)) {
                JSONObject jsonObject = this.getJSONObject("props");
                if (Objects.nonNull(jsonObject) && jsonObject.containsKey(CustomerLayoutField.header)) {
                    header = jsonObject.getString(CustomerLayoutField.header);
                }
            }
            return StringUtils.defaultString(header, "");
        } catch (Exception e) {
            log.error("getHeader error! componentApiName:{}", this.getString(CustomerLayoutField.apiName), e);
            return "";
        }
    }

    public JSONArray getTabs() {
        try {
            JSONArray jsonArray = this.getJSONArray(CustomerLayoutField.tabCollection);
            if (Objects.nonNull(jsonArray)) {
                return jsonArray;
            }
            JSONObject props = this.getJSONObject(CustomerLayoutField.props);
            if (Objects.isNull(props)) {
                return new JSONArray();
            }
            JSONArray tabs = props.getJSONArray(CustomerLayoutField.tabCollection);

            return Objects.isNull(tabs) ? new JSONArray() : tabs;
        } catch (Exception e) {
            log.error("getTabs Error! componentInfo:{}", this, e);
            return new JSONArray();
        }
    }

    public static String getTabTransKey(String tenantId, String layoutId, String widgetId, String tabApiName) {
        if (WebPageGraySwitch.isGetTabsTransValueByOldKeyGrayEi(tenantId)) {
            return TranslateI18nUtils.getWebTemplateTabsNameKey(layoutId, widgetId, tabApiName);
        }
        return TranslateI18nUtils.getWebTemplateWidgetsNameKey(layoutId, tabApiName);
    }

    public Integer getCategory() {
        Integer category = this.getInteger(CustomerLayoutField.isCateGory);
        if (Objects.nonNull(category)) {
            return category;
        }
        JSONObject props = this.getJSONObject(CustomerLayoutField.props);
        if (Objects.isNull(props)) {
            return null;
        }
        category = props.getInteger(CustomerLayoutField.isCateGory);
        return category;
    }

    public String getDataId() {
        String dataId = this.getString(CustomerLayoutField.dataId);
        if (Objects.nonNull(dataId)) {
            return dataId;
        }
        JSONObject props = this.getJSONObject(CustomerLayoutField.props);
        if (Objects.isNull(props)) {
            return null;
        }
        return props.getString(CustomerLayoutField.dataId);
    }

    public List<String> getListString(String key) {
        try {
            JSONArray jsonArray = this.getJSONArray(key);
            if (Objects.isNull(jsonArray)) {
                return Lists.newArrayList();
            }
            return jsonArray.toJavaList(String.class);
        } catch (Exception e) {
            log.error("getTabs Error! componentInfo:{}", this, e);
            return Lists.newArrayList();
        }
    }

    public String getI18nInfoList() {
        String i18nInfoListStr = getString(I18N_INFO_LIST);
        if (Objects.nonNull(i18nInfoListStr)) {
            return i18nInfoListStr;
        }
        JSONObject props = this.getJSONObject(CustomerLayoutField.props);
        if (Objects.isNull(props)) {
            return "";
        }
        return props.getString(I18N_INFO_LIST);
    }

    public String getPropsField(String fieldApi) {
        JSONObject props = getJSONObject("props");
        if (Objects.isNull(props)) {
            return "";
        }
        return props.getString(fieldApi);
    }
}
