package com.facishare.webpage.customer.api.model.core;

import com.fxiaoke.release.GrayRule;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@NoArgsConstructor
public class URLFilter implements Serializable {
    @Setter
    @Getter
    private String url;
    @Setter
    @Getter
    private long theLowVersion;
    @Setter
    @Getter
    private Set<String> clientTypeList = new HashSet<>();
    @Setter
    @Getter
    private Set<String> appIdList;
    @Getter
    private String fsGrayRuleString;

    private GrayRule grayRule;

    public static String getURLByFilterList(List<URLFilter> customizedUrlList,
                                            int ei, int employeeId, String clientTypeString, long clientVersion) {
        return getURLByFilterList(customizedUrlList, ei, employeeId, clientTypeString, clientVersion, "");
    }

    public static String getURLByFilterList(List<URLFilter> customizedUrlList,
                                            int ei, int employeeId, String clientTypeString, long clientVersion, String fsAppId) {
        if (CollectionUtils.isEmpty(customizedUrlList)) {
            return null;
        }
        String userId = ei + "." + employeeId;
        for (URLFilter filter : customizedUrlList) {
            if (filter == null) {
                continue;
            }
            String url = filter.getURLByFilter(userId, clientTypeString, clientVersion, fsAppId);
            if (StringUtils.isNotBlank(url)) {
                return url;
            }
        }
        return null;
    }

    public String getURLByFilter(String userId, String clientTypeString, long clientVersion, String fsAppId) {
        if (theLowVersion > 0 && clientVersion > 0) {
            if (clientVersion < theLowVersion
                    || (clientVersion > 100000000L && clientVersion < (100000000L + theLowVersion))) {
                return null;
            }
        }
        if (CollectionUtils.isNotEmpty(clientTypeList) && !clientTypeList.contains(clientTypeString)) {
            return null;
        }
        if (StringUtils.isNotBlank(fsAppId) && CollectionUtils.isNotEmpty(appIdList) && !appIdList.contains(fsAppId)) {
            return null;
        }
        if (grayRule != null && !grayRule.isAllow(userId)) {
            return null;
        }
        return url;
    }

    public void setFsGrayRuleString(String fsGrayRuleString) {
        this.fsGrayRuleString = fsGrayRuleString;
        if (StringUtils.isBlank(fsGrayRuleString)) {
            grayRule = null;
        } else {
            grayRule = new GrayRule(fsGrayRuleString);
        }
    }

    @Override
    public String toString() {
        return "URLFilter{" +
                "theLowVersion=" + theLowVersion +
                ", url='" + url + '\'' +
                ", clientTypeList=" + clientTypeList +
                ", fsGrayRuleString='" + fsGrayRuleString + '\'' +
                ", grayRule=" + grayRule +
                '}';
    }
}
