package com.facishare.webpage.customer.api.model.arg;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Locale;

/**
 * Created by zhangyu on 2019/9/9
 */
@Data
public class GetHomePageWidgetsByIdArg implements Serializable {

    private String layoutId;
    private Integer tenantId;
    private Locale locale;
    private Integer userId;
    private boolean translateFlag = true;
    private Integer enterpriseId;
    private String enterpriseAccount;
    private Integer employeeId;
    private Integer appType;
    private ClientTypeEnum type;

}
