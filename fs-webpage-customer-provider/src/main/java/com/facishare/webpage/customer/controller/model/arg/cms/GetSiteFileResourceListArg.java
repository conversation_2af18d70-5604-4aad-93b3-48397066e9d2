package com.facishare.webpage.customer.controller.model.arg.cms;

import lombok.Data;
import java.io.Serializable;

/**
 * 获取站点文件资源列表参数 Created by cursor.
 */
@Data
public class GetSiteFileResourceListArg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 站点ApiName
     */
    private String siteApiName;

    /**
     * 站点中的目录路径，从站点定义的根目录开始，为空表示根目录
     */
    private String dirPath;
}
