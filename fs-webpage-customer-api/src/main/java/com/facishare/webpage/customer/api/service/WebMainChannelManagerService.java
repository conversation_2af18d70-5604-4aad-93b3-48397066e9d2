package com.facishare.webpage.customer.api.service;


import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.arg.GetManMainChannelListArg;
import com.facishare.webpage.customer.api.model.result.GetManMainChannelListResult;

@RestResource(
        value = "WebMainChannelResource",
        desc = "web主频道",  // ignoreI18n
        contentType = "application/json"
)
public interface WebMainChannelManagerService {

    @POST(value = "rest/webMainChannelManager/webMainChannelList")
    GetManMainChannelListResult getManWebMainChannelList(@HeaderParam("x-fs-ei") String tenantId,
                                                         @HeaderParam("x-fs-locale") String language,
                                                         @HeaderParam("x-fs-userinfo") String userId,
                                                         @Body GetManMainChannelListArg arg);
}
