package com.facishare.webpage.customer.api.model;

import com.facishare.webpage.customer.api.model.core.WebMainChannelMenuVO;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/10 11:04 上午
 */
public interface SaveTenantMainChannel {

    @Data
    @Builder
    class Arg implements Serializable {
        private int enterpriseId;
        private String apiName;
        private String name;
        private List<Scope> scopeList;
        private Integer priorityLevel;
        private List<WebMainChannelMenuVO> mainChannelMenuVOList;
        private Integer version;
        private Boolean showAppName;
        private Boolean canCustom;
        private Boolean showMoreAppEntry;
        private boolean system;
    }

    @Data
    @Builder
    class Result implements Serializable {
        private Boolean success;
    }

}
