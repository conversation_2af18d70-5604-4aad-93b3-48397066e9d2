package com.facishare.webpage.customer.brush.model;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> Yu
 */
public interface OuterRoleBrush {

    @Data
    class Arg implements Serializable {
        private String appId;
        private List<Integer> tenantIds;
        private String fromRoleCode;       //原始的code
        private String toRoleCode;       //要替换的code

        public void valid() {
            if (StringUtils.isEmpty(appId) ||
                    CollectionUtils.isEmpty(tenantIds) ||
                    StringUtils.isEmpty(fromRoleCode) ||
                    StringUtils.isEmpty(toRoleCode)) {
                throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
            }
        }
    }

    @Data
    class Result implements Serializable {

        private List<OuterRoleBrushResultData> outerRoleBrushResultDataList;

        @Data
        public static class OuterRoleBrushResultData implements Serializable {
            private int tenantId;
            private int updateDataNum;
        }
    }

}
