package com.facishare.webpage.customer.api.model;

import java.io.Serializable;

/**
 * Created by zhangyu on 2020/8/18
 */
public enum DataSourceEnv implements Serializable {

    /**
     * 企业内
     */
    INNER(true),
    /**
     * 互联
     */
    CROSS(false);

    private boolean type;

    DataSourceEnv(boolean type) {
        this.type = type;
    }

    public boolean isType() {
        return type;
    }

    public static DataSourceEnv values(boolean type) {
        DataSourceEnv[] values = DataSourceEnv.values();
        for (DataSourceEnv dataSourceEnv : values) {
            if (dataSourceEnv.type == type) {
                return dataSourceEnv;
            }
        }
        return INNER;
    }
}
