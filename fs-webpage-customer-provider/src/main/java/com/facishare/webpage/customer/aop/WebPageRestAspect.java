package com.facishare.webpage.customer.aop;

import com.facishare.webpage.customer.api.exception.WebPageException;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
@Aspect
public class WebPageRestAspect {

    private static Logger logger = LoggerFactory.getLogger(WebPageRestAspect.class);

    @Before(value = "execution(* com.facishare.webpage.customer.rest..*.*(..))")
    public void beforeCall(JoinPoint joinPoint) {
        logger.info("before WebPageRestAspect method:{} Args:{}", joinPoint.getSignature().toShortString(), joinPoint.getArgs());
    }

    @AfterReturning(value = "execution(* com.facishare.webpage.customer.rest..*.*(..))", returning = "ret")
    public void afterReturn(JoinPoint joinPoint, Object ret) {
        logger.info("afterReturn, WebPageRestAspect:{} arg:{} Return:{}",
                joinPoint.getSignature().toShortString(), joinPoint.getArgs(), ret);
    }

    @AfterThrowing(value = "execution(* com.facishare.webpage.customer.rest..*.*(..))", throwing = "e")
    public void afterThrowing(JoinPoint joinPoint, Throwable e) throws Throwable {
        if (e instanceof WebPageException) {
            logger.warn("afterThrowing, WebPageRestAspect:{} ", joinPoint.getSignature().toShortString(), e);
        } else {
            logger.error("afterThrowing, WebPageRestAspect:{} ", joinPoint.getSignature().toShortString(), e);
            throw e;
        }
    }

}
