package com.facishare.webpage.customer.api.model.result;

import com.facishare.webpage.customer.api.model.PageWidget;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryCustomerPageWidgetsResult implements Serializable {

    private List<PageWidget> customerPageWidgets;


}
