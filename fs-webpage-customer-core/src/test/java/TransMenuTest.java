import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.core.Icon;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.api.model.core.Url;
import com.facishare.webpage.customer.core.model.Widget;

import java.util.List;
import java.util.stream.Collectors;

import static com.alibaba.fastjson.JSON.parseArray;

/**
 * Created by shecheng on 19/12/10.
 */
public class TransMenuTest {

    public static void main(String[] args) {
        String description = "[{\n" +
                "    \"action\": {\n" +
                "      \"url\": \"https://www.fxiaoke.com/h5app/ec-eservice-crm/#/engineer/list?_bury_apptemplate_source_field=appTemplate\"\n" +
                "    },\n" +
                "    \"bizId\": \"FSAID_989aa5_eservice_todo_work_order\",\n" +
                "    \"category\": \"App\",\n" +
                "    \"i18nKey\": \"wechat_union.service_management.sm_error_code.eservice_todo_work_order\",\n" +
                "    \"icon\": {\n" +
                "      \"path\": \"https://a3.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201903_19_ffdd48b311ba459b844d093caa4ccb4e.png&size=150_150&ea=appCenter\"\n" +
                "    },\n" +
                "    \"id\": \"45e1873af6c0455b9340bf6a8ecd8df8\",\n" +
                "    \"name\": \"待办工单\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"action\": {\n" +
                "      \"url\": \"https://www.fxiaoke.com/h5app/ec-eservice-crm/#/workorder/list?_bury_apptemplate_source_field=appTemplate\"\n" +
                "    },\n" +
                "    \"bizId\": \"FSAID_989aa5_eservice_work_order\",\n" +
                "    \"category\": \"App\",\n" +
                "    \"i18nKey\": \"wechat_union.service_management.sm_error_code.eservice_work_order_manage\",\n" +
                "    \"icon\": {\n" +
                "      \"path\": \"https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201903_19_c8402d395fa44533bab5d6d16dde8d4b.png&size=150_150&ea=appCenter\"\n" +
                "    },\n" +
                "    \"id\": \"fe3331194c954cfdb860149b40f32627\",\n" +
                "    \"name\": \"工单管理\"\n" +
                "  },\n" +
                " {\n" +
                "    \"action\": {\n" +
                "      \"url\": \"https://www.fxiaoke.com/h5app/cuservice-device-manage?_bury_apptemplate_source_field=appTemplate\"\n" +
                "    },\n" +
                "    \"bizId\": \"FSAID_989aa5_eservice_e_device\",\n" +
                "    \"category\": \"App\",\n" +
                "    \"i18nKey\": \"wechat_union.service_management.sm_error_code.eservice_device\",\n" +
                "    \"icon\": {\n" +
                "      \"path\": \"https://a6.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201903_19_9150f7ee69b741e8ab8aa3360f77b767.png&size=150_150&ea=appCenter\"\n" +
                "    },\n" +
                "    \"id\": \"215d0f51956147088d5e1b22fae51c6d\",\n" +
                "    \"name\": \"设备通\"\n" +
                "  },\n" +
                " {\n" +
                "    \"action\": {\n" +
                "      \"url\": \"https://www.fxiaoke.com/h5app/fs-workorder/#/exception/suspending-list?title=异常提醒&_bury_apptemplate_source_field=appTemplate\"\n" +
                "    },\n" +
                "    \"bizId\": \"FSAID_989aa5_eservice_e_exception\",\n" +
                "    \"category\": \"App\",\n" +
                "    \"i18nKey\": \"wechat_union.service_management.sm_error_code.eservice_exception_tips\",\n" +
                "    \"icon\": {\n" +
                "      \"path\": \"https://a6.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201903_19_9150f7ee69b741e8ab8aa3360f77b767.png&size=150_150&ea=appCenter\"\n" +
                "    },\n" +
                "    \"id\": \"fab0a8f7289248ddbdd9ab956d806c6f\",\n" +
                "    \"name\": \"异常提醒\"\n" +
                "  },\n" +
                "{\n" +
                "\t\"action\": {\n" +
                "\t\t\"url\": \"https://www.fxiaoke.com/h5app/ec-eservice-crm/#/workorderAccessories/receiveMaterialBillProduct/list?_bury_apptemplate_source_field=appTemplate\"\n" +
                "\t},\n" +
                "\t\"bizId\": \"FSAID_989aa5_eservice_e_employeeWarehouse\",\n" +
                "\t\"category\": \"App\",\n" +
                "\t\"i18nKey\": \"eservice.cases.sm_error_code.employee_warehouse\",\n" +
                "\t\"icon\": {\n" +
                "\t\t\"path\": \"https://a3.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201903_27_bd14651d4d214cf69cd74dfd05f47c6e.png&size=150_150&ea=appCenter\"\n" +
                "\t},\n" +
                "\t\"id\": \"d12e5b22-649a-44fe-8978-9e9e2a9f4e72\",\n" +
                "\t\"name\": \"员工个人库\"\n" +
                "},\n" +
                "   {\n" +
                "    \"action\": {\n" +
                "      \"url\": \"https://www.fxiaoke.com/h5app/ec-eservice-crm/#/workorderAccessories/receiveMaterialBill/list?_bury_apptemplate_source_field=appTemplate\"\n" +
                "    },\n" +
                "    \"bizId\": \"FSAID_989aa5_eservice_e_receiveMaterialBill\",\n" +
                "    \"category\": \"App\",\n" +
                "    \"i18nKey\": \"eservice.cases.sm_error_code.receive_material_bill\",\n" +
                "    \"icon\": {\n" +
                "      \"path\": \"https://a5.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201903_27_666939959520481a8761e7c80eef7326.png&size=150_150&ea=appCenter\"\n" +
                "    },\n" +
                "    \"id\": \"9bde5e95-75ab-4020-a5b1-3927079a8e3e\",\n" +
                "    \"name\": \"领料单\"\n" +
                "  },\n" +
                "{\n" +
                "    \"action\": {\n" +
                "      \"url\": \"https://www.fxiaoke.com/h5app/ec-eservice-crm/#/workorderAccessories/refundMaterialBill/list?_bury_apptemplate_source_field=appTemplate\"\n" +
                "    },\n" +
                "    \"bizId\": \"FSAID_989aa5_eservice_e_refundMaterialBill\",\n" +
                "    \"category\": \"App\",\n" +
                "    \"i18nKey\": \"eservice.cases.sm_error_code.refund_material_bill\",\n" +
                "    \"icon\": {\n" +
                "      \"path\": \"https://a6.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201903_27_f9148c529c3a483f92ee5cdca8afd790.png&size=150_150&ea=appCenter\"\n" +
                "    },\n" +
                "    \"id\": \"aac0ef5d-e84a-43b3-af92-aaa84c41fcd4\",\n" +
                "    \"name\": \"退料单\"\n" +
                "  },\n" +
                "{\n" +
                "\"action\": {\n" +
                "\"url\": \"https://www.fxiaoke.com/h5app/ec-eservice-crm/#/knowledgelib/list?scenaryType=appTemplate\"\n" +
                "},\n" +
                "\"bizId\": \"FSAID_989aa5_eservice_service_knowledge\",\n" +
                "\"category\": \"App\",\n" +
                "\"i18nKey\": \"wechat_union.service_management.sm_error_code.knowledge_base\",\n" +
                "\"icon\": {\n" +
                "\"path\": \"https://a6.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201909_02_484b3f09aead4da2bfea6cc40347e970.png&size=150_150&ea=appCenter\"\n" +
                "},\n" +
                "\"id\": \"b9656ce3999b424b81637e863756264c\",\n" +
                "\"name\": \"服务知识库\"\n" +
                "},\n" +
                "{\n" +
                "\"action\": {\n" +
                "\"url\": \"fs://CRM/objectList/DeviceObj?{\\\"apiName\\\":\\\"DeviceObj\\\",\\\"handlerSelector\\\":\\\"pushListVC:\\\",\\\"pushListVC\\\":\\\"DeviceObj\\\"}\"\n" +
                "},\n" +
                "\"bizId\": \"FSAID_989aa5_eservice_device_asset\",\n" +
                "\"category\": \"App\",\n" +
                "\"i18nKey\": \"wechat_union.service_management.sm_error_code.devices_assets\",\n" +
                "\"icon\": {\n" +
                "\"path\": \"https://a2.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201912_05_de0da4773a1b486b9a827a35d38eccc3.png&size=150_150&ea=appCenter\"\n" +
                "},\n" +
                "\"id\": \"8141ab1a183c47ee8253b31ff2bf7af2\",\n" +
                "\"name\": \"设备资产\"\n" +
                "},\n" +
                "{\n" +
                "\"action\": {\n" +
                "\"url\": \"fs://CRM/objectList/PreventiveMaintenanceObj?{\\\"apiName\\\":\\\"PreventiveMaintenanceObj\\\",\\\"handlerSelector\\\":\\\"pushListVC:\\\",\\\"pushListVC\\\":\\\"PreventiveMaintenanceObj\\\"}\"\n" +
                "},\n" +
                "\"bizId\": \"FSAID_989aa5_eservice_prevent_maintenance\",\n" +
                "\"category\": \"App\",\n" +
                "\"i18nKey\": \"wechat_union.service_management.sm_error_code.device_maintenance\",\n" +
                "\"icon\": {\n" +
                "\"path\": \"https://a6.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201912_05_efd54135973e4fe8aaa1025e9920e9b0.png&size=150_150&ea=appCenter\"\n" +
                "},\n" +
                "\"id\": \"9b93fe77d0264d1c935c5a603f889b7f\",\n" +
                "\"name\": \"设备维保\"\n" +
                "}\n" +
                "]";
        List<BizDescriptionEntity> descriptions = parseArray(description, BizDescriptionEntity.class);
        List<Menu> menus = descriptions.stream().map(bizDescription -> {
            Menu menu = new Menu();
            menu.setId(bizDescription.getId());
            menu.setName(bizDescription.getName());
            menu.setNameI18nKey(bizDescription.getI18nKey());
            Icon icon = new Icon();
            icon.setIcon_1(bizDescription.getIcon().getPath());
            menu.setIcon(icon);
            Url url = new Url();
            url.setWebUrl(bizDescription.getAction().getUrl());
            url.setAndroidUrl(bizDescription.getAction().getAndroid());
            url.setIOSUrl(bizDescription.getAction().getIos());
            menu.setUrl(url);
            return menu;
        }).collect(Collectors.toList());
//        System.out.println(JSONObject.toJSONString(menus));

        String components = "[\n" +
                "    {\n" +
                "        \"componentType\":1,\n" +
                "        \"id\":\"custom_component\",\n" +
                "        \"title\":\"自定义组件\",\n" +
                "        \"type\":0,\n" +
                "        \"parentId\":\"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"PS_BI_CUSTOMER\",\n" +
                "        \"id\":\"bi_customer\",\n" +
                "        \"title\":\"图表\",\n" +
                "        \"type\":2,\n" +
                "        \"parentId\":\"custom_component\",\n" +
                "        \"url\":\"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"PS_Filter\",\n" +
                "        \"id\":\"scenes\",\n" +
                "        \"title\":\"场景\",\n" +
                "        \"type\":103,\n" +
                "        \"parentId\":\"custom_component\",\n" +
                "        \"url\":\"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"PS_Tool\",\n" +
                "        \"id\":\"tool\",\n" +
                "        \"title\":\"工具\",\n" +
                "        \"type\":104,\n" +
                "        \"parentId\":\"custom_component\",\n" +
                "        \"url\":\"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":1,\n" +
                "        \"id\":\"preset_component\",\n" +
                "        \"title\":\"预设组件\",\n" +
                "        \"type\":0,\n" +
                "        \"parentId\":\"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":1,\n" +
                "        \"id\":\"jobs\",\n" +
                "        \"title\":\"工作\",\n" +
                "        \"type\":0,\n" +
                "        \"parentId\":\"preset_component\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"PS_Schedule\",\n" +
                "        \"id\":\"schedule\",\n" +
                "        \"title\":\"日程\",\n" +
                "        \"type\":101,\n" +
                "        \"parentId\":\"jobs\",\n" +
                "        \"url\":\"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"PS_Task\",\n" +
                "        \"id\":\"task\",\n" +
                "        \"title\":\"任务\",\n" +
                "        \"type\":102,\n" +
                "        \"parentId\":\"jobs\",\n" +
                "        \"url\":\"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":1,\n" +
                "        \"id\":\"chart\",\n" +
                "        \"title\":\"图表\",\n" +
                "        \"type\":0,\n" +
                "        \"parentId\":\"preset_component\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"BI_SaleReport\",\n" +
                "        \"id\":\"sale_report\",\n" +
                "        \"title\":\"销售简报\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/bi-report/6.1/index.html?fromapp=1#/salesbrief\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"BI_5a6871d037aa1b3d4865831d\",\n" +
                "        \"id\":\"payback_rate\",\n" +
                "        \"title\":\"回款率(回款/目标)\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/bi-chart/5.5/index.html?fromapp=1&appid=FSAID_5f5e533#/?from=crmindex&id=BI_5a6871d037aa1b3d4865831d\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"BI_5a68724c37aa1b3d48658337\",\n" +
                "        \"id\":\"prediction\",\n" +
                "        \"title\":\"预测\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/bi-chart/5.5/index.html?fromapp=1&appid=FSAID_5f5e533#/?from=crmindex&id=BI_5a68724c37aa1b3d48658337\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"BI_5a68730437aa1b3d48658351\",\n" +
                "        \"id\":\"order_statistics\",\n" +
                "        \"title\":\"订单统计\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/bi-chart/5.5/index.html?fromapp=1&appid=FSAID_5f5e533#/?from=crmindex&id=BI_5a68730437aa1b3d48658351\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"BI_5a68738337aa1b3d4865836e\",\n" +
                "        \"id\":\"payment_statistics\",\n" +
                "        \"title\":\"回款统计\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/bi-chart/5.5/index.html?fromapp=1&appid=FSAID_5f5e533#/?from=crmindex&id=BI_5a68738337aa1b3d4865836e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"BI_5a6873f237aa1b3d4865838b\",\n" +
                "        \"id\":\"refund_statistics\",\n" +
                "        \"title\":\"退款统计\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/bi-chart/5.5/index.html?fromapp=1&appid=FSAID_5f5e533#/?from=crmindex&id=BI_5a6873f237aa1b3d4865838b\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"PS_CluesInto\",\n" +
                "        \"id\":\"cluesinfo\",\n" +
                "        \"title\":\"线索转化\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/bi-chart/5.5/index.html?fromapp=1&appid=FSAID_5f5e533#/?from=crmindex&id=PS_CluesInto\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"BI_595e213437aa1badec9778e6\",\n" +
                "        \"id\":\"sale_analysis\",\n" +
                "        \"title\":\"销售阶段转化分析\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/bi-chart/5.5/index.html?fromapp=1&appid=FSAID_5f5e533#/?from=crmindex&id=BI_595e213437aa1badec9778e6\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"BI_Rank\",\n" +
                "        \"id\":\"leaderboard\",\n" +
                "        \"title\":\"排行榜\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/bi-report/6.1/index.html?fromapp=1#/rank\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"BI_SalesAssistant\",\n" +
                "        \"id\":\"sales_assistant\",\n" +
                "        \"title\":\"销售助手\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/salesassist/index.html#/index\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"BI_58acfc2537aa1badf31d169a\",\n" +
                "        \"id\":\"sales_funnel\",\n" +
                "        \"title\":\"销售漏斗（商机金额）\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/bi-chart/5.5/index.html?fromapp=1&appid=FSAID_5f5e533#/?from=crmindex&id=BI_58acfc2537aa1badf31d169a\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"BI_5af92d6deb8caafcfbbbc6c6\",\n" +
                "        \"id\":\"target_completion_rate\",\n" +
                "        \"title\":\"目标完成率\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/bi-chart/5.5/index.html?fromapp=1&appid=FSAID_5f5e533#/?from=crmindex&id=BI_5af92d6deb8caafcfbbbc6c6\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"BI_5af92d16eb8caafcfbbbc6ab\",\n" +
                "        \"id\":\"employee_ranking\",\n" +
                "        \"title\":\"员工目标完成率排行\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/bi-chart/5.5/index.html?fromapp=1&appid=FSAID_5f5e533#/?from=crmindex&id=BI_5af92d16eb8caafcfbbbc6ab\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"BI_5af92cf0eb8caafcfbbbc690\",\n" +
                "        \"id\":\"annual_target_completion\",\n" +
                "        \"title\":\"年度目标完成情况\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/bi-chart/5.5/index.html?fromapp=1&appid=FSAID_5f5e533#/?from=crmindex&id=BI_5af92cf0eb8caafcfbbbc690\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"componentType\":2,\n" +
                "        \"cardId\":\"BI_5af92cd8eb8caafcfbbbc675\",\n" +
                "        \"id\":\"department_goal_completion\",\n" +
                "        \"title\":\"部门目标完成情况\",\n" +
                "        \"type\":1,\n" +
                "        \"parentId\":\"chart\",\n" +
                "        \"url\":\" https://www.fxiaoke.com/fsh5/bi-chart/5.5/index.html?fromapp=1&appid=FSAID_5f5e533#/?from=crmindex&id=BI_5af92cd8eb8caafcfbbbc675\"\n" +
                "    }\n" +
                "]";
        List<JSONObject> widgetsObject = JSONObject.parseArray(components, JSONObject.class);

        List<Widget> widgets = widgetsObject.stream().filter(jsonObject -> jsonObject.getInteger("type") > 0).map(jsonObject -> {
            Widget widget = new Widget();
            widget.setId(jsonObject.getString("id"));
            widget.setName(jsonObject.getString("title"));
            widget.setCardId(jsonObject.getString("cardId"));
            widget.setWidgetType(jsonObject.getIntValue("type"));
            return widget;
        }).collect(Collectors.toList());
        System.out.println(JSONObject.toJSONString(widgets));


    }
}
