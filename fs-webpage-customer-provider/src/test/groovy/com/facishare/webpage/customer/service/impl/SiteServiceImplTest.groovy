package com.facishare.webpage.customer.service.impl

import com.facishare.webpage.customer.api.constant.PaaSStatus
import com.facishare.webpage.customer.api.model.User
import com.facishare.webpage.customer.constant.MenuStatus
import com.facishare.webpage.customer.controller.model.I18nInfoDTO
import com.facishare.webpage.customer.dao.*
import com.facishare.webpage.customer.dao.entity.*
import com.facishare.webpage.customer.processor.siteDiffProcessor.factory.SiteDiffFactory
import com.facishare.webpage.customer.processor.siteDiffProcessor.result.DiffResults
import com.facishare.webpage.customer.processor.siteDiffProcessor.result.EntityDiffResult
import com.facishare.webpage.customer.processor.siteDiffProcessor.service.*
import com.facishare.webpage.customer.service.SiteI18nQueryService
import com.facishare.webpage.customer.util.ReferenceTargetType
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class SiteServiceImplTest extends Specification {

    SiteServiceImpl siteService
    SiteEntityDao siteEntityDao = Mock()
    ThemeLayoutEntityDao themeLayoutEntityDao = Mock()
    TenantMenuDao tenantMenuDao = Mock()
    HomePageLayoutDao homePageLayoutDao = Mock()
    ThemeStyleEntityDao themeStyleEntityDao = Mock()
    SiteConfigEntityDao siteConfigEntityDao = Mock()
    ReferenceEntityDao referenceEntityDao = Mock()
    SiteI18nQueryService siteI18nQueryService = Mock()
    SiteDiffFactory siteDiffFactory = Mock()
    
    // Mock processors
    ThemeStyleDiffProcessor themeStyleDiffProcessor = Mock()
    ThemeLayoutDiffProcessor themeLayoutDiffProcessor = Mock()
    TenantMenuDiffProcessor tenantMenuDiffProcessor = Mock()
    HomePageLayoutDiffProcessor homePageLayoutDiffProcessor = Mock()
    ReferenceDiffProcessor referenceDiffProcessor = Mock()

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        siteService = new SiteServiceImpl()
        siteService.siteEntityDao = siteEntityDao
        siteService.themeLayoutEntityDao = themeLayoutEntityDao
        siteService.tenantMenuDao = tenantMenuDao
        siteService.homePageLayoutDao = homePageLayoutDao
        siteService.themeStyleEntityDao = themeStyleEntityDao
        siteService.siteConfigEntityDao = siteConfigEntityDao
        siteService.referenceEntityDao = referenceEntityDao
        siteService.siteI18nQueryService = siteI18nQueryService
        siteService.siteDiffFactory = siteDiffFactory
        
        // Setup mock processors
        siteDiffFactory.getThemeStyleProcessor() >> themeStyleDiffProcessor
        siteDiffFactory.getThemeLayoutProcessor() >> themeLayoutDiffProcessor
        siteDiffFactory.getMenuProcessor() >> tenantMenuDiffProcessor
        siteDiffFactory.getPageProcessor() >> homePageLayoutDiffProcessor
        siteDiffFactory.getReferenceProcessor() >> referenceDiffProcessor
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试publishSitePages方法的正常场景，包含所有实体类型的新增、更新、删除操作
     */
    def "publishSitePagesTest正常场景"() {
        given:
        def user = createTestUser()
        def siteEntity = createTestSiteEntity()
        def version = 1L
        def clientType = "web"

        // 准备新的实体列表
        def newThemeLayoutEntityList = createTestThemeLayoutEntities()
        def newMenuEntityList = createTestTenantMenuEntities()
        def newPageEntityList = createTestHomePageLayoutEntities()
        def newThemeStyleEntityList = createTestThemeStyleEntities()
        def siteConfigEntity = createTestSiteConfigEntity()
        def fileReferenceEntityList = createTestFileReferenceEntities()
        def i18nReferenceEntityList = createTestI18nReferenceEntities()
        def i18nInfoList = createTestI18nInfoList()

        // 准备diff结果
        def themeStyleDiff = createMockEntityDiffResult(["style1"], ["style2"], ["style3"])
        def themeLayoutDiff = createMockEntityDiffResult(["layout1"], ["layout2"], ["layout3"])
        def menuDiff = createMockEntityDiffResult(["menu1"], ["menu2"], ["menu3"])
        def pageDiff = createMockEntityDiffResult(["page1"], ["page2"], ["page3"])
        def fileDiff = createMockEntityDiffResult(["file1"], ["file2"], ["file3"])
        def i18nDiff = createMockEntityDiffResult(["i18n1"], ["i18n2"], ["i18n3"])
        
        def diffResults = new DiffResults(themeStyleDiff, themeLayoutDiff, menuDiff, pageDiff, fileDiff, i18nDiff)

        // Mock diff processors
        themeStyleDiffProcessor.processDiff(user, siteEntity, newThemeStyleEntityList, clientType, null, true) >> themeStyleDiff
        themeLayoutDiffProcessor.processDiff(user, siteEntity, newThemeLayoutEntityList, clientType, null, true) >> themeLayoutDiff
        tenantMenuDiffProcessor.processDiff(user, siteEntity, newMenuEntityList, clientType, null, true) >> menuDiff
        homePageLayoutDiffProcessor.processDiff(user, siteEntity, newPageEntityList, clientType, null, true) >> pageDiff
        referenceDiffProcessor.processDiff(user, siteEntity, fileReferenceEntityList, clientType, ReferenceTargetType.FILE.code, true) >> fileDiff
        referenceDiffProcessor.processDiff(user, siteEntity, i18nReferenceEntityList, clientType, ReferenceTargetType.I18N.code, true) >> i18nDiff

        // Mock findDraftReferenceEntityList
        referenceEntityDao.findReferenceByCondition(_) >> []

        when:
        siteService.publishSitePages(user, siteEntity, version, newThemeLayoutEntityList, newMenuEntityList,
                newPageEntityList, newThemeStyleEntityList, siteConfigEntity, fileReferenceEntityList,
                i18nReferenceEntityList, i18nInfoList, clientType)

        then:
        // 验证各种DAO操作被正确调用
        1 * themeStyleEntityDao.updateStatus(user, themeStyleDiff.toDelete, PaaSStatus.delete)
        1 * themeStyleEntityDao.batchUpdate(user, themeStyleDiff.toUpdate)
        1 * themeStyleEntityDao.batchSave(user, themeStyleDiff.toCreate)

        1 * themeLayoutEntityDao.updateStatus(user, themeLayoutDiff.toDelete, PaaSStatus.delete)
        1 * themeLayoutEntityDao.batchUpdate(user, themeLayoutDiff.toUpdate)
        1 * themeLayoutEntityDao.batchSave(user, themeLayoutDiff.toCreate)

        1 * tenantMenuDao.updateStatus(user, menuDiff.toDelete, MenuStatus.deleteStatus)
        1 * tenantMenuDao.batchUpdate(user, menuDiff.toUpdate)
        1 * tenantMenuDao.batchSave(user, menuDiff.toCreate)

        1 * homePageLayoutDao.deleteByIds(user, pageDiff.toDelete)
        1 * homePageLayoutDao.batchUpdate(user, pageDiff.toUpdate)
        1 * homePageLayoutDao.batchSave(user, pageDiff.toCreate)

        1 * referenceEntityDao.batchDelete(_)
        1 * referenceEntityDao.batchUpdate(user, _)
        1 * referenceEntityDao.batchSave(_)

        1 * siteConfigEntityDao.save(user, siteConfigEntity)
        1 * siteEntityDao.publishSite(user, siteEntity.apiName, version, clientType)
        1 * siteI18nQueryService.syncI18nInfoForSite(user.tenantId, null, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试publishSitePages方法当旧数据为空时的场景
     */
    def "publishSitePagesTest旧数据为空场景"() {
        given:
        def user = createTestUser()
        def siteEntity = createTestSiteEntity()
        def version = 1L
        def clientType = "web"

        def newThemeLayoutEntityList = createTestThemeLayoutEntities()
        def newMenuEntityList = createTestTenantMenuEntities()
        def newPageEntityList = createTestHomePageLayoutEntities()
        def newThemeStyleEntityList = createTestThemeStyleEntities()
        def siteConfigEntity = createTestSiteConfigEntity()
        def fileReferenceEntityList = createTestFileReferenceEntities()
        def i18nReferenceEntityList = createTestI18nReferenceEntities()
        def i18nInfoList = createTestI18nInfoList()

        // 准备空的diff结果（只有新增，没有更新和删除）
        def themeStyleDiff = createMockEntityDiffResult(["style1"], [], [])
        def themeLayoutDiff = createMockEntityDiffResult(["layout1"], [], [])
        def menuDiff = createMockEntityDiffResult(["menu1"], [], [])
        def pageDiff = createMockEntityDiffResult(["page1"], [], [])
        def fileDiff = createMockEntityDiffResult(["file1"], [], [])
        def i18nDiff = createMockEntityDiffResult(["i18n1"], [], [])

        // Mock diff processors
        themeStyleDiffProcessor.processDiff(user, siteEntity, newThemeStyleEntityList, clientType, null, true) >> themeStyleDiff
        themeLayoutDiffProcessor.processDiff(user, siteEntity, newThemeLayoutEntityList, clientType, null, true) >> themeLayoutDiff
        tenantMenuDiffProcessor.processDiff(user, siteEntity, newMenuEntityList, clientType, null, true) >> menuDiff
        homePageLayoutDiffProcessor.processDiff(user, siteEntity, newPageEntityList, clientType, null, true) >> pageDiff
        referenceDiffProcessor.processDiff(user, siteEntity, fileReferenceEntityList, clientType, ReferenceTargetType.FILE.code, true) >> fileDiff
        referenceDiffProcessor.processDiff(user, siteEntity, i18nReferenceEntityList, clientType, ReferenceTargetType.I18N.code, true) >> i18nDiff

        // Mock findDraftReferenceEntityList
        referenceEntityDao.findReferenceByCondition(_) >> []

        when:
        siteService.publishSitePages(user, siteEntity, version, newThemeLayoutEntityList, newMenuEntityList,
                newPageEntityList, newThemeStyleEntityList, siteConfigEntity, fileReferenceEntityList,
                i18nReferenceEntityList, i18nInfoList, clientType)

        then:
        // 验证只有保存操作，没有更新和删除操作
        1 * themeStyleEntityDao.updateStatus(user, [], PaaSStatus.delete)
        1 * themeStyleEntityDao.batchUpdate(user, [])
        1 * themeStyleEntityDao.batchSave(user, themeStyleDiff.toCreate)

        1 * themeLayoutEntityDao.updateStatus(user, [], PaaSStatus.delete)
        1 * themeLayoutEntityDao.batchUpdate(user, [])
        1 * themeLayoutEntityDao.batchSave(user, themeLayoutDiff.toCreate)

        1 * tenantMenuDao.updateStatus(user, [], MenuStatus.deleteStatus)
        1 * tenantMenuDao.batchUpdate(user, [])
        1 * tenantMenuDao.batchSave(user, menuDiff.toCreate)

        1 * homePageLayoutDao.deleteByIds(user, [])
        1 * homePageLayoutDao.batchUpdate(user, [])
        1 * homePageLayoutDao.batchSave(user, pageDiff.toCreate)

        1 * referenceEntityDao.batchDelete(_)
        1 * referenceEntityDao.batchUpdate(user, _)
        1 * referenceEntityDao.batchSave(_)

        1 * siteConfigEntityDao.save(user, siteConfigEntity)
        1 * siteEntityDao.publishSite(user, siteEntity.apiName, version, clientType)
        1 * siteI18nQueryService.syncI18nInfoForSite(user.tenantId, null, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试publishSitePages方法当siteConfigEntity为null时的场景
     */
    def "publishSitePagesTestSiteConfig为空场景"() {
        given:
        def user = createTestUser()
        def siteEntity = createTestSiteEntity()
        def version = 1L
        def clientType = "web"

        def newThemeLayoutEntityList = []
        def newMenuEntityList = []
        def newPageEntityList = []
        def newThemeStyleEntityList = []
        def siteConfigEntity = null
        def fileReferenceEntityList = []
        def i18nReferenceEntityList = []
        def i18nInfoList = []

        // 准备空的diff结果
        def emptyDiff = createMockEntityDiffResult([], [], [])

        // Mock diff processors
        themeStyleDiffProcessor.processDiff(user, siteEntity, newThemeStyleEntityList, clientType, null,null) >> emptyDiff
        themeLayoutDiffProcessor.processDiff(user, siteEntity, newThemeLayoutEntityList, clientType, null, null) >> emptyDiff
        tenantMenuDiffProcessor.processDiff(user, siteEntity, newMenuEntityList, clientType, null, null) >> emptyDiff
        homePageLayoutDiffProcessor.processDiff(user, siteEntity, newPageEntityList, clientType, null, null) >> emptyDiff
        referenceDiffProcessor.processDiff(user, siteEntity, fileReferenceEntityList, clientType, ReferenceTargetType.FILE.code, true) >> emptyDiff
        referenceDiffProcessor.processDiff(user, siteEntity, i18nReferenceEntityList, clientType, ReferenceTargetType.I18N.code, true) >> emptyDiff

        // Mock findDraftReferenceEntityList
        referenceEntityDao.findReferenceByCondition(_) >> []

        when:
        siteService.publishSitePages(user, siteEntity, version, newThemeLayoutEntityList, newMenuEntityList,
                newPageEntityList, newThemeStyleEntityList, siteConfigEntity, fileReferenceEntityList,
                i18nReferenceEntityList, i18nInfoList, clientType)

        then:
        // 验证siteConfigEntity为null时不调用save方法
        0 * siteConfigEntityDao.save(_, _)
        1 * siteEntityDao.publishSite(user, siteEntity.apiName, version, clientType)
        1 * siteI18nQueryService.syncI18nInfoForSite(user.tenantId, null, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试publishSitePages方法的边界值场景，包括各种边界参数
     */
    def "publishSitePagesTest边界值场景"() {
        given:
        def user = new User(tenantId: tenantId, userId: userId)
        def siteEntity = new SiteEntity(apiName: siteApiName, appId: appId)
        def newThemeLayoutEntityList = []
        def newMenuEntityList = []
        def newPageEntityList = []
        def newThemeStyleEntityList = []
        def siteConfigEntity = null
        def fileReferenceEntityList = []
        def i18nReferenceEntityList = []
        def i18nInfoList = []

        // 准备空的diff结果
        def emptyDiff = createMockEntityDiffResult([], [], [])

        // Mock diff processors
        themeStyleDiffProcessor.processDiff(user, siteEntity, newThemeStyleEntityList, clientType, null, true) >> emptyDiff
        themeLayoutDiffProcessor.processDiff(user, siteEntity, newThemeLayoutEntityList, clientType, null, true) >> emptyDiff
        tenantMenuDiffProcessor.processDiff(user, siteEntity, newMenuEntityList, clientType, null, true) >> emptyDiff
        homePageLayoutDiffProcessor.processDiff(user, siteEntity, newPageEntityList, clientType, null, true) >> emptyDiff
        referenceDiffProcessor.processDiff(user, siteEntity, fileReferenceEntityList, clientType, ReferenceTargetType.FILE.code, true) >> emptyDiff
        referenceDiffProcessor.processDiff(user, siteEntity, i18nReferenceEntityList, clientType, ReferenceTargetType.I18N.code, true) >> emptyDiff

        // Mock findDraftReferenceEntityList
        referenceEntityDao.findReferenceByCondition(_) >> []

        when:
        siteService.publishSitePages(user, siteEntity, version, newThemeLayoutEntityList, newMenuEntityList,
                newPageEntityList, newThemeStyleEntityList, siteConfigEntity, fileReferenceEntityList,
                i18nReferenceEntityList, i18nInfoList, clientType)

        then:
        1 * siteEntityDao.publishSite(user, siteApiName, expectedVersion, clientType)

        where:
        tenantId | userId | siteApiName | appId  | version | clientType | expectedVersion
        1        | 1      | "site1"     | "app1" | 1L      | "web"      | 1L
        0        | 0      | "site1"     | "app1" | 0L      | "web"      | 0L
        -1       | -1     | "site1"     | "app1" | null    | "web"      | 0L
        999999   | 999999 | ""          | ""     | 999999L | "mobile"   | 999999L
        1        | 1      | "site1"     | "app1" | 1L      | ""         | 1L
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试publishSitePages方法的异常场景，验证各种异常情况的处理
     */
    def "publishSitePagesError异常场景"() {
        given:
        def user = createTestUser()
        def siteEntity = createTestSiteEntity()
        def version = 1L
        def clientType = "web"

        def newThemeLayoutEntityList = []
        def newMenuEntityList = []
        def newPageEntityList = []
        def newThemeStyleEntityList = []
        def siteConfigEntity = null
        def fileReferenceEntityList = []
        def i18nReferenceEntityList = []
        def i18nInfoList = []

        // Mock diff processor 抛出异常
        themeStyleDiffProcessor.processDiff(_, _, _, _, _) >> { throw new RuntimeException("Database error") }

        when:
        siteService.publishSitePages(user, siteEntity, version, newThemeLayoutEntityList, newMenuEntityList,
                newPageEntityList, newThemeStyleEntityList, siteConfigEntity, fileReferenceEntityList,
                i18nReferenceEntityList, i18nInfoList, clientType)

        then:
        thrown(RuntimeException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试publishSitePages方法当i18nInfoList为空时不调用同步方法
     */
    def "publishSitePagesTestI18n为空场景"() {
        given:
        def user = createTestUser()
        def siteEntity = createTestSiteEntity()
        def version = 1L
        def clientType = "web"

        def newThemeLayoutEntityList = []
        def newMenuEntityList = []
        def newPageEntityList = []
        def newThemeStyleEntityList = []
        def siteConfigEntity = null
        def fileReferenceEntityList = []
        def i18nReferenceEntityList = []
        def i18nInfoList = [] // 空的i18n列表

        // 准备空的diff结果
        def emptyDiff = createMockEntityDiffResult([], [], [])

        // Mock diff processors
        themeStyleDiffProcessor.processDiff(user, siteEntity, newThemeStyleEntityList, clientType, null, true) >> emptyDiff
        themeLayoutDiffProcessor.processDiff(user, siteEntity, newThemeLayoutEntityList, clientType, null, true) >> emptyDiff
        tenantMenuDiffProcessor.processDiff(user, siteEntity, newMenuEntityList, clientType, null, true) >> emptyDiff
        homePageLayoutDiffProcessor.processDiff(user, siteEntity, newPageEntityList, clientType, null, true) >> emptyDiff
        referenceDiffProcessor.processDiff(user, siteEntity, fileReferenceEntityList, clientType, ReferenceTargetType.FILE.code, true) >> emptyDiff
        referenceDiffProcessor.processDiff(user, siteEntity, i18nReferenceEntityList, clientType, ReferenceTargetType.I18N.code, true) >> emptyDiff

        // Mock findDraftReferenceEntityList
        referenceEntityDao.findReferenceByCondition(_) >> []

        when:
        siteService.publishSitePages(user, siteEntity, version, newThemeLayoutEntityList, newMenuEntityList,
                newPageEntityList, newThemeStyleEntityList, siteConfigEntity, fileReferenceEntityList,
                i18nReferenceEntityList, i18nInfoList, clientType)

        then:
        // 验证当i18nInfoList为空时不调用同步方法
        0 * siteI18nQueryService.syncI18nInfoForSite(_, _, _, _)
        1 * siteEntityDao.publishSite(user, siteEntity.apiName, version, clientType)
    }

    // 辅助方法创建测试数据
    private User createTestUser() {
        return new User(tenantId: 12345, userId: 67890)
    }

    private SiteEntity createTestSiteEntity() {
        return new SiteEntity(apiName: "testSite", appId: "testApp")
    }

    private List<ThemeLayoutEntity> createTestThemeLayoutEntities() {
        return [
                new ThemeLayoutEntity(id: "1", apiName: "layout1", name: "Layout 1"),
                new ThemeLayoutEntity(id: "2", apiName: "layout2", name: "Layout 2")
        ]
    }

    private List<TenantMenuEntity> createTestTenantMenuEntities() {
        return [
                new TenantMenuEntity(id: "1", apiName: "menu1", name: "Menu 1"),
                new TenantMenuEntity(id: "2", apiName: "menu2", name: "Menu 2")
        ]
    }

    private List<HomePageLayoutEntity> createTestHomePageLayoutEntities() {
        return [
                new HomePageLayoutEntity(layoutId: "1", apiName: "page1", name: "Page 1"),
                new HomePageLayoutEntity(layoutId: "2", apiName: "page2", name: "Page 2")
        ]
    }

    private List<ThemeStyleEntity> createTestThemeStyleEntities() {
        return [
                new ThemeStyleEntity(id: "1", apiName: "style1", name: "Style 1"),
                new ThemeStyleEntity(id: "2", apiName: "style2", name: "Style 2")
        ]
    }

    private SiteConfigEntity createTestSiteConfigEntity() {
        return new SiteConfigEntity(id: "1", siteApiName: "testSite")
    }

    private List<ReferenceEntity> createTestFileReferenceEntities() {
        return [
                new ReferenceEntity(id: "1", targetType: ReferenceTargetType.FILE.code, targetId: "file1", sourceId: "source1", sourceType: "type1"),
                new ReferenceEntity(id: "2", targetType: ReferenceTargetType.FILE.code, targetId: "file2", sourceId: "source2", sourceType: "type2")
        ]
    }

    private List<ReferenceEntity> createTestI18nReferenceEntities() {
        return [
                new ReferenceEntity(id: "3", targetType: ReferenceTargetType.I18N.code, targetId: "i18n1", sourceId: "source1", sourceType: "type1"),
                new ReferenceEntity(id: "4", targetType: ReferenceTargetType.I18N.code, targetId: "i18n2", sourceId: "source2", sourceType: "type2")
        ]
    }

    private List<I18nInfoDTO> createTestI18nInfoList() {
        return [
                new I18nInfoDTO(key: "key1", sourceId: "source1"),
                new I18nInfoDTO(key: "key2", sourceId: "source2")
        ]
    }

    private EntityDiffResult createMockEntityDiffResult(List toCreate, List toUpdate, List toDelete) {
        return EntityDiffResult.builder()
                .toCreate(toCreate)
                .toUpdate(toUpdate)
                .toDelete(toDelete)
                .build()
    }
} 