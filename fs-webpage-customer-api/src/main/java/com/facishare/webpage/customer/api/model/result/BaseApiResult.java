package com.facishare.webpage.customer.api.model.result;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/14
 */
@Data
public class BaseApiResult<T> implements Serializable {
    public static final int SUCCESS_CODE = 200;
    public static final int EXCEPTION_CODE = 400;
    public static final int FAIL_CODE = 500;

    private int code;
    private String msg;
    private T content;

    public BaseApiResult() {
    }

    public BaseApiResult(int code, String msg, T content) {
        this.code = code;
        this.msg = msg;
        this.content = content;
    }

    public static BaseApiResult success() {
        return new BaseApiResult(SUCCESS_CODE, "Success", null);
    }

    public static BaseApiResult success(Object content) {
        return new BaseApiResult(SUCCESS_CODE, "Success", content);
    }

    public static BaseApiResult fail(String msg) {
        return new BaseApiResult(FAIL_CODE, msg, null);
    }

    public static BaseApiResult exception(String exception) {
        return new BaseApiResult(EXCEPTION_CODE, exception, null);
    }
}
