package com.facishare.webpage.customer.controller.model.arg.cms;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.List;

/**
 * 移动资源参数
 */
@Data
public class MoveResourceArg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 移动资源列表
     */
    private List<MoveResourceItem> moveResourceList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MoveResourceItem {
        /**
         * 文件ApiName
         */
        private String apiName;

        /**
         * 目标父文件夹ApiName，为空表示移动到根目录
         */
        private String newParentApiName;
        private String nodeType;
        private String parentApiName;
    }
}
