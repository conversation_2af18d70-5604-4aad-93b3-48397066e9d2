package com.facishare.webpage.customer.api.console.result;

import com.facishare.webpage.customer.api.console.item.TenantMenuItem;
import com.facishare.webpage.customer.api.console.item.UserMenuItem;
import io.protostuff.Tag;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QueryMenuResult implements Serializable {
    @Tag(1)
    private List<TenantMenuItem> tenantMenuList;
    @Tag(2)
    private List<UserMenuItem> userMenuList;
}
