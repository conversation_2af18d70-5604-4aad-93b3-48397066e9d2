package com.facishare.webpage.customer.api.model;

import com.facishare.webpage.customer.api.model.MainChannelMenuVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2020/11/19
 */
@Data
public class UserWebMainChannelVO implements Serializable {
    /**
     * apiName
     */
    private String apiName;

    /**
     * 是否显示应用名称
     */
    private Boolean showAppName;
    /**
     * 是否可以自定义
     */
    private Boolean canCustom;
    /**
     * 是否显示更多应用入口
     */
    private Boolean showMoreAppEntry;
    /**
     * 主导航数据
     */
    private List<MainChannelMenuVO> mainChannelMenuVOList;
    /**
     * 版本
     */
    private Integer version;

}
