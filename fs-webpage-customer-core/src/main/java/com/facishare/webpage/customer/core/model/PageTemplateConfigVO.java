package com.facishare.webpage.customer.core.model;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2020/1/9
 */
@Data
public class PageTemplateConfigVO implements Serializable {
    /**
     * 默认名称
     */
    private String defaultName;
    /**
     * 是否包含菜单
     */
    private boolean hasMenu;
    /**
     * 是否是互联
     */
    private boolean crossApp;
    /**
     * 前端是要走什么框架
     */
    private int applicationType;
    /**
     * 多语key
     */
    private String nameI18nKey;
    /**
     * 是否有工具栏
     */
    private Boolean hasUtilityBarType = Boolean.FALSE;

    /**
     * 是否包含筛选器
     */
    private Boolean hasFilter;


}
