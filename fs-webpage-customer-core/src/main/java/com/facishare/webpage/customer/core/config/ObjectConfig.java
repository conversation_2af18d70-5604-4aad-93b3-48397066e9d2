package com.facishare.webpage.customer.core.config;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.core.Icon;
import com.facishare.webpage.customer.api.model.core.IndexIcon;
import com.facishare.webpage.customer.api.model.core.PreObject;
import com.facishare.webpage.customer.api.model.core.Url;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/12/18.
 */
public class ObjectConfig {
    private static final Logger logger = LoggerFactory.getLogger(ObjectConfig.class);

    private Map<String, PreObject> preObjectMap = new HashMap<>();
    private Map<Integer, IndexIcon> indexIconMap = new HashMap<>();
    private List<String> showObjectApiNames = Lists.newArrayList();

    private String icon_1 = "";
    private String icon_2 = "";

    public Map<String, PreObject> getPreObjectMap() {
        return preObjectMap;
    }

    public Map<Integer, IndexIcon> getIndexIconMap() {
        return indexIconMap;
    }

    private static final String defaultApiName = "default";

    /**
     * @see IconPathConfig#init()
     */
    @Autowired
    private IconPathConfig iconPathConfig;

    public void init() {
        ConfigFactory.getInstance().getConfig("fs-webpage-customer-preObject", iConfig -> {
            List<PreObject> preObjects = JSONObject.parseArray(iConfig.getString(), PreObject.class);
            if (CollectionUtils.isNotEmpty(preObjects)) {
                preObjectMap = preObjects.stream().collect(Collectors.toMap(PreObject::getApiName, Function.identity(), (key1, key2) -> key2));
                List<String> apiNames = preObjects.stream().distinct().filter(preObject -> preObject.isShow()).map(preObject -> preObject.getApiName()).collect(Collectors.toList());
                showObjectApiNames = apiNames;
                logger.info("showObjectApiNames:{}", showObjectApiNames);
            }
        });

        ConfigFactory.getInstance().getConfig("fs-webpage-customer-indexIcon", iConfig -> {
            List<IndexIcon> preObjects = JSONObject.parseArray(iConfig.getString(), IndexIcon.class);
            logger.info("fs-webpage-customer-indexIcon info ={}", preObjects);
            if (CollectionUtils.isNotEmpty(preObjects)) {
                indexIconMap = preObjects.stream().collect(Collectors.toMap(IndexIcon::getIndex, Function.identity(), (key1, key2) -> key2));
            }
            logger.info("icon_index info={}", indexIconMap);
        });

        ConfigFactory.getInstance().getConfig("fs-webpage-customer-defaultIcon", iConfig -> {
            Map<String, String> map = iConfig.getAll();
            icon_1 = map.get("icon_1");
            icon_2 = map.get("icon_2");
        });
        logger.info("init ObjectConfig over");
    }

    /**
     * 根据 apiName 获取 Icon，并处理图标 <br>
     * 1、如果 icon_1 为空，从 fs-webpage-svg-icon-config
     * 2、如果 icon_2 为空，从 fs-webpage-png-icon-config
     * 3、如果 iconIndex 不为空，设置 fxIcon 为 fx-icon-obj-app{iconIndex+1}
     * @param apiName 对象 APIName
     * @return Icon 对象
     */
    public Icon getIconByApiName(String apiName) {
        PreObject preObject = preObjectMap.get(apiName);
        if (preObject == null || preObject.getIcon() == null) {
            return buildDefaultIcon();
        }
        Icon icon = preObject.getIcon();
        if (icon.getIcon_1() == null) {
            String svgIconPath = iconPathConfig.getSvgIconPath(apiName);
            // 如果有 svgIconPath 使用 svgIconPath 否则使用 icon_1
            icon.setIcon_1(StringUtils.isEmpty(svgIconPath) ? icon_1 : svgIconPath);
        }
        if (icon.getIcon_2() == null) {
            String pngIconPath = iconPathConfig.getPngIconPath(apiName);
            icon.setIcon_2(StringUtils.isEmpty(pngIconPath) ? icon_2 : pngIconPath);
        }
        if (preObject.getIconIndex() != null) {
            // 如果 iconIndex 不为空，则设置 fxIcon 为 fx-icon-obj-app{iconIndex+1}
            icon.setFxIcon("fx-icon-obj-app" + (1 + preObject.getIconIndex()));
        }
        return icon;
    }
    public Integer getIconIndexByApiName(String apiName, Integer iconIndex) {
        PreObject preObject = preObjectMap.get(apiName);
        if (preObject != null && preObject.getIconIndex() != null) {
            return preObject.getIconIndex();
        }
        if (iconIndex == null) {
            return 0;
        }
        return iconIndex;
    }

    public Icon getIconByIndex(Integer index) {
        IndexIcon indexIcon = indexIconMap.get(index);
        if (indexIcon == null || indexIcon.getIcon() == null) {
            return buildDefaultIcon();
        }
        Icon icon = indexIcon.getIcon();
        if (icon.getIcon_1() == null) {
            icon.setIcon_1(icon_1);
        }
        if (icon.getIcon_2() == null) {
            icon.setIcon_2(icon_2);
        }
        icon.setFxIcon("fx-icon-obj-app" + (1 + index));
        return icon;
    }

    public boolean hiddenAddWithDeviceType(String apiName, String deviceType) {
        PreObject preObject = preObjectMap.get(apiName);
        if (preObject == null) {
            return false;
        }
        List<String> hideAddDeviceTypes = preObject.getHideAddDeviceTypes();
        if (CollectionUtils.isNotEmpty(hideAddDeviceTypes) && hideAddDeviceTypes.contains(deviceType)) {
            return true;
        } else {
            return false;
        }
    }

    public List<String> showApiNames() {
        return Collections.unmodifiableList(showObjectApiNames);
    }

    public List<String> getShowDeviceTypes(String apiName) {
        PreObject preObject = preObjectMap.get(apiName);
        if (preObject == null) {
            return Lists.newArrayList();
        }
        return preObject.getShowDeviceTypes();
    }

    public boolean isHiddenByApiName(String apiName) {
        PreObject preObject = preObjectMap.get(apiName);
        if (preObject == null) {
            return false;
        }
        return preObject.isHidden();
    }

    public Url getGrayUrlByApiName(String apiName) {
        PreObject preObject = preObjectMap.get(apiName);
        if (preObject != null && preObject.getGrayUrl() != null && preObject.getGrayUrl().isUseServerUrl()) {
            return preObject.getGrayUrl();
        }
        return null;
    }

    private Icon buildDefaultIcon() {
        Icon icon = new Icon();
        icon.setIcon_1(icon_1);
        icon.setIcon_2(icon_2);
        return icon;
    }

    public static void main(String[] args) {
        System.setProperty("process.profile", "fstest");
        ObjectConfig objectConfig = new ObjectConfig();
        objectConfig.init();
        Icon icon = objectConfig.getIconByApiName("PromotionObj");
        boolean filterAddWithDeviceType = objectConfig.hiddenAddWithDeviceType("111", "IOS");
        List<String> whiteApiName = objectConfig.showApiNames();
        System.out.println("---------------------------------");
        System.out.println(filterAddWithDeviceType);
        System.out.println("---------------------------------");
    }
}
