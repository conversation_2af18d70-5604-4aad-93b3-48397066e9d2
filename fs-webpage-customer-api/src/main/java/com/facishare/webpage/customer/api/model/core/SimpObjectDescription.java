package com.facishare.webpage.customer.api.model.core;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;


/**
 * Created by she<PERSON> on 19/12/10.
 */
@Data
public class SimpObjectDescription implements Serializable {

    @J<PERSON><PERSON>ield(ordinal = 1)
    private String apiName;

    @JSONField(ordinal = 2)
    private String name;

    @JSO<PERSON>ield(ordinal = 3)
    private Icon icon;

    @JSO<PERSON>ield(ordinal = 4)
    private Integer iconIndex;

    @JSONField(ordinal = 5)
    private String objectType;

    @JSONField(ordinal = 6)
    private boolean isActive;

    @JSONField(ordinal = 7)
    private boolean checkDetailObjectButton;

    @JSONField(ordinal = 8)
    private Integer iconSlot;
}
