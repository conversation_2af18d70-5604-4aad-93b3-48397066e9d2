package com.facishare.webpage.customer.api.model.core;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * Created by she<PERSON> on 19/12/10.
 */
@Data
public class TenantPrivilege implements Serializable {

    @JSONField(ordinal = 1)
    private List<String> licenseProductCodes;

    @JSONField(ordinal = 2)
    private List<String> licenseModuleCodes;

    @JSONField(ordinal = 3)
    private String appId;

    @JSONField(ordinal = 4)
    private List<String> enableObjects = Lists.newArrayList();

    @JSONField(ordinal = 5)
    private List<String> disEnableObjects = Lists.newArrayList();

    @JSONField(ordinal = 6)
    private String objectApiName;

    @JSONField(ordinal = 7)
    private Set<Integer> notSupportSource;

    @JSONField(ordinal = 8)
    private List<String> bizConfKeys;
    @JSONField(ordinal = 9)
    private List<String> objectPlugins;

    @JSONField(ordinal = 10)
    private String functionCode;
}
