package com.facishare.webpage.customer.api.model.result.apibus;

public enum ResultCode {
    OK(0, "OK"),
    UNKNOWN(-9999, "UNKNOWN ERROR");
    private int errorCode;
    private String errorMessage;

    ResultCode(int errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public static ResultCode getByCode(int errorCode) {
        for (ResultCode status : values()) {
            if (status.getErrorCode() == errorCode) {
                return status;
            }
        }
        return UNKNOWN;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }
}
