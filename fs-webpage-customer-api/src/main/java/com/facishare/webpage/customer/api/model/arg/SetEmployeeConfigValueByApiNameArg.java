package com.facishare.webpage.customer.api.model.arg;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2019/11/19
 */
@Data
public class SetEmployeeConfigValueByApiNameArg implements Serializable {

    @SerializedName(value = "apiName", alternate = {"ApiName"})
    private String apiName;
    @SerializedName(value = "key", alternate = {"Key"})
    private int key;
    @SerializedName(value = "value", alternate = {"Value"})
    private String value;
    private int tenantId;
    private int userId;
}
