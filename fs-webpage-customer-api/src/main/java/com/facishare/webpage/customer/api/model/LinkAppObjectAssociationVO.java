package com.facishare.webpage.customer.api.model;

import com.fxiaoke.crmrestapi.common.contants.PaasObjectPeopleAllocateRule;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Id;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LinkAppObjectAssociationVO implements Serializable {
    @Id
    private ObjectId id;
    /**
     * 上游ea
     */
    private String upstreamEa;
    /**
     * 对象apiName
     */
    private String objectApiName;
    /**
     * 对象apiName
     */
    private List<String> objectApiNames;
    /**
     * 企业互联应用Id
     */
    private String linkAppId;
    /**
     * 企业互联应用Id
     */
    private List<String> linkAppIds;
    /**
     * 是否允许被删除
     */
    private Boolean allowRemove = Boolean.TRUE;
    /**
     * 对象状态 0:启用  1:禁用
     */
    private int objectStatus;
    /**
     * 创建时间
     */
    private Date createTime = new Date();
    /**
     * 更新时间
     */
    private Date updateTime = new Date();
    /**
     * 不允许分配的业务类型列表
     */
    private List<String> notRecordTypeNames;
    /**
     * 不允许分配的布局列表
     */
    private List<String> notLayoutNames;
    /**
     * 对象label
     */
    private String objectLabel;
    /**
     * 是否内置
     */
    private Boolean inLay = Boolean.FALSE;
    /**
     * 是否需要分配业务类型、布局
     */
    private Boolean needAllocate = Boolean.TRUE;
    /**
     * 是否分配新建编辑页面布局
     */
    private Boolean needAllocateCreateEditLayout = Boolean.FALSE;
    /**
     * 是否分配移动端页面布局
     */
    private Boolean needAllocateMobileSummaryLayout = Boolean.FALSE;
    /**
     * 是否按业务类型分配列表布局
     */
    private Boolean needAllocateListLayoutByRecord = Boolean.FALSE;

    /**
     * 是否分配详情页页面布局
     */
    private Boolean needAllocateDetailLayout = Boolean.TRUE;

    /**
     * 1:上游企业互联应用对象  2:预置互联应用对象
     */
    private Integer type;
    private String ownerAllocateRule = PaasObjectPeopleAllocateRule.AUTO.getRule();

    private Boolean hideOwnerAllocateRule = Boolean.FALSE;

    /**
     * 开启应用分层
     */
    private Boolean supportAppLayered = Boolean.FALSE;

    // true: 是从对象
    private Boolean masterDetail = Boolean.FALSE;

}
