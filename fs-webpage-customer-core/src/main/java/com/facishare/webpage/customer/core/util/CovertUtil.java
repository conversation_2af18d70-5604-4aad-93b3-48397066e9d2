package com.facishare.webpage.customer.core.util;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.HomePageLayoutCard;
import com.facishare.webpage.customer.api.model.HomePageLayoutFilter;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Created by zhangyu on 2020/6/28
 */
public class CovertUtil {

    public static final String PS_Filter = "PS_Filter";

    private static final String RE_API_NAME = "REAPINAME";

    public static String getApiName(HomePageLayoutCard homePageLayoutCard) {

        if (PS_Filter.equals(homePageLayoutCard.getCardId())) {
            return homePageLayoutCard.getCardId() + "-" + homePageLayoutCard.getAppId() + "_" + RE_API_NAME + "_" + (long)(Math.random()*1000 * 1000 * 1000);
        }

        if (StringUtils.isNotEmpty(homePageLayoutCard.getAppId())) {
            return homePageLayoutCard.getCardId() + "-" + homePageLayoutCard.getAppId();
        } else {
            return homePageLayoutCard.getCardId();
        }
    }

    public static List<JSONObject> buildFilters(List<HomePageLayoutFilter> homePageLayoutFilters) {
        if (CollectionUtils.isEmpty(homePageLayoutFilters)) {
            return Lists.newArrayList();
        }
        List<JSONObject> filters = Lists.newArrayList();

        for (HomePageLayoutFilter homePageLayoutFilter : homePageLayoutFilters) {
            JSONObject filter = new JSONObject();
            {
                filter.put("FilterMainID", homePageLayoutFilter.getFilterMainID());
                filter.put("FilterKey", homePageLayoutFilter.getFilterKey());
                filter.put("FilterName", homePageLayoutFilter.getFilterName());
                filter.put("ObjectApiName", homePageLayoutFilter.getObjectApiName());
                filter.put("SearchApiName", homePageLayoutFilter.getSearchApiName());
            }
            filters.add(filter);
        }

        return filters;
    }

}
